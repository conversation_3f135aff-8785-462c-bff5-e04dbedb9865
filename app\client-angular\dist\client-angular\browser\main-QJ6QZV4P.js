var ED=Object.defineProperty,_D=Object.defineProperties;var CD=Object.getOwnPropertyDescriptors;var yp=Object.getOwnPropertySymbols;var wD=Object.prototype.hasOwnProperty,bD=Object.prototype.propertyIsEnumerable;var Dp=(e,t,n)=>t in e?ED(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,D=(e,t)=>{for(var n in t||={})wD.call(t,n)&&Dp(e,n,t[n]);if(yp)for(var n of yp(t))bD.call(t,n)&&Dp(e,n,t[n]);return e},G=(e,t)=>_D(e,CD(t));var cl;function fs(){return cl}function Pt(e){let t=cl;return cl=e,t}var Ep=Symbol("NotFound");function br(e){return e===Ep||e?.name==="\u0275NotFound"}function vs(e,t){return Object.is(e,t)}var Pe=null,ps=!1,ll=1,ID=null,Fe=Symbol("SIGNAL");function F(e){let t=Pe;return Pe=e,t}function ys(){return Pe}var Ir={version:0,lastCleanEpoch:0,dirty:!1,producers:void 0,producersTail:void 0,consumers:void 0,consumersTail:void 0,recomputing:!1,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function Sr(e){if(ps)throw new Error("");if(Pe===null)return;Pe.consumerOnSignalRead(e);let t=Pe.producersTail;if(t!==void 0&&t.producer===e)return;let n,r=Pe.recomputing;if(r&&(n=t!==void 0?t.nextProducer:Pe.producers,n!==void 0&&n.producer===e)){Pe.producersTail=n,n.lastReadVersion=e.version;return}let o=e.consumersTail;if(o!==void 0&&o.consumer===Pe&&(!r||TD(o,Pe)))return;let i=Mr(Pe),s={producer:e,consumer:Pe,nextProducer:n,prevConsumer:o,lastReadVersion:e.version,nextConsumer:void 0};Pe.producersTail=s,t!==void 0?t.nextProducer=s:Pe.producers=s,i&&bp(e,s)}function _p(){ll++}function Ds(e){if(!(Mr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===ll)){if(!e.producerMustRecompute(e)&&!Vo(e)){ms(e);return}e.producerRecomputeValue(e),ms(e)}}function ul(e){if(e.consumers===void 0)return;let t=ps;ps=!0;try{for(let n=e.consumers;n!==void 0;n=n.nextConsumer){let r=n.consumer;r.dirty||SD(r)}}finally{ps=t}}function dl(){return Pe?.consumerAllowSignalWrites!==!1}function SD(e){e.dirty=!0,ul(e),e.consumerMarkedDirty?.(e)}function ms(e){e.dirty=!1,e.lastCleanEpoch=ll}function Tr(e){return e&&Cp(e),F(e)}function Cp(e){e.producersTail=void 0,e.recomputing=!0}function Lo(e,t){F(t),e&&wp(e)}function wp(e){e.recomputing=!1;let t=e.producersTail,n=t!==void 0?t.nextProducer:e.producers;if(n!==void 0){if(Mr(e))do n=fl(n);while(n!==void 0);t!==void 0?t.nextProducer=void 0:e.producers=void 0}}function Vo(e){for(let t=e.producers;t!==void 0;t=t.nextProducer){let n=t.producer,r=t.lastReadVersion;if(r!==n.version||(Ds(n),r!==n.version))return!0}return!1}function jo(e){if(Mr(e)){let t=e.producers;for(;t!==void 0;)t=fl(t)}e.producers=void 0,e.producersTail=void 0,e.consumers=void 0,e.consumersTail=void 0}function bp(e,t){let n=e.consumersTail,r=Mr(e);if(n!==void 0?(t.nextConsumer=n.nextConsumer,n.nextConsumer=t):(t.nextConsumer=void 0,e.consumers=t),t.prevConsumer=n,e.consumersTail=t,!r)for(let o=e.producers;o!==void 0;o=o.nextProducer)bp(o.producer,o)}function fl(e){let t=e.producer,n=e.nextProducer,r=e.nextConsumer,o=e.prevConsumer;if(e.nextConsumer=void 0,e.prevConsumer=void 0,r!==void 0?r.prevConsumer=o:t.consumersTail=o,o!==void 0)o.nextConsumer=r;else if(t.consumers=r,!Mr(t)){let i=t.producers;for(;i!==void 0;)i=fl(i)}return n}function Mr(e){return e.consumerIsAlwaysLive||e.consumers!==void 0}function Es(e){ID?.(e)}function TD(e,t){let n=t.producersTail;if(n!==void 0){let r=t.producers;do{if(r===e)return!0;if(r===n)break;r=r.nextProducer}while(r!==void 0)}return!1}function _s(e,t){let n=Object.create(MD);n.computation=e,t!==void 0&&(n.equal=t);let r=()=>{if(Ds(n),Sr(n),n.value===Fo)throw n.error;return n.value};return r[Fe]=n,Es(n),r}var hs=Symbol("UNSET"),gs=Symbol("COMPUTING"),Fo=Symbol("ERRORED"),MD=G(D({},Ir),{value:hs,dirty:!0,error:null,equal:vs,kind:"computed",producerMustRecompute(e){return e.value===hs||e.value===gs},producerRecomputeValue(e){if(e.value===gs)throw new Error("");let t=e.value;e.value=gs;let n=Tr(e),r,o=!1;try{r=e.computation(),F(null),o=t!==hs&&t!==Fo&&r!==Fo&&e.equal(t,r)}catch(i){r=Fo,e.error=i}finally{Lo(e,n)}if(o){e.value=t;return}e.value=r,e.version++}});function AD(){throw new Error}var Ip=AD;function Sp(e){Ip(e)}function pl(e){Ip=e}var ND=null;function hl(e,t){let n=Object.create(Cs);n.value=e,t!==void 0&&(n.equal=t);let r=()=>Tp(n);return r[Fe]=n,Es(n),[r,s=>Ar(n,s),s=>gl(n,s)]}function Tp(e){return Sr(e),e.value}function Ar(e,t){dl()||Sp(e),e.equal(e.value,t)||(e.value=t,xD(e))}function gl(e,t){dl()||Sp(e),Ar(e,t(e.value))}var Cs=G(D({},Ir),{equal:vs,value:void 0,kind:"signal"});function xD(e){e.version++,_p(),ul(e),ND?.(e)}function j(e){return typeof e=="function"}function Nr(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var ws=Nr(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Bo(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var de=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(j(r))try{r()}catch(i){t=i instanceof ws?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Mp(i)}catch(s){t=t??[],s instanceof ws?t=[...t,...s.errors]:t.push(s)}}if(t)throw new ws(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Mp(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Bo(n,t)}remove(t){let{_finalizers:n}=this;n&&Bo(n,t),t instanceof e&&t._removeParent(this)}};de.EMPTY=(()=>{let e=new de;return e.closed=!0,e})();var ml=de.EMPTY;function bs(e){return e instanceof de||e&&"closed"in e&&j(e.remove)&&j(e.add)&&j(e.unsubscribe)}function Mp(e){j(e)?e():e.unsubscribe()}var _t={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var xr={setTimeout(e,t,...n){let{delegate:r}=xr;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=xr;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Is(e){xr.setTimeout(()=>{let{onUnhandledError:t}=_t;if(t)t(e);else throw e})}function Uo(){}var Ap=vl("C",void 0,void 0);function Np(e){return vl("E",void 0,e)}function xp(e){return vl("N",e,void 0)}function vl(e,t,n){return{kind:e,value:t,error:n}}var Wn=null;function Rr(e){if(_t.useDeprecatedSynchronousErrorHandling){let t=!Wn;if(t&&(Wn={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Wn;if(Wn=null,n)throw r}}else e()}function Rp(e){_t.useDeprecatedSynchronousErrorHandling&&Wn&&(Wn.errorThrown=!0,Wn.error=e)}var qn=class extends de{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,bs(t)&&t.add(this)):this.destination=kD}static create(t,n,r){return new Or(t,n,r)}next(t){this.isStopped?Dl(xp(t),this):this._next(t)}error(t){this.isStopped?Dl(Np(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Dl(Ap,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},RD=Function.prototype.bind;function yl(e,t){return RD.call(e,t)}var El=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Ss(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Ss(r)}else Ss(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Ss(n)}}},Or=class extends qn{constructor(t,n,r){super();let o;if(j(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&_t.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&yl(t.next,i),error:t.error&&yl(t.error,i),complete:t.complete&&yl(t.complete,i)}):o=t}this.destination=new El(o)}};function Ss(e){_t.useDeprecatedSynchronousErrorHandling?Rp(e):Is(e)}function OD(e){throw e}function Dl(e,t){let{onStoppedNotification:n}=_t;n&&xr.setTimeout(()=>n(e,t))}var kD={closed:!0,next:Uo,error:OD,complete:Uo};var kr=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Je(e){return e}function _l(...e){return Cl(e)}function Cl(e){return e.length===0?Je:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var z=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=FD(n)?n:new Or(n,r,o);return Rr(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Op(r),new r((o,i)=>{let s=new Or({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[kr](){return this}pipe(...n){return Cl(n)(this)}toPromise(n){return n=Op(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Op(e){var t;return(t=e??_t.Promise)!==null&&t!==void 0?t:Promise}function PD(e){return e&&j(e.next)&&j(e.error)&&j(e.complete)}function FD(e){return e&&e instanceof qn||PD(e)&&bs(e)}function wl(e){return j(e?.lift)}function q(e){return t=>{if(wl(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function W(e,t,n,r,o){return new bl(e,t,n,r,o)}var bl=class extends qn{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Pr(){return q((e,t)=>{let n=null;e._refCount++;let r=W(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Fr=class extends z{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,wl(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new de;let n=this.getSubject();t.add(this.source.subscribe(W(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=de.EMPTY)}return t}refCount(){return Pr()(this)}};var kp=Nr(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ce=(()=>{class e extends z{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Ts(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new kp}next(n){Rr(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Rr(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Rr(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?ml:(this.currentObservers=null,i.push(n),new de(()=>{this.currentObservers=null,Bo(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new z;return n.source=this,n}}return e.create=(t,n)=>new Ts(t,n),e})(),Ts=class extends ce{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:ml}};var Ee=class extends ce{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var We=new z(e=>e.complete());function Pp(e){return e&&j(e.schedule)}function Fp(e){return e[e.length-1]}function Ms(e){return j(Fp(e))?e.pop():void 0}function Dn(e){return Pp(Fp(e))?e.pop():void 0}function Vp(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(d){s(d)}}function c(u){try{l(r.throw(u))}catch(d){s(d)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function Lp(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Zn(e){return this instanceof Zn?(this.v=e,this):new Zn(e)}function jp(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(g){return Promise.resolve(g).then(f,d)}}function a(f,g){r[f]&&(o[f]=function(S){return new Promise(function(C,E){i.push([f,S,C,E])>1||c(f,S)})},g&&(o[f]=g(o[f])))}function c(f,g){try{l(r[f](g))}catch(S){p(i[0][3],S)}}function l(f){f.value instanceof Zn?Promise.resolve(f.value.v).then(u,d):p(i[0][2],f)}function u(f){c("next",f)}function d(f){c("throw",f)}function p(f,g){f(g),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Bp(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Lp=="function"?Lp(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var As=e=>e&&typeof e.length=="number"&&typeof e!="function";function Ns(e){return j(e?.then)}function xs(e){return j(e[kr])}function Rs(e){return Symbol.asyncIterator&&j(e?.[Symbol.asyncIterator])}function Os(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function LD(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var ks=LD();function Ps(e){return j(e?.[ks])}function Fs(e){return jp(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Zn(n.read());if(o)return yield Zn(void 0);yield yield Zn(r)}}finally{n.releaseLock()}})}function Ls(e){return j(e?.getReader)}function fe(e){if(e instanceof z)return e;if(e!=null){if(xs(e))return VD(e);if(As(e))return jD(e);if(Ns(e))return BD(e);if(Rs(e))return Up(e);if(Ps(e))return UD(e);if(Ls(e))return HD(e)}throw Os(e)}function VD(e){return new z(t=>{let n=e[kr]();if(j(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function jD(e){return new z(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function BD(e){return new z(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Is)})}function UD(e){return new z(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Up(e){return new z(t=>{$D(e,t).catch(n=>t.error(n))})}function HD(e){return Up(Fs(e))}function $D(e,t){var n,r,o,i;return Vp(this,void 0,void 0,function*(){try{for(n=Bp(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function qe(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Vs(e,t=0){return q((n,r)=>{n.subscribe(W(r,o=>qe(r,e,()=>r.next(o),t),()=>qe(r,e,()=>r.complete(),t),o=>qe(r,e,()=>r.error(o),t)))})}function js(e,t=0){return q((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Hp(e,t){return fe(e).pipe(js(t),Vs(t))}function $p(e,t){return fe(e).pipe(js(t),Vs(t))}function zp(e,t){return new z(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Gp(e,t){return new z(n=>{let r;return qe(n,t,()=>{r=e[ks](),qe(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>j(r?.return)&&r.return()})}function Bs(e,t){if(!e)throw new Error("Iterable cannot be null");return new z(n=>{qe(n,t,()=>{let r=e[Symbol.asyncIterator]();qe(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Wp(e,t){return Bs(Fs(e),t)}function qp(e,t){if(e!=null){if(xs(e))return Hp(e,t);if(As(e))return zp(e,t);if(Ns(e))return $p(e,t);if(Rs(e))return Bs(e,t);if(Ps(e))return Gp(e,t);if(Ls(e))return Wp(e,t)}throw Os(e)}function ae(e,t){return t?qp(e,t):fe(e)}function P(...e){let t=Dn(e);return ae(e,t)}function Lr(e,t){let n=j(e)?e:()=>e,r=o=>o.error(n());return new z(t?o=>t.schedule(r,0,o):r)}function Il(e){return!!e&&(e instanceof z||j(e.lift)&&j(e.subscribe))}var en=Nr(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function O(e,t){return q((n,r)=>{let o=0;n.subscribe(W(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:zD}=Array;function GD(e,t){return zD(t)?e(...t):e(t)}function Us(e){return O(t=>GD(e,t))}var{isArray:WD}=Array,{getPrototypeOf:qD,prototype:ZD,keys:YD}=Object;function Hs(e){if(e.length===1){let t=e[0];if(WD(t))return{args:t,keys:null};if(QD(t)){let n=YD(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function QD(e){return e&&typeof e=="object"&&qD(e)===ZD}function $s(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function zs(...e){let t=Dn(e),n=Ms(e),{args:r,keys:o}=Hs(e);if(r.length===0)return ae([],t);let i=new z(KD(r,t,o?s=>$s(o,s):Je));return n?i.pipe(Us(n)):i}function KD(e,t,n=Je){return r=>{Zp(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Zp(t,()=>{let l=ae(e[c],t),u=!1;l.subscribe(W(r,d=>{i[c]=d,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Zp(e,t,n){e?qe(n,e,t):t()}function Yp(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,d=!1,p=()=>{d&&!c.length&&!l&&t.complete()},f=S=>l<r?g(S):c.push(S),g=S=>{i&&t.next(S),l++;let C=!1;fe(n(S,u++)).subscribe(W(t,E=>{o?.(E),i?f(E):t.next(E)},()=>{C=!0},void 0,()=>{if(C)try{for(l--;c.length&&l<r;){let E=c.shift();s?qe(t,s,()=>g(E)):g(E)}p()}catch(E){t.error(E)}}))};return e.subscribe(W(t,f,()=>{d=!0,p()})),()=>{a?.()}}function _e(e,t,n=1/0){return j(t)?_e((r,o)=>O((i,s)=>t(r,i,o,s))(fe(e(r,o))),n):(typeof t=="number"&&(n=t),q((r,o)=>Yp(r,o,e,n)))}function Qp(e=1/0){return _e(Je,e)}function Kp(){return Qp(1)}function Vr(...e){return Kp()(ae(e,Dn(e)))}function Ho(e){return new z(t=>{fe(e()).subscribe(t)})}function Sl(...e){let t=Ms(e),{args:n,keys:r}=Hs(e),o=new z(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let d=!1;fe(n[u]).subscribe(W(i,p=>{d||(d=!0,l--),a[u]=p},()=>c--,void 0,()=>{(!c||!d)&&(l||i.next(r?$s(r,a):a),i.complete())}))}});return t?o.pipe(Us(t)):o}function Ze(e,t){return q((n,r)=>{let o=0;n.subscribe(W(r,i=>e.call(t,i,o++)&&r.next(i)))})}function En(e){return q((t,n)=>{let r=null,o=!1,i;r=t.subscribe(W(n,void 0,void 0,s=>{i=fe(e(s,En(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Jp(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(W(s,u=>{let d=l++;c=a?e(c,u,d):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function _n(e,t){return j(t)?_e(e,t,1):_e(e,1)}function Cn(e){return q((t,n)=>{let r=!1;t.subscribe(W(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function tn(e){return e<=0?()=>We:q((t,n)=>{let r=0;t.subscribe(W(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Gs(e=JD){return q((t,n)=>{let r=!1;t.subscribe(W(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function JD(){return new en}function Yn(e){return q((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function nn(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ze((o,i)=>e(o,i,r)):Je,tn(1),n?Cn(t):Gs(()=>new en))}function jr(e){return e<=0?()=>We:q((t,n)=>{let r=[];t.subscribe(W(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Tl(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ze((o,i)=>e(o,i,r)):Je,jr(1),n?Cn(t):Gs(()=>new en))}function Ml(e,t){return q(Jp(e,t,arguments.length>=2,!0))}function Al(...e){let t=Dn(e);return q((n,r)=>{(t?Vr(e,n,t):Vr(e,n)).subscribe(r)})}function Le(e,t){return q((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(W(r,c=>{o?.unsubscribe();let l=0,u=i++;fe(e(c,u)).subscribe(o=W(r,d=>r.next(t?t(c,d,u,l++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function Ws(e){return q((t,n)=>{fe(e).subscribe(W(n,()=>n.complete(),Uo)),!n.closed&&t.subscribe(n)})}function Ie(e,t,n){let r=j(e)||t||n?{next:e,error:t,complete:n}:e;return r?q((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(W(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):Je}function Xp(e){let t=F(null);try{return e()}finally{F(t)}}var Qs="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",_=class extends Error{code;constructor(t,n){super(Wo(t,n)),this.code=t}};function XD(e){return`NG0${Math.abs(e)}`}function Wo(e,t){return`${XD(e)}${t?": "+t:""}`}var tr=globalThis;function K(e){for(let t in e)if(e[t]===K)return t;throw Error("")}function nh(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function on(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(on).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function Ul(e,t){return e?t?`${e} ${t}`:e:t||""}var eE=K({__forward_ref__:K});function tt(e){return e.__forward_ref__=tt,e.toString=function(){return on(this())},e}function xe(e){return Hl(e)?e():e}function Hl(e){return typeof e=="function"&&e.hasOwnProperty(eE)&&e.__forward_ref__===tt}function I(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function wt(e){return{providers:e.providers||[],imports:e.imports||[]}}function qo(e){return tE(e,Ks)}function $l(e){return qo(e)!==null}function tE(e,t){return e.hasOwnProperty(t)&&e[t]||null}function nE(e){let t=e?.[Ks]??null;return t||null}function xl(e){return e&&e.hasOwnProperty(Zs)?e[Zs]:null}var Ks=K({\u0275prov:K}),Zs=K({\u0275inj:K}),b=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=I({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function zl(e){return e&&!!e.\u0275providers}var Gl=K({\u0275cmp:K}),Wl=K({\u0275dir:K}),ql=K({\u0275pipe:K}),Zl=K({\u0275mod:K}),zo=K({\u0275fac:K}),nr=K({__NG_ELEMENT_ID__:K}),eh=K({__NG_ENV_ID__:K});function bn(e){return typeof e=="string"?e:e==null?"":String(e)}function rh(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():bn(e)}var oh=K({ngErrorCode:K}),rE=K({ngErrorMessage:K}),oE=K({ngTokenPath:K});function Yl(e,t){return ih("",-200,t)}function Js(e,t){throw new _(-201,!1)}function ih(e,t,n){let r=new _(t,e);return r[oh]=t,r[rE]=e,n&&(r[oE]=n),r}function iE(e){return e[oh]}var Rl;function sh(){return Rl}function Xe(e){let t=Rl;return Rl=e,t}function Ql(e,t,n){let r=qo(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&8)return null;if(t!==void 0)return t;Js(e,"Injector")}var sE={},Qn=sE,aE="__NG_DI_FLAG__",Ol=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=Kn(n)||0;try{return this.injector.get(t,r&8?null:Qn,r)}catch(o){if(br(o))return o;throw o}}};function cE(e,t=0){let n=fs();if(n===void 0)throw new _(-203,!1);if(n===null)return Ql(e,void 0,t);{let r=lE(t),o=n.retrieve(e,r);if(br(o)){if(r.optional)return null;throw o}return o}}function M(e,t=0){return(sh()||cE)(xe(e),t)}function h(e,t){return M(e,Kn(t))}function Kn(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function lE(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function kl(e){let t=[];for(let n=0;n<e.length;n++){let r=xe(e[n]);if(Array.isArray(r)){if(r.length===0)throw new _(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=uE(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(M(o,i))}else t.push(M(r))}return t}function uE(e){return e[aE]}function Jn(e,t){let n=e.hasOwnProperty(zo);return n?e[zo]:null}function ah(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function ch(e){return e.flat(Number.POSITIVE_INFINITY)}function Xs(e,t){e.forEach(n=>Array.isArray(n)?Xs(n,t):t(n))}function Kl(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Zo(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function lh(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function uh(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function dh(e,t,n){let r=Ur(e,t);return r>=0?e[r|1]=n:(r=~r,uh(e,r,t,n)),r}function ea(e,t){let n=Ur(e,t);if(n>=0)return e[n|1]}function Ur(e,t){return dE(e,t,1)}function dE(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var In={},et=[],an=new b(""),Jl=new b("",-1),Xl=new b(""),Go=class{get(t,n=Qn){if(n===Qn){let o=ih("",-201);throw o.name="\u0275NotFound",o}return n}};function eu(e){return e[Zl]||null}function Sn(e){return e[Gl]||null}function tu(e){return e[Wl]||null}function fh(e){return e[ql]||null}function cn(e){return{\u0275providers:e}}function ph(e){return cn([{provide:an,multi:!0,useValue:e}])}function hh(...e){return{\u0275providers:nu(!0,e),\u0275fromNgModule:!0}}function nu(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Xs(t,s=>{let a=s;Ys(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&gh(o,i),n}function gh(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];ru(o,i=>{t(i,r)})}}function Ys(e,t,n,r){if(e=xe(e),!e)return!1;let o=null,i=xl(e),s=!i&&Sn(e);if(!i&&!s){let c=e.ngModule;if(i=xl(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Ys(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{Xs(i.imports,u=>{Ys(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&gh(l,t)}if(!a){let l=Jn(o)||(()=>new o);t({provide:o,useFactory:l,deps:et},o),t({provide:Xl,useValue:o,multi:!0},o),t({provide:an,useValue:()=>M(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;ru(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function ru(e,t){for(let n of e)zl(n)&&(n=n.\u0275providers),Array.isArray(n)?ru(n,t):t(n)}var fE=K({provide:String,useValue:K});function mh(e){return e!==null&&typeof e=="object"&&fE in e}function pE(e){return!!(e&&e.useExisting)}function hE(e){return!!(e&&e.useFactory)}function Xn(e){return typeof e=="function"}function vh(e){return!!e.useClass}var Yo=new b(""),qs={},th={},Nl;function Qo(){return Nl===void 0&&(Nl=new Go),Nl}var pe=class{},er=class extends pe{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Fl(t,s=>this.processProvider(s)),this.records.set(Jl,Br(void 0,this)),o.has("environment")&&this.records.set(pe,Br(void 0,this));let i=this.records.get(Yo);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Xl,et,{self:!0}))}retrieve(t,n){let r=Kn(n)||0;try{return this.get(t,Qn,r)}catch(o){if(br(o))return o;throw o}}destroy(){$o(this),this._destroyed=!0;let t=F(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),F(t)}}onDestroy(t){return $o(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){$o(this);let n=Pt(this),r=Xe(void 0),o;try{return t()}finally{Pt(n),Xe(r)}}get(t,n=Qn,r){if($o(this),t.hasOwnProperty(eh))return t[eh](this);let o=Kn(r),i,s=Pt(this),a=Xe(void 0);try{if(!(o&4)){let l=this.records.get(t);if(l===void 0){let u=DE(t)&&qo(t);u&&this.injectableDefInScope(u)?l=Br(Pl(t),qs):l=null,this.records.set(t,l)}if(l!=null)return this.hydrate(t,l,o)}let c=o&2?Qo():this.parent;return n=o&8&&n===Qn?null:n,c.get(t,n)}catch(c){let l=iE(c);throw l===-200||l===-201?new _(l,null):c}finally{Xe(a),Pt(s)}}resolveInjectorInitializers(){let t=F(null),n=Pt(this),r=Xe(void 0),o;try{let i=this.get(an,et,{self:!0});for(let s of i)s()}finally{Pt(n),Xe(r),F(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(on(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=xe(t);let n=Xn(t)?t:xe(t&&t.provide),r=mE(t);if(!Xn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Br(void 0,qs,!0),o.factory=()=>kl(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n,r){let o=F(null);try{if(n.value===th)throw Yl(on(t));return n.value===qs&&(n.value=th,n.value=n.factory(void 0,r)),typeof n.value=="object"&&n.value&&yE(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{F(o)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=xe(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Pl(e){let t=qo(e),n=t!==null?t.factory:Jn(e);if(n!==null)return n;if(e instanceof b)throw new _(204,!1);if(e instanceof Function)return gE(e);throw new _(204,!1)}function gE(e){if(e.length>0)throw new _(204,!1);let n=nE(e);return n!==null?()=>n.factory(e):()=>new e}function mE(e){if(mh(e))return Br(void 0,e.useValue);{let t=ou(e);return Br(t,qs)}}function ou(e,t,n){let r;if(Xn(e)){let o=xe(e);return Jn(o)||Pl(o)}else if(mh(e))r=()=>xe(e.useValue);else if(hE(e))r=()=>e.useFactory(...kl(e.deps||[]));else if(pE(e))r=(o,i)=>M(xe(e.useExisting),i!==void 0&&i&8?8:void 0);else{let o=xe(e&&(e.useClass||e.provide));if(vE(e))r=()=>new o(...kl(e.deps));else return Jn(o)||Pl(o)}return r}function $o(e){if(e.destroyed)throw new _(205,!1)}function Br(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function vE(e){return!!e.deps}function yE(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function DE(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Fl(e,t){for(let n of e)Array.isArray(n)?Fl(n,t):n&&zl(n)?Fl(n.\u0275providers,t):t(n)}function Re(e,t){let n;e instanceof er?($o(e),n=e):n=new Ol(e);let r,o=Pt(n),i=Xe(void 0);try{return t()}finally{Pt(o),Xe(i)}}function yh(){return sh()!==void 0||fs()!=null}var bt=0,A=1,L=2,Ce=3,dt=4,Ue=5,rr=6,Hr=7,le=8,$r=9,Lt=10,te=11,zr=12,iu=13,or=14,He=15,Tn=16,ir=17,Vt=18,Ko=19,su=20,rn=21,ta=22,Jo=23,nt=24,na=25,Oe=26,he=27,Dh=1,au=6,Mn=7,Xo=8,sr=9,ge=10;function jt(e){return Array.isArray(e)&&typeof e[Dh]=="object"}function It(e){return Array.isArray(e)&&e[Dh]===!0}function cu(e){return(e.flags&4)!==0}function An(e){return e.componentOffset>-1}function ei(e){return(e.flags&1)===1}function Bt(e){return!!e.template}function Gr(e){return(e[L]&512)!==0}function ar(e){return(e[L]&256)===256}var lu="svg",Eh="math";function ft(e){for(;Array.isArray(e);)e=e[bt];return e}function uu(e,t){return ft(t[e])}function St(e,t){return ft(t[e.index])}function ti(e,t){return e.data[t]}function pt(e,t){let n=t[e];return jt(n)?n:n[bt]}function _h(e){return(e[L]&4)===4}function ra(e){return(e[L]&128)===128}function Ch(e){return It(e[Ce])}function ht(e,t){return t==null?null:e[t]}function du(e){e[ir]=0}function fu(e){e[L]&1024||(e[L]|=1024,ra(e)&&ri(e))}function wh(e,t){for(;e>0;)t=t[or],e--;return t}function ni(e){return!!(e[L]&9216||e[nt]?.dirty)}function oa(e){e[Lt].changeDetectionScheduler?.notify(8),e[L]&64&&(e[L]|=1024),ni(e)&&ri(e)}function ri(e){e[Lt].changeDetectionScheduler?.notify(0);let t=wn(e);for(;t!==null&&!(t[L]&8192||(t[L]|=8192,!ra(t)));)t=wn(t)}function pu(e,t){if(ar(e))throw new _(911,!1);e[rn]===null&&(e[rn]=[]),e[rn].push(t)}function bh(e,t){if(e[rn]===null)return;let n=e[rn].indexOf(t);n!==-1&&e[rn].splice(n,1)}function wn(e){let t=e[Ce];return It(t)?t[Ce]:t}function hu(e){return e[Hr]??=[]}function gu(e){return e.cleanup??=[]}function Ih(e,t,n,r){let o=hu(t);o.push(n),e.firstCreatePass&&gu(e).push(r,o.length-1)}var $={lFrame:Fh(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Ll=!1;function Sh(){return $.lFrame.elementDepthCount}function Th(){$.lFrame.elementDepthCount++}function mu(){$.lFrame.elementDepthCount--}function vu(){return $.bindingsEnabled}function yu(){return $.skipHydrationRootTNode!==null}function Du(e){return $.skipHydrationRootTNode===e}function Eu(){$.skipHydrationRootTNode=null}function B(){return $.lFrame.lView}function ue(){return $.lFrame.tView}function N(e){return $.lFrame.contextLView=e,e[le]}function x(e){return $.lFrame.contextLView=null,e}function ke(){let e=_u();for(;e!==null&&e.type===64;)e=e.parent;return e}function _u(){return $.lFrame.currentTNode}function Mh(){let e=$.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Wr(e,t){let n=$.lFrame;n.currentTNode=e,n.isParent=t}function Cu(){return $.lFrame.isParent}function wu(){$.lFrame.isParent=!1}function bu(){return Ll}function Iu(e){let t=Ll;return Ll=e,t}function Su(){let e=$.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Ah(){return $.lFrame.bindingIndex}function Nh(e){return $.lFrame.bindingIndex=e}function Nn(){return $.lFrame.bindingIndex++}function Tu(e){let t=$.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function xh(){return $.lFrame.inI18n}function Rh(e,t){let n=$.lFrame;n.bindingIndex=n.bindingRootIndex=e,ia(t)}function Oh(){return $.lFrame.currentDirectiveIndex}function ia(e){$.lFrame.currentDirectiveIndex=e}function kh(e){let t=$.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Mu(){return $.lFrame.currentQueryIndex}function sa(e){$.lFrame.currentQueryIndex=e}function EE(e){let t=e[A];return t.type===2?t.declTNode:t.type===1?e[Ue]:null}function Au(e,t,n){if(n&4){let o=t,i=e;for(;o=o.parent,o===null&&!(n&1);)if(o=EE(i),o===null||(i=i[or],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=$.lFrame=Ph();return r.currentTNode=t,r.lView=e,!0}function aa(e){let t=Ph(),n=e[A];$.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Ph(){let e=$.lFrame,t=e===null?null:e.child;return t===null?Fh(e):t}function Fh(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Lh(){let e=$.lFrame;return $.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Nu=Lh;function ca(){let e=Lh();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Vh(e){return($.lFrame.contextLView=wh(e,$.lFrame.contextLView))[le]}function ln(){return $.lFrame.selectedIndex}function xn(e){$.lFrame.selectedIndex=e}function oi(){let e=$.lFrame;return ti(e.tView,e.selectedIndex)}function la(){$.lFrame.currentNamespace=lu}function jh(){return $.lFrame.currentNamespace}var Bh=!0;function ua(){return Bh}function da(e){Bh=e}function Vl(e,t=null,n=null,r){let o=xu(e,t,n,r);return o.resolveInjectorInitializers(),o}function xu(e,t=null,n=null,r,o=new Set){let i=[n||et,hh(e)];return r=r||(typeof e=="object"?void 0:on(e)),new er(i,t||Qo(),r||null,o)}var Ct=class e{static THROW_IF_NOT_FOUND=Qn;static NULL=new Go;static create(t,n){if(Array.isArray(t))return Vl({name:""},n,t,"");{let r=t.name??"";return Vl({name:r},t.parent,t.providers,r)}}static \u0275prov=I({token:e,providedIn:"any",factory:()=>M(Jl)});static __NG_ELEMENT_ID__=-1},me=new b(""),Ut=(()=>{class e{static __NG_ELEMENT_ID__=_E;static __NG_ENV_ID__=n=>n}return e})(),jl=class extends Ut{_lView;constructor(t){super(),this._lView=t}get destroyed(){return ar(this._lView)}onDestroy(t){let n=this._lView;return pu(n,t),()=>bh(n,t)}};function _E(){return new jl(B())}var Ft=class{_console=console;handleError(t){this._console.error("ERROR",t)}},$e=new b("",{providedIn:"root",factory:()=>{let e=h(pe),t;return n=>{e.destroyed&&!t?setTimeout(()=>{throw n}):(t??=e.get(Ft),t.handleError(n))}}}),Uh={provide:an,useValue:()=>void h(Ft),multi:!0},CE=new b("",{providedIn:"root",factory:()=>{let e=h(me).defaultView;if(!e)return;let t=h($e),n=i=>{t(i.reason),i.preventDefault()},r=i=>{i.error?t(i.error):t(new Error(i.message,{cause:i})),i.preventDefault()},o=()=>{e.addEventListener("unhandledrejection",n),e.addEventListener("error",r)};typeof Zone<"u"?Zone.root.run(o):o(),h(Ut).onDestroy(()=>{e.removeEventListener("error",r),e.removeEventListener("unhandledrejection",n)})}});function Ru(){return cn([ph(()=>void h(CE))])}function Ou(e){return typeof e=="function"&&e[Fe]!==void 0}function Tt(e,t){let[n,r,o]=hl(e,t?.equal),i=n,s=i[Fe];return i.set=r,i.update=o,i.asReadonly=Hh.bind(i),i}function Hh(){let e=this[Fe];if(e.readonlyFn===void 0){let t=()=>this();t[Fe]=e,e.readonlyFn=t}return e.readonlyFn}function ku(e){return Ou(e)&&typeof e.set=="function"}var sn=class{},ii=new b("",{providedIn:"root",factory:()=>!1});var Pu=new b(""),fa=new b("");var Ht=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new Ee(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new z(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=I({token:e,providedIn:"root",factory:()=>new e})}return e})(),pa=(()=>{class e{internalPendingTasks=h(Ht);scheduler=h(sn);errorHandler=h($e);add(){let n=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(n)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(n))}}run(n){let r=this.add();n().catch(this.errorHandler).finally(r)}static \u0275prov=I({token:e,providedIn:"root",factory:()=>new e})}return e})();function si(...e){}var Fu=(()=>{class e{static \u0275prov=I({token:e,providedIn:"root",factory:()=>new Bl})}return e})(),Bl=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||r.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,r]of this.queues)n===null?t||=this.flushQueue(r):t||=n.run(()=>this.flushQueue(r));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let r of t)r.dirty&&(this.dirtyEffectCount--,n=!0,r.run());return n}};function mi(e){return{toString:e}.toString()}function xE(e){return typeof e=="function"}var wa=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function bg(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var rt=(()=>{let e=()=>Ig;return e.ngInherit=!0,e})();function Ig(e){return e.type.prototype.ngOnChanges&&(e.setInput=OE),RE}function RE(){let e=Tg(this),t=e?.current;if(t){let n=e.previous;if(n===In)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function OE(e,t,n,r,o){let i=this.declaredInputs[r],s=Tg(e)||kE(e,{previous:In,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new wa(l&&l.currentValue,n,c===In),bg(e,t,o,n)}var Sg="__ngSimpleChanges__";function Tg(e){return e[Sg]||null}function kE(e,t){return e[Sg]=t}var $h=[];var X=function(e,t=null,n){for(let r=0;r<$h.length;r++){let o=$h[r];o(e,t,n)}};function PE(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Ig(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function Mg(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function ya(e,t,n){Ag(e,t,3,n)}function Da(e,t,n,r){(e[L]&3)===n&&Ag(e,t,n,r)}function Lu(e,t){let n=e[L];(n&3)===t&&(n&=16383,n+=1,e[L]=n)}function Ag(e,t,n,r){let o=r!==void 0?e[ir]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[ir]+=65536),(a<i||i==-1)&&(FE(e,n,t,c),e[ir]=(e[ir]&**********)+c+2),c++}function zh(e,t){X(4,e,t);let n=F(null);try{t.call(e)}finally{F(n),X(5,e,t)}}function FE(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[L]>>14<e[ir]>>16&&(e[L]&3)===t&&(e[L]+=16384,zh(a,i)):zh(a,i)}var Zr=-1,lr=class{factory;name;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r,o){this.factory=t,this.name=o,this.canSeeViewProviders=n,this.injectImpl=r}};function LE(e){return(e.flags&8)!==0}function VE(e){return(e.flags&16)!==0}function jE(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];BE(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Ng(e){return e===3||e===4||e===6}function BE(e){return e.charCodeAt(0)===64}function Yr(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Gh(e,n,o,null,t[++r]):Gh(e,n,o,null,null))}}return e}function Gh(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function xg(e){return e!==Zr}function ba(e){return e&32767}function UE(e){return e>>16}function Ia(e,t){let n=UE(e),r=t;for(;n>0;)r=r[or],n--;return r}var qu=!0;function Wh(e){let t=qu;return qu=e,t}var HE=256,Rg=HE-1,Og=5,$E=0,$t={};function zE(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(nr)&&(r=n[nr]),r==null&&(r=n[nr]=$E++);let o=r&Rg,i=1<<o;t.data[e+(o>>Og)]|=i}function Sa(e,t){let n=kg(e,t);if(n!==-1)return n;let r=t[A];r.firstCreatePass&&(e.injectorIndex=t.length,Vu(r.data,e),Vu(t,null),Vu(r.blueprint,null));let o=Sd(e,t),i=e.injectorIndex;if(xg(o)){let s=ba(o),a=Ia(o,t),c=a[A].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function Vu(e,t){e.push(0,0,0,0,0,0,0,0,t)}function kg(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Sd(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=jg(o),r===null)return Zr;if(n++,o=o[or],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Zr}function Zu(e,t,n){zE(e,t,n)}function GE(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Ng(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function Pg(e,t,n){if(n&8||e!==void 0)return e;Js(t,"NodeInjector")}function Fg(e,t,n,r){if(n&8&&r===void 0&&(r=null),(n&3)===0){let o=e[$r],i=Xe(void 0);try{return o?o.get(t,r,n&8):Ql(t,r,n&8)}finally{Xe(i)}}return Pg(r,t,n)}function Lg(e,t,n,r=0,o){if(e!==null){if(t[L]&2048&&!(r&2)){let s=YE(e,t,n,r,$t);if(s!==$t)return s}let i=Vg(e,t,n,r,$t);if(i!==$t)return i}return Fg(t,n,r,o)}function Vg(e,t,n,r,o){let i=qE(n);if(typeof i=="function"){if(!Au(t,e,r))return r&1?Pg(o,n,r):Fg(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&8))Js(n);else return s}finally{Nu()}}else if(typeof i=="number"){let s=null,a=kg(e,t),c=Zr,l=r&1?t[He][Ue]:null;for((a===-1||r&4)&&(c=a===-1?Sd(e,t):t[a+8],c===Zr||!Zh(r,!1)?a=-1:(s=t[A],a=ba(c),t=Ia(c,t)));a!==-1;){let u=t[A];if(qh(i,a,u.data)){let d=WE(a,t,n,s,r,l);if(d!==$t)return d}c=t[a+8],c!==Zr&&Zh(r,t[A].data[a+8]===l)&&qh(i,a,t)?(s=u,a=ba(c),t=Ia(c,t)):a=-1}}return o}function WE(e,t,n,r,o,i){let s=t[A],a=s.data[e+8],c=r==null?An(a)&&qu:r!=s&&(a.type&3)!==0,l=o&1&&i===a,u=Ea(a,s,n,c,l);return u!==null?li(t,s,u,a,o):$t}function Ea(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,d=r?a:a+u,p=o?a+u:l;for(let f=d;f<p;f++){let g=s[f];if(f<c&&n===g||f>=c&&g.type===n)return f}if(o){let f=s[c];if(f&&Bt(f)&&f.type===n)return c}return null}function li(e,t,n,r,o){let i=e[n],s=t.data;if(i instanceof lr){let a=i;if(a.resolving){let f=rh(s[n]);throw Yl(f)}let c=Wh(a.canSeeViewProviders);a.resolving=!0;let l=s[n].type||s[n],u,d=a.injectImpl?Xe(a.injectImpl):null,p=Au(e,r,0);try{i=e[n]=a.factory(void 0,o,s,e,r),t.firstCreatePass&&n>=r.directiveStart&&PE(n,s[n],t)}finally{d!==null&&Xe(d),Wh(c),a.resolving=!1,Nu()}}return i}function qE(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(nr)?e[nr]:void 0;return typeof t=="number"?t>=0?t&Rg:ZE:t}function qh(e,t,n){let r=1<<e;return!!(n[t+(e>>Og)]&r)}function Zh(e,t){return!(e&2)&&!(e&1&&t)}var cr=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Lg(this._tNode,this._lView,t,Kn(r),n)}};function ZE(){return new cr(ke(),B())}function hr(e){return mi(()=>{let t=e.prototype.constructor,n=t[zo]||Yu(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[zo]||Yu(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Yu(e){return Hl(e)?()=>{let t=Yu(xe(e));return t&&t()}:Jn(e)}function YE(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[L]&2048&&!Gr(s);){let a=Vg(i,s,n,r|2,$t);if(a!==$t)return a;let c=i.parent;if(!c){let l=s[su];if(l){let u=l.get(n,$t,r);if(u!==$t)return u}c=jg(s),s=s[or]}i=c}return o}function jg(e){let t=e[A],n=t.type;return n===2?t.declTNode:n===1?e[Ue]:null}function vi(e){return GE(ke(),e)}function QE(){return eo(ke(),B())}function eo(e,t){return new ot(St(e,t))}var ot=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=QE}return e})();function KE(e){return e instanceof ot?e.nativeElement:e}function JE(){return this._results[Symbol.iterator]()}var Ta=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new ce}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=ch(t);(this._changesDetected=!ah(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=JE};function Bg(e){return(e.flags&128)===128}var Td=(function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e})(Td||{}),Ug=new Map,XE=0;function e_(){return XE++}function t_(e){Ug.set(e[Ko],e)}function Qu(e){Ug.delete(e[Ko])}var Yh="__ngContext__";function Qr(e,t){jt(t)?(e[Yh]=t[Ko],t_(t)):e[Yh]=t}function Hg(e){return zg(e[zr])}function $g(e){return zg(e[dt])}function zg(e){for(;e!==null&&!It(e);)e=e[dt];return e}var Ku;function Md(e){Ku=e}function Ad(){if(Ku!==void 0)return Ku;if(typeof document<"u")return document;throw new _(210,!1)}var Ua=new b("",{providedIn:"root",factory:()=>n_}),n_="ng",Ha=new b(""),to=new b("",{providedIn:"platform",factory:()=>"unknown"});var $a=new b("",{providedIn:"root",factory:()=>Ad().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var r_="h",o_="b";var Gg="r";var Wg="di";var qg=!1,Zg=new b("",{providedIn:"root",factory:()=>qg});var i_=(e,t,n,r)=>{};function s_(e,t,n,r){i_(e,t,n,r)}function za(e){return(e.flags&32)===32}var a_=()=>null;function Yg(e,t,n=!1){return a_(e,t,n)}function Qg(e,t){let n=e.contentQueries;if(n!==null){let r=F(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];sa(i),a.contentQueries(2,t[s],s)}}}finally{F(r)}}}function Ju(e,t,n){sa(0);let r=F(null);try{t(e,n)}finally{F(r)}}function Kg(e,t,n){if(cu(t)){let r=F(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{F(r)}}}var un=(function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e})(un||{}),ha;function c_(){if(ha===void 0&&(ha=null,tr.trustedTypes))try{ha=tr.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ha}function Ga(e){return c_()?.createHTML(e)||e}var ga;function Jg(){if(ga===void 0&&(ga=null,tr.trustedTypes))try{ga=tr.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ga}function Qh(e){return Jg()?.createHTML(e)||e}function Kh(e){return Jg()?.createScriptURL(e)||e}var Ma=class{changingThisBreaksApplicationSecurity;constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Qs})`}};function yi(e){return e instanceof Ma?e.changingThisBreaksApplicationSecurity:e}function Wa(e,t){let n=Xg(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Qs})`)}return n===t}function Xg(e){return e instanceof Ma&&e.getTypeName()||null}function l_(e){let t=new ed(e);return u_()?new Xu(t):t}var Xu=class{inertDocumentHelper;constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(Ga(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.firstChild?.remove(),n)}catch{return null}}},ed=class{defaultDoc;inertDocument;constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=Ga(t),n}};function u_(){try{return!!new window.DOMParser().parseFromString(Ga(""),"text/html")}catch{return!1}}var d_=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Nd(e){return e=String(e),e.match(d_)?e:"unsafe:"+e}function dn(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function Di(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var em=dn("area,br,col,hr,img,wbr"),tm=dn("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),nm=dn("rp,rt"),f_=Di(nm,tm),p_=Di(tm,dn("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),h_=Di(nm,dn("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Jh=Di(em,p_,h_,f_),rm=dn("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),g_=dn("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),m_=dn("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),v_=Di(rm,g_,m_),y_=dn("script,style,template"),td=class{sanitizedSomething=!1;buf=[];sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=__(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=E_(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=Xh(t).toLowerCase();if(!Jh.hasOwnProperty(n))return this.sanitizedSomething=!0,!y_.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!v_.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;rm[a]&&(c=Nd(c)),this.buf.push(" ",s,'="',eg(c),'"')}return this.buf.push(">"),!0}endElement(t){let n=Xh(t).toLowerCase();Jh.hasOwnProperty(n)&&!em.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(eg(t))}};function D_(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function E_(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw om(t);return t}function __(e){let t=e.firstChild;if(t&&D_(e,t))throw om(t);return t}function Xh(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function om(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var C_=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,w_=/([^\#-~ |!])/g;function eg(e){return e.replace(/&/g,"&amp;").replace(C_,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(w_,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var ma;function im(e,t){let n=null;try{ma=ma||l_(e);let r=t?String(t):"";n=ma.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=ma.getInertBodyElement(r)}while(r!==i);let a=new td().sanitizeChildren(tg(n)||n);return Ga(a)}finally{if(n){let r=tg(n)||n;for(;r.firstChild;)r.firstChild.remove()}}}function tg(e){return"content"in e&&b_(e)?e.content:null}function b_(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var Ei=(function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e})(Ei||{});function xd(e){let t=Od();return t?Qh(t.sanitize(Ei.HTML,e)||""):Wa(e,"HTML")?Qh(yi(e)):im(Ad(),bn(e))}function _i(e){let t=Od();return t?t.sanitize(Ei.URL,e)||"":Wa(e,"URL")?yi(e):Nd(bn(e))}function sm(e){let t=Od();if(t)return Kh(t.sanitize(Ei.RESOURCE_URL,e)||"");if(Wa(e,"ResourceURL"))return Kh(yi(e));throw new _(904,!1)}function I_(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?sm:_i}function Rd(e,t,n){return I_(t,n)(e)}function Od(){let e=B();return e&&e[Lt].sanitizer}function am(e){return e instanceof Function?e():e}function S_(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var cm="ng-template";function T_(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&S_(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(kd(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function kd(e){return e.type===4&&e.value!==cm}function M_(e,t,n){let r=e.type===4&&!n?cm:e.value;return t===r}function A_(e,t,n){let r=4,o=e.attrs,i=o!==null?R_(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Mt(r)&&!Mt(c))return!1;if(s&&Mt(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!M_(e,c,n)||c===""&&t.length===1){if(Mt(r))return!1;s=!0}}else if(r&8){if(o===null||!T_(e,o,c,n)){if(Mt(r))return!1;s=!0}}else{let l=t[++a],u=N_(c,o,kd(e),n);if(u===-1){if(Mt(r))return!1;s=!0;continue}if(l!==""){let d;if(u>i?d="":d=o[u+1].toLowerCase(),r&2&&l!==d){if(Mt(r))return!1;s=!0}}}}return Mt(r)||s}function Mt(e){return(e&1)===0}function N_(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return O_(t,e)}function lm(e,t,n=!1){for(let r=0;r<t.length;r++)if(A_(e,t[r],n))return!0;return!1}function x_(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function R_(e){for(let t=0;t<e.length;t++){let n=e[t];if(Ng(n))return t}return e.length}function O_(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function k_(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function ng(e,t){return e?":not("+t.trim()+")":t}function P_(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Mt(s)&&(t+=ng(i,o),o=""),r=s,i=i||!Mt(r);n++}return o!==""&&(t+=ng(i,o)),t}function F_(e){return e.map(P_).join(",")}function L_(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Mt(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var it={};function V_(e,t){return e.createText(t)}function j_(e,t,n){e.setValue(t,n)}function um(e,t,n){return e.createElement(t,n)}function Aa(e,t,n,r,o){e.insertBefore(t,n,r,o)}function dm(e,t,n){e.appendChild(t,n)}function rg(e,t,n,r,o){r!==null?Aa(e,t,n,r,o):dm(e,t,n)}function fm(e,t,n){e.removeChild(null,t,n)}function B_(e,t,n){e.setAttribute(t,"style",n)}function U_(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function pm(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&jE(e,t,r),o!==null&&U_(e,t,o),i!==null&&B_(e,t,i)}function Pd(e,t,n,r,o,i,s,a,c,l,u){let d=he+r,p=d+o,f=H_(d,p),g=typeof l=="function"?l():l;return f[A]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:g,incompleteFirstPass:!1,ssrId:u}}function H_(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:it);return n}function $_(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Pd(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Fd(e,t,n,r,o,i,s,a,c,l,u){let d=t.blueprint.slice();return d[bt]=o,d[L]=r|4|128|8|64|1024,(l!==null||e&&e[L]&2048)&&(d[L]|=2048),du(d),d[Ce]=d[or]=e,d[le]=n,d[Lt]=s||e&&e[Lt],d[te]=a||e&&e[te],d[$r]=c||e&&e[$r]||null,d[Ue]=i,d[Ko]=e_(),d[rr]=u,d[su]=l,d[He]=t.type==2?e[He]:d,d}function z_(e,t,n){let r=St(t,e),o=$_(n),i=e[Lt].rendererFactory,s=Ld(e,Fd(e,o,null,hm(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function hm(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function gm(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Ld(e,t){return e[zr]?e[iu][dt]=t:e[zr]=t,e[iu]=t,t}function v(e=1){mm(ue(),B(),ln()+e,!1)}function mm(e,t,n,r){if(!r)if((t[L]&3)===3){let i=e.preOrderCheckHooks;i!==null&&ya(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Da(t,i,0,n)}xn(n)}var qa=(function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e})(qa||{});function nd(e,t,n,r){let o=F(null);try{let[i,s,a]=e.inputs[n],c=null;(s&qa.SignalBased)!==0&&(c=t[i][Fe]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):bg(t,c,i,r)}finally{F(o)}}var At=(function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e})(At||{}),G_;function Vd(e,t){return G_(e,t)}var Za=new Set;function qr(e,t,n,r,o,i){if(r!=null){let s,a=!1;It(r)?s=r:jt(r)&&(a=!0,r=r[bt]);let c=ft(r);e===0&&n!==null?o==null?dm(t,n,c):Aa(t,n,c,o||null,!0):e===1&&n!==null?Aa(t,n,c,o||null,!0):e===2?og(i,()=>{fm(t,c,a)}):e===3&&og(i,()=>{t.destroyNode(c)}),s!=null&&nC(t,e,s,n,o)}}function W_(e,t){vm(e,t),t[bt]=null,t[Ue]=null}function q_(e,t,n,r,o,i){r[bt]=o,r[Ue]=t,Qa(e,r,n,1,o,i)}function vm(e,t){t[Lt].changeDetectionScheduler?.notify(9),Qa(e,t,t[te],2,null,null)}function Z_(e){let t=e[zr];if(!t)return ju(e[A],e);for(;t;){let n=null;if(jt(t))n=t[zr];else{let r=t[ge];r&&(n=r)}if(!n){for(;t&&!t[dt]&&t!==e;)jt(t)&&ju(t[A],t),t=t[Ce];t===null&&(t=e),jt(t)&&ju(t[A],t),n=t&&t[dt]}t=n}}function jd(e,t){let n=e[sr],r=n.indexOf(t);n.splice(r,1)}function Ya(e,t){if(ar(t))return;let n=t[te];n.destroyNode&&Qa(e,t,n,3,null,null),Z_(t)}function ju(e,t){if(ar(t))return;let n=F(null);try{t[L]&=-129,t[L]|=256,t[nt]&&jo(t[nt]),K_(e,t),Q_(e,t),t[A].type===1&&t[te].destroy();let r=t[Tn];if(r!==null&&It(t[Ce])){r!==t[Ce]&&jd(r,t);let o=t[Vt];o!==null&&o.detachView(e)}Qu(t)}finally{F(n)}}function og(e,t){if(e&&e[Oe]&&e[Oe].leave)if(e[Oe].skipLeaveAnimations)e[Oe].skipLeaveAnimations=!1;else{let n=e[Oe].leave,r=[];for(let o=0;o<n.length;o++){let i=n[o];r.push(i())}e[Oe].running=Promise.allSettled(r),e[Oe].leave=void 0}Y_(e,t)}function Y_(e,t){if(e&&e[Oe]&&e[Oe].running){e[Oe].running.then(()=>{e[Oe]&&e[Oe].running&&(e[Oe].running=void 0),Za.delete(e),t()});return}t()}function Q_(e,t){let n=e.cleanup,r=t[Hr];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[Hr]=null);let o=t[rn];if(o!==null){t[rn]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Jo];if(i!==null){t[Jo]=null;for(let s of i)s.destroy()}}function K_(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof lr)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];X(4,a,c);try{c.call(a)}finally{X(5,a,c)}}else{X(4,o,i);try{i.call(o)}finally{X(5,o,i)}}}}}function ym(e,t,n){return J_(e,t.parent,n)}function J_(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[bt];if(An(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===un.None||o===un.Emulated)return null}return St(r,n)}function Dm(e,t,n){return eC(e,t,n)}function X_(e,t,n){return e.type&40?St(e,n):null}var eC=X_,ig;function Bd(e,t,n,r){let o=ym(e,r,t),i=t[te],s=r.parent||t[Ue],a=Dm(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)rg(i,o,n[c],a,!1);else rg(i,o,n,a,!1);ig!==void 0&&ig(i,r,t,n,o)}function ai(e,t){if(t!==null){let n=t.type;if(n&3)return St(t,e);if(n&4)return rd(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return ai(e,r);{let o=e[t.index];return It(o)?rd(-1,o):ft(o)}}else{if(n&128)return ai(e,t.next);if(n&32)return Vd(t,e)()||ft(e[t.index]);{let r=Em(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=wn(e[He]);return ai(o,r)}else return ai(e,t.next)}}}return null}function Em(e,t){if(t!==null){let r=e[He][Ue],o=t.projection;return r.projection[o]}return null}function rd(e,t){let n=ge+e+1;if(n<t.length){let r=t[n],o=r[A].firstChild;if(o!==null)return ai(r,o)}return t[Mn]}function Ud(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Qr(ft(a),r),n.flags|=2),!za(n))if(c&8)Ud(e,t,n.child,r,o,i,!1),qr(t,e,o,a,i,r);else if(c&32){let l=Vd(n,r),u;for(;u=l();)qr(t,e,o,u,i,r);qr(t,e,o,a,i,r)}else c&16?_m(e,t,r,n,o,i):qr(t,e,o,a,i,r);n=s?n.projectionNext:n.next}}function Qa(e,t,n,r,o,i){Ud(n,r,e.firstChild,t,o,i,!1)}function tC(e,t,n){let r=t[te],o=ym(e,n,t),i=n.parent||t[Ue],s=Dm(i,n,t);_m(r,0,t,n,o,s)}function _m(e,t,n,r,o,i){let s=n[He],c=s[Ue].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];qr(t,e,o,u,i,n)}else{let l=c,u=s[Ce];Bg(r)&&(l.flags|=128),Ud(e,t,l,u,o,i,!0)}}function nC(e,t,n,r,o){let i=n[Mn],s=ft(n);i!==s&&qr(t,e,r,i,o);for(let a=ge;a<n.length;a++){let c=n[a];Qa(c[A],c,e,t,r,i)}}function rC(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:At.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=At.Important),e.setStyle(n,r,o,i))}}function Cm(e,t,n,r,o){let i=ln(),s=r&2;try{xn(-1),s&&t.length>he&&mm(e,t,he,!1),X(s?2:0,o,n),n(r,o)}finally{xn(i),X(s?3:1,o,n)}}function Hd(e,t,n){lC(e,t,n),(n.flags&64)===64&&uC(e,t,n)}function Ka(e,t,n=St){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function oC(e,t,n,r){let i=r.get(Zg,qg)||n===un.ShadowDom,s=e.selectRootElement(t,i);return iC(s),s}function iC(e){sC(e)}var sC=()=>null;function aC(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function wm(e,t,n,r,o,i){let s=t[A];if($d(e,s,t,n,r)){An(e)&&cC(t,e.index);return}e.type&3&&(n=aC(n)),bm(e,t,n,r,o,i)}function bm(e,t,n,r,o,i){if(e.type&3){let s=St(e,t);r=i!=null?i(r,e.value||"",n):r,o.setProperty(s,n,r)}else e.type&12}function cC(e,t){let n=pt(t,e);n[L]&16||(n[L]|=64)}function lC(e,t,n){let r=n.directiveStart,o=n.directiveEnd;An(n)&&z_(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Sa(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=li(t,e,s,n);if(Qr(c,t),i!==null&&hC(t,s-r,c,a,n,i),Bt(a)){let l=pt(n.index,t);l[le]=li(t,e,s,n)}}}function uC(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Oh();try{xn(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];ia(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&dC(c,l)}}finally{xn(-1),ia(s)}}function dC(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Im(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];lm(t,i.selectors,!1)&&(r??=[],Bt(i)?r.unshift(i):r.push(i))}return r}function fC(e,t,n,r,o,i){let s=St(e,t);pC(t[te],s,i,e.value,n,r,o)}function pC(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?bn(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function hC(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];nd(r,n,c,l)}}function Sm(e,t,n,r,o){let i=he+n,s=t[A],a=o(s,t,e,r,n);t[i]=a,Wr(e,!0);let c=e.type===2;return c?(pm(t[te],a,e),(Sh()===0||ei(e))&&Qr(a,t),Th()):Qr(a,t),ua()&&(!c||!za(e))&&Bd(s,t,a,e),e}function Tm(e){let t=e;return Cu()?wu():(t=t.parent,Wr(t,!1)),t}function gC(e,t){let n=e[$r];if(!n)return;let r;try{r=n.get($e,null)}catch{r=null}r?.(t)}function $d(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],d=t.data[l];nd(d,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];nd(u,l,r,o),a=!0}return a}function mC(e,t){let n=pt(t,e),r=n[A];vC(r,n);let o=n[bt];o!==null&&n[rr]===null&&(n[rr]=Yg(o,n[$r])),X(18),zd(r,n,n[le]),X(19,n[le])}function vC(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function zd(e,t,n){aa(t);try{let r=e.viewQuery;r!==null&&Ju(1,r,n);let o=e.template;o!==null&&Cm(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Vt]?.finishViewCreation(e),e.staticContentQueries&&Qg(e,t),e.staticViewQueries&&Ju(2,e.viewQuery,n);let i=e.components;i!==null&&yC(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[L]&=-5,ca()}}function yC(e,t){for(let n=0;n<t.length;n++)mC(e,t[n])}function Ci(e,t,n,r){let o=F(null);try{let i=t.tView,a=e[L]&4096?4096:16,c=Fd(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[Tn]=l;let u=e[Vt];return u!==null&&(c[Vt]=u.createEmbeddedView(i)),zd(i,c,n),c}finally{F(o)}}function Kr(e,t){return!t||t.firstChild===null||Bg(e)}function ui(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(ft(i)),It(i)&&Mm(i,r);let s=n.type;if(s&8)ui(e,t,n.child,r);else if(s&32){let a=Vd(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Em(t,n);if(Array.isArray(a))r.push(...a);else{let c=wn(t[He]);ui(c[A],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Mm(e,t){for(let n=ge;n<e.length;n++){let r=e[n],o=r[A].firstChild;o!==null&&ui(r[A],r,o,t)}e[Mn]!==e[bt]&&t.push(e[Mn])}function Am(e){if(e[na]!==null){for(let t of e[na])t.impl.addSequence(t);e[na].length=0}}var Nm=[];function DC(e){return e[nt]??EC(e)}function EC(e){let t=Nm.pop()??Object.create(CC);return t.lView=e,t}function _C(e){e.lView[nt]!==e&&(e.lView=null,Nm.push(e))}var CC=G(D({},Ir),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{ri(e.lView)},consumerOnSignalRead(){this.lView[nt]=this}});function wC(e){let t=e[nt]??Object.create(bC);return t.lView=e,t}var bC=G(D({},Ir),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=wn(e.lView);for(;t&&!xm(t[A]);)t=wn(t);t&&fu(t)},consumerOnSignalRead(){this.lView[nt]=this}});function xm(e){return e.type!==2}function Rm(e){if(e[Jo]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Jo])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[L]&8192)}}var IC=100;function Om(e,t=0){let r=e[Lt].rendererFactory,o=!1;o||r.begin?.();try{SC(e,t)}finally{o||r.end?.()}}function SC(e,t){let n=bu();try{Iu(!0),od(e,t);let r=0;for(;ni(e);){if(r===IC)throw new _(103,!1);r++,od(e,1)}}finally{Iu(n)}}function TC(e,t,n,r){if(ar(t))return;let o=t[L],i=!1,s=!1;aa(t);let a=!0,c=null,l=null;i||(xm(e)?(l=DC(t),c=Tr(l)):ys()===null?(a=!1,l=wC(t),c=Tr(l)):t[nt]&&(jo(t[nt]),t[nt]=null));try{du(t),Nh(e.bindingStartIndex),n!==null&&Cm(e,t,n,2,r),MC(t);let u=(o&3)===3;if(!i)if(u){let f=e.preOrderCheckHooks;f!==null&&ya(t,f,null)}else{let f=e.preOrderHooks;f!==null&&Da(t,f,0,null),Lu(t,0)}if(s||AC(t),Rm(t),km(t,0),e.contentQueries!==null&&Qg(e,t),!i)if(u){let f=e.contentCheckHooks;f!==null&&ya(t,f)}else{let f=e.contentHooks;f!==null&&Da(t,f,1),Lu(t,1)}xC(e,t);let d=e.components;d!==null&&Fm(t,d,0);let p=e.viewQuery;if(p!==null&&Ju(2,p,r),!i)if(u){let f=e.viewCheckHooks;f!==null&&ya(t,f)}else{let f=e.viewHooks;f!==null&&Da(t,f,2),Lu(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[ta]){for(let f of t[ta])f();t[ta]=null}i||(Am(t),t[L]&=-73)}catch(u){throw i||ri(t),u}finally{l!==null&&(Lo(l,c),a&&_C(l)),ca()}}function MC(e){let t=e[Oe];if(t?.enter){for(let n of t.enter)n();t.enter=void 0}}function km(e,t){for(let n=Hg(e);n!==null;n=$g(n))for(let r=ge;r<n.length;r++){let o=n[r];Pm(o,t)}}function AC(e){for(let t=Hg(e);t!==null;t=$g(t)){if(!(t[L]&2))continue;let n=t[sr];for(let r=0;r<n.length;r++){let o=n[r];fu(o)}}}function NC(e,t,n){X(18);let r=pt(t,e);Pm(r,n),X(19,r[le])}function Pm(e,t){ra(e)&&od(e,t)}function od(e,t){let r=e[A],o=e[L],i=e[nt],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Vo(i)),s||=!1,i&&(i.dirty=!1),e[L]&=-9217,s)TC(r,e,r.template,e[le]);else if(o&8192){let a=F(null);try{Rm(e),km(e,1);let c=r.components;c!==null&&Fm(e,c,1),Am(e)}finally{F(a)}}}function Fm(e,t,n){for(let r=0;r<t.length;r++)NC(e,t[r],n)}function xC(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)xn(~o);else{let i=o,s=n[++r],a=n[++r];Rh(s,i);let c=t[i];X(24,c),a(2,c),X(25,c)}}}finally{xn(-1)}}function Gd(e,t){let n=bu()?64:1088;for(e[Lt].changeDetectionScheduler?.notify(t);e;){e[L]|=n;let r=wn(e);if(Gr(e)&&!r)return e;e=r}return null}function Lm(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Vm(e,t){let n=ge+t;if(n<e.length)return e[n]}function wi(e,t,n,r=!0){let o=t[A];if(RC(o,t,e,n),r){let s=rd(n,e),a=t[te],c=a.parentNode(e[Mn]);c!==null&&q_(o,e[Ue],a,t,c,s)}let i=t[rr];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function jm(e,t){let n=di(e,t);return n!==void 0&&Ya(n[A],n),n}function di(e,t){if(e.length<=ge)return;let n=ge+t,r=e[n];if(r){let o=r[Tn];o!==null&&o!==e&&jd(o,r),t>0&&(e[n-1][dt]=r[dt]);let i=Zo(e,ge+t);W_(r[A],r);let s=i[Vt];s!==null&&s.detachView(i[A]),r[Ce]=null,r[dt]=null,r[L]&=-129}return r}function RC(e,t,n,r){let o=ge+r,i=n.length;r>0&&(n[o-1][dt]=t),r<i-ge?(t[dt]=n[o],Kl(n,ge+r,t)):(n.push(t),t[dt]=null),t[Ce]=n;let s=t[Tn];s!==null&&n!==s&&Bm(s,t);let a=t[Vt];a!==null&&a.insertView(e),oa(t),t[L]|=128}function Bm(e,t){let n=e[sr],r=t[Ce];if(jt(r))e[L]|=2;else{let o=r[Ce][He];t[He]!==o&&(e[L]|=2)}n===null?e[sr]=[t]:n.push(t)}var Rn=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[A];return ui(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[le]}set context(t){this._lView[le]=t}get destroyed(){return ar(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[Ce];if(It(t)){let n=t[Xo],r=n?n.indexOf(this):-1;r>-1&&(di(t,r),Zo(n,r))}this._attachedToViewContainer=!1}Ya(this._lView[A],this._lView)}onDestroy(t){pu(this._lView,t)}markForCheck(){Gd(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[L]&=-129}reattach(){oa(this._lView),this._lView[L]|=128}detectChanges(){this._lView[L]|=1024,Om(this._lView)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new _(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Gr(this._lView),n=this._lView[Tn];n!==null&&!t&&jd(n,this._lView),vm(this._lView[A],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new _(902,!1);this._appRef=t;let n=Gr(this._lView),r=this._lView[Tn];r!==null&&!n&&Bm(r,this._lView),oa(this._lView)}};var ur=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=OC;constructor(n,r,o){this._declarationLView=n,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,r){return this.createEmbeddedViewImpl(n,r)}createEmbeddedViewImpl(n,r,o){let i=Ci(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:r,dehydratedView:o});return new Rn(i)}}return e})();function OC(){return Wd(ke(),B())}function Wd(e,t){return e.type&4?new ur(t,e,eo(e,t)):null}function no(e,t,n,r,o){let i=e.data[t];if(i===null)i=kC(e,t,n,r,o),xh()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Mh();i.injectorIndex=s===null?-1:s.injectorIndex}return Wr(i,!0),i}function kC(e,t,n,r,o){let i=_u(),s=Cu(),a=s?i:i&&i.parent,c=e.data[t]=FC(e,a,n,t,r,o);return PC(e,c,i,s),c}function PC(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function FC(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return yu()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var hk=new RegExp(`^(\\d+)*(${o_}|${r_})*(.*)`);function LC(e){let t=e[au]??[],r=e[Ce][te],o=[];for(let i of t)i.data[Wg]!==void 0?o.push(i):VC(i,r);e[au]=o}function VC(e,t){let n=0,r=e.firstChild;if(r){let o=e.data[Gg];for(;n<o;){let i=r.nextSibling;fm(t,r,!1),r=i,n++}}}var jC=()=>null,BC=()=>null;function Na(e,t){return jC(e,t)}function Um(e,t,n){return BC(e,t,n)}var Hm=class{},Ja=class{},id=class{resolveComponentFactory(t){throw new _(917,!1)}},bi=class{static NULL=new id},dr=class{},fn=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>UC()}return e})();function UC(){let e=B(),t=ke(),n=pt(t.index,e);return(jt(n)?n:e)[te]}var $m=(()=>{class e{static \u0275prov=I({token:e,providedIn:"root",factory:()=>null})}return e})();var _a={},sd=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){let o=this.injector.get(t,_a,r);return o!==_a||n===_a?o:this.parentInjector.get(t,n,r)}};function xa(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Ul(o,a);else if(i==2){let c=a,l=t[++s];r=Ul(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function T(e,t=0){let n=B();if(n===null)return M(e,t);let r=ke();return Lg(r,n,xe(e),t)}function zm(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a=s,c=null,l=null;for(let u of s)if(u.resolveHostDirectives!==null){[a,c,l]=u.resolveHostDirectives(s);break}zC(e,t,n,a,i,c,l)}i!==null&&r!==null&&HC(n,r,i)}function HC(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new _(-301,!1);r.push(t[o],i)}}function $C(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function zC(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let f=r[p];!c&&Bt(f)&&(c=!0,$C(e,n,p)),Zu(Sa(n,t),e,f.type)}QC(n,e.data.length,a);for(let p=0;p<a;p++){let f=r[p];f.providersResolver&&f.providersResolver(f)}let l=!1,u=!1,d=gm(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let f=r[p];if(n.mergedAttrs=Yr(n.mergedAttrs,f.hostAttrs),WC(e,n,t,d,f),YC(d,f,o),s!==null&&s.has(f)){let[S,C]=s.get(f);n.directiveToIndex.set(f.type,[d,S+n.directiveStart,C+n.directiveStart])}else(i===null||!i.has(f))&&n.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(n.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(n.flags|=64);let g=f.type.prototype;!l&&(g.ngOnChanges||g.ngOnInit||g.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(g.ngOnChanges||g.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),d++}GC(e,n,i)}function GC(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))sg(0,t,o,r),sg(1,t,o,r),cg(t,r,!1);else{let i=n.get(o);ag(0,t,i,r),ag(1,t,i,r),cg(t,r,!0)}}}function sg(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),Gm(t,i)}}function ag(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Gm(t,s)}}function Gm(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function cg(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||kd(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function WC(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Jn(o.type,!0)),s=new lr(i,Bt(o),T,null);e.blueprint[r]=s,n[r]=s,qC(e,t,r,gm(e,n,o.hostVars,it),o)}function qC(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;ZC(s)!=a&&s.push(a),s.push(n,r,i)}}function ZC(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function YC(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Bt(t)&&(n[""]=e)}}function QC(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Wm(e,t,n,r,o,i,s,a){let c=t[A],l=c.consts,u=ht(l,s),d=no(c,e,n,r,u);return i&&zm(c,t,d,ht(l,a),o),d.mergedAttrs=Yr(d.mergedAttrs,d.attrs),d.attrs!==null&&xa(d,d.attrs,!1),d.mergedAttrs!==null&&xa(d,d.mergedAttrs,!0),c.queries!==null&&c.queries.elementStart(c,d),d}function qm(e,t){Mg(e,t),cu(t)&&e.queries.elementEnd(t)}function KC(e,t,n,r,o,i){let s=t.consts,a=ht(s,o),c=no(t,e,n,r,a);if(c.mergedAttrs=Yr(c.mergedAttrs,c.attrs),i!=null){let l=ht(s,i);c.localNames=[];for(let u=0;u<l.length;u+=2)c.localNames.push(l[u],-1)}return c.attrs!==null&&xa(c,c.attrs,!1),c.mergedAttrs!==null&&xa(c,c.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,c),c}function qd(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Zm(e,t,n){return e[t]=n}function JC(e,t){return e[t]}function Nt(e,t,n){if(n===it)return!1;let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function XC(e,t,n,r){let o=Nt(e,t,n);return Nt(e,t+1,r)||o}function Ca(e,t,n){return function r(o){let i=An(e)?pt(e.index,t):t;Gd(i,5);let s=t[le],a=lg(t,s,n,o),c=r.__ngNextListenerFn__;for(;c;)a=lg(t,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function lg(e,t,n,r){let o=F(null);try{return X(6,t,n),n(r)!==!1}catch(i){return gC(e,i),!1}finally{X(7,t,n),F(o)}}function Ym(e,t,n,r,o,i,s,a){let c=ei(e),l=!1,u=null;if(!r&&c&&(u=tw(t,n,i,e.index)),u!==null){let d=u.__ngLastListenerFn__||u;d.__ngNextListenerFn__=s,u.__ngLastListenerFn__=s,l=!0}else{let d=St(e,n),p=r?r(d):d;s_(n,p,i,a);let f=o.listen(p,i,a);if(!ew(i)){let g=r?S=>r(ft(S[e.index])):e.index;Qm(g,t,n,i,a,f,!1)}}return l}function ew(e){return e.startsWith("animation")||e.startsWith("transition")}function tw(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[Hr],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Qm(e,t,n,r,o,i,s){let a=t.firstCreatePass?gu(t):null,c=hu(n),l=c.length;c.push(o,i),a&&a.push(r,e,l,(l+1)*(s?-1:1))}function ug(e,t,n,r,o,i){let s=t[n],a=t[A],l=a.data[n].outputs[r],d=s[l].subscribe(i);Qm(e.index,a,t,o,i,d,!0)}var ad=Symbol("BINDING");var Ra=class extends bi{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Sn(t);return new Jr(n,this.ngModule)}};function nw(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&qa.SignalBased)!==0};return o&&(i.transform=o),i})}function rw(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function ow(e,t,n){let r=t instanceof pe?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new sd(n,r):n}function iw(e){let t=e.get(dr,null);if(t===null)throw new _(407,!1);let n=e.get($m,null),r=e.get(sn,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r,ngReflect:!1}}function sw(e,t){let n=Km(e);return um(t,n,n==="svg"?lu:n==="math"?Eh:null)}function Km(e){return(e.selectors[0][0]||"div").toLowerCase()}var Jr=class extends Ja{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=nw(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=rw(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=F_(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o,i,s){X(22);let a=F(null);try{let c=this.componentDef,l=aw(r,c,s,i),u=ow(c,o||this.ngModule,t),d=iw(u),p=d.rendererFactory.createRenderer(null,c),f=r?oC(p,r,c.encapsulation,u):sw(c,p),g=s?.some(dg)||i?.some(E=>typeof E!="function"&&E.bindings.some(dg)),S=Fd(null,l,null,512|hm(c),null,null,d,p,u,null,Yg(f,u,!0));S[he]=f,aa(S);let C=null;try{let E=Wm(he,S,2,"#host",()=>l.directiveRegistry,!0,0);f&&(pm(p,f,E),Qr(f,S)),Hd(l,S,E),Kg(l,E,S),qm(l,E),n!==void 0&&lw(E,this.ngContentSelectors,n),C=pt(E.index,S),S[le]=C[le],zd(l,S,null)}catch(E){throw C!==null&&Qu(C),Qu(S),E}finally{X(23),ca()}return new Oa(this.componentType,S,!!g)}finally{F(a)}}};function aw(e,t,n,r){let o=e?["ng-version","20.3.1"]:L_(t.selectors[0]),i=null,s=null,a=0;if(n)for(let u of n)a+=u[ad].requiredVars,u.create&&(u.targetIdx=0,(i??=[]).push(u)),u.update&&(u.targetIdx=0,(s??=[]).push(u));if(r)for(let u=0;u<r.length;u++){let d=r[u];if(typeof d!="function")for(let p of d.bindings){a+=p[ad].requiredVars;let f=u+1;p.create&&(p.targetIdx=f,(i??=[]).push(p)),p.update&&(p.targetIdx=f,(s??=[]).push(p))}}let c=[t];if(r)for(let u of r){let d=typeof u=="function"?u:u.type,p=tu(d);c.push(p)}return Pd(0,null,cw(i,s),1,a,c,null,null,null,[o],null)}function cw(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let r of e)r.create();if(n&2&&t)for(let r of t)r.update()}}function dg(e){let t=e[ad].kind;return t==="input"||t==="twoWay"}var Oa=class extends Hm{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,r){super(),this._rootLView=n,this._hasInputBindings=r,this._tNode=ti(n[A],he),this.location=eo(this._tNode,n),this.instance=pt(this._tNode.index,n)[le],this.hostView=this.changeDetectorRef=new Rn(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=$d(r,o[A],o,t,n);this.previousInputValues.set(t,n);let s=pt(r.index,o);Gd(s,1)}get injector(){return new cr(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function lw(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var On=(()=>{class e{static __NG_ELEMENT_ID__=uw}return e})();function uw(){let e=ke();return Xm(e,B())}var dw=On,Jm=class extends dw{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return eo(this._hostTNode,this._hostLView)}get injector(){return new cr(this._hostTNode,this._hostLView)}get parentInjector(){let t=Sd(this._hostTNode,this._hostLView);if(xg(t)){let n=Ia(t,this._hostLView),r=ba(t),o=n[A].data[r+8];return new cr(o,n)}else return new cr(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=fg(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-ge}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Na(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Kr(this._hostTNode,s)),a}createComponent(t,n,r,o,i,s,a){let c=t&&!xE(t),l;if(c)l=n;else{let C=n||{};l=C.index,r=C.injector,o=C.projectableNodes,i=C.environmentInjector||C.ngModuleRef,s=C.directives,a=C.bindings}let u=c?t:new Jr(Sn(t)),d=r||this.parentInjector;if(!i&&u.ngModule==null){let E=(c?d:this.parentInjector).get(pe,null);E&&(i=E)}let p=Sn(u.componentType??{}),f=Na(this._lContainer,p?.id??null),g=f?.firstChild??null,S=u.create(d,o,g,i,s,a);return this.insertImpl(S.hostView,l,Kr(this._hostTNode,f)),S}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Ch(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[Ce],l=new Jm(c,c[Ue],c[Ce]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return wi(s,o,i,r),t.attachToViewContainerRef(),Kl(Bu(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=fg(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=di(this._lContainer,n);r&&(Zo(Bu(this._lContainer),n),Ya(r[A],r))}detach(t){let n=this._adjustIndex(t,-1),r=di(this._lContainer,n);return r&&Zo(Bu(this._lContainer),n)!=null?new Rn(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function fg(e){return e[Xo]}function Bu(e){return e[Xo]||(e[Xo]=[])}function Xm(e,t){let n,r=t[e.index];return It(r)?n=r:(n=Lm(r,t,null,e),t[e.index]=n,Ld(t,n)),pw(n,t,e,r),new Jm(n,e,t)}function fw(e,t){let n=e[te],r=n.createComment(""),o=St(t,e),i=n.parentNode(o);return Aa(n,i,r,n.nextSibling(o),!1),r}var pw=mw,hw=()=>!1;function gw(e,t,n){return hw(e,t,n)}function mw(e,t,n,r){if(e[Mn])return;let o;n.type&8?o=ft(r):o=fw(t,n),e[Mn]=o}var cd=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},ld=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Zd(t,n).matches!==null&&this.queries[n].setDirty()}},ud=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=bw(t):this.predicate=t}},dd=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},fd=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,vw(n,i)),this.matchTNodeWithReadOption(t,n,Ea(n,t,i,!1,!1))}else r===ur?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Ea(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===ot||o===On||o===ur&&n.type&4)this.addMatch(n.index,-2);else{let i=Ea(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function vw(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function yw(e,t){return e.type&11?eo(e,t):e.type&4?Wd(e,t):null}function Dw(e,t,n,r){return n===-1?yw(t,e):n===-2?Ew(e,t,r):li(e,e[A],n,t)}function Ew(e,t,n){if(n===ot)return eo(t,e);if(n===ur)return Wd(t,e);if(n===On)return Xm(t,e)}function ev(e,t,n,r){let o=t[Vt].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(Dw(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function pd(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=ev(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let d=ge;d<u.length;d++){let p=u[d];p[Tn]===p[Ce]&&pd(p[A],p,l,r)}if(u[sr]!==null){let d=u[sr];for(let p=0;p<d.length;p++){let f=d[p];pd(f[A],f,l,r)}}}}}return r}function _w(e,t){return e[Vt].queries[t].queryList}function Cw(e,t,n){let r=new Ta((n&4)===4);return Ih(e,t,r,r.destroy),(t[Vt]??=new ld).queries.push(new cd(r))-1}function ww(e,t,n){let r=ue();return r.firstCreatePass&&(Iw(r,new ud(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Cw(r,B(),t)}function bw(e){return e.split(",").map(t=>t.trim())}function Iw(e,t,n){e.queries===null&&(e.queries=new dd),e.queries.track(new fd(t,n))}function Zd(e,t){return e.queries.getByIndex(t)}function Sw(e,t){let n=e[A],r=Zd(n,t);return r.crossesNgTemplate?pd(n,e,t,[]):ev(n,e,r,t)}var pg=new Set;function kn(e){pg.has(e)||(pg.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var fr=class{},Xa=class{};var ka=class extends fr{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Ra(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=eu(t);this._bootstrapComponents=am(i.bootstrap),this._r3Injector=xu(t,n,[{provide:fr,useValue:this},{provide:bi,useValue:this.componentFactoryResolver},...r],on(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Pa=class extends Xa{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new ka(this.moduleType,t,[])}};var fi=class extends fr{injector;componentFactoryResolver=new Ra(this);instance=null;constructor(t){super();let n=new er([...t.providers,{provide:fr,useValue:this},{provide:bi,useValue:this.componentFactoryResolver}],t.parent||Qo(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Ii(e,t,n=null){return new fi({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var Tw=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=nu(!1,n.type),o=r.length>0?Ii([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=I({token:e,providedIn:"environment",factory:()=>new e(M(pe))})}return e})();function Z(e){return mi(()=>{let t=tv(e),n=G(D({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Td.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(Tw).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||un.Emulated,styles:e.styles||et,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&kn("NgStandalone"),nv(n);let r=e.dependencies;return n.directiveDefs=hg(r,Mw),n.pipeDefs=hg(r,fh),n.id=xw(n),n})}function Mw(e){return Sn(e)||tu(e)}function zt(e){return mi(()=>({type:e.type,bootstrap:e.bootstrap||et,declarations:e.declarations||et,imports:e.imports||et,exports:e.exports||et,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Aw(e,t){if(e==null)return In;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=qa.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function Nw(e){if(e==null)return In;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function ve(e){return mi(()=>{let t=tv(e);return nv(t),t})}function tv(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||In,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||et,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:Aw(e.inputs,t),outputs:Nw(e.outputs),debugInfo:null}}function nv(e){e.features?.forEach(t=>t(e))}function hg(e,t){return e?()=>{let n=typeof e=="function"?e():e,r=[];for(let o of n){let i=t(o);i!==null&&r.push(i)}return r}:null}function xw(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function Rw(e){return Object.getPrototypeOf(e.prototype).constructor}function Gt(e){let t=Rw(e.type),n=!0,r=[e];for(;t;){let o;if(Bt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new _(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Uu(e.inputs),s.declaredInputs=Uu(e.declaredInputs),s.outputs=Uu(e.outputs);let a=o.hostBindings;a&&Lw(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&Pw(e,c),l&&Fw(e,l),Ow(e,o),nh(e.outputs,o.outputs),Bt(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Gt&&(n=!1)}}t=Object.getPrototypeOf(t)}kw(r)}function Ow(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function kw(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Yr(o.hostAttrs,n=Yr(n,o.hostAttrs))}}function Uu(e){return e===In?{}:e===et?[]:e}function Pw(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function Fw(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function Lw(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function rv(e,t,n,r,o,i,s,a){if(n.firstCreatePass){e.mergedAttrs=Yr(e.mergedAttrs,e.attrs);let u=e.tView=Pd(2,e,o,i,s,n.directiveRegistry,n.pipeRegistry,null,n.schemas,n.consts,null);n.queries!==null&&(n.queries.template(n,e),u.queries=n.queries.embeddedTView(e))}a&&(e.flags|=a),Wr(e,!1);let c=jw(n,t,e,r);ua()&&Bd(n,t,c,e),Qr(c,t);let l=Lm(c,t,c,e);t[r+he]=l,Ld(t,l),gw(l,e,t)}function Vw(e,t,n,r,o,i,s,a,c,l,u){let d=n+he,p;return t.firstCreatePass?(p=no(t,d,4,s||null,a||null),vu()&&zm(t,e,p,ht(t.consts,l),Im),Mg(t,p)):p=t.data[d],rv(p,e,t,n,r,o,i,c),ei(p)&&Hd(t,e,p),l!=null&&Ka(e,p,u),p}function pi(e,t,n,r,o,i,s,a,c,l,u){let d=n+he,p;if(t.firstCreatePass){if(p=no(t,d,4,s||null,a||null),l!=null){let f=ht(t.consts,l);p.localNames=[];for(let g=0;g<f.length;g+=2)p.localNames.push(f[g],-1)}}else p=t.data[d];return rv(p,e,t,n,r,o,i,c),l!=null&&Ka(e,p,u),p}function ec(e,t,n,r,o,i,s,a){let c=B(),l=ue(),u=ht(l.consts,i);return Vw(c,l,e,t,n,r,o,u,void 0,s,a),ec}var jw=Bw;function Bw(e,t,n,r){return da(!0),t[te].createComment("")}var Yd=(function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e})(Yd||{}),Si=new b(""),ov=!1,hd=class extends ce{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,yh()&&(this.destroyRef=h(Ut,{optional:!0})??void 0,this.pendingTasks=h(Ht,{optional:!0})??void 0)}emit(t){let n=F(null);try{super.next(t)}finally{F(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof de&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},k=hd;function iv(e){let t,n;function r(){e=si;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function gg(e){return queueMicrotask(()=>e()),()=>{e=si}}var Qd="isAngularZone",Fa=Qd+"_ID",Uw=0,ne=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new k(!1);onMicrotaskEmpty=new k(!1);onStable=new k(!1);onError=new k(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=ov}=t;if(typeof Zone>"u")throw new _(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,zw(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Qd)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new _(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new _(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Hw,si,si);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},Hw={};function Kd(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function $w(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){iv(()=>{e.callbackScheduled=!1,gd(e),e.isCheckStableRunning=!0,Kd(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),gd(e)}function zw(e){let t=()=>{$w(e)},n=Uw++;e._inner=e._inner.fork({name:"angular",properties:{[Qd]:!0,[Fa]:n,[Fa+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Gw(c))return r.invokeTask(i,s,a,c);try{return mg(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),vg(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return mg(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Ww(c)&&t(),vg(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,gd(e),Kd(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function gd(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function mg(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function vg(e){e._nesting--,Kd(e)}var hi=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new k;onMicrotaskEmpty=new k;onStable=new k;onError=new k;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function Gw(e){return sv(e,"__ignore_ng_zone__")}function Ww(e){return sv(e,"__scheduler_tick__")}function sv(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var av=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=I({token:e,providedIn:"root",factory:()=>new e})}return e})();var Jd=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Xd=new b("");function Pn(e){return!!e&&typeof e.then=="function"}function ef(e){return!!e&&typeof e.subscribe=="function"}var cv=new b("");var tf=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=h(cv,{optional:!0})??[];injector=h(Ct);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=Re(this.injector,o);if(Pn(i))n.push(i);else if(ef(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),tc=new b("");function lv(){pl(()=>{let e="";throw new _(600,e)})}function uv(e){return e.isBoundToModule}var qw=10;var Fn=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=h($e);afterRenderManager=h(av);zonelessEnabled=h(ii);rootEffectScheduler=h(Fu);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new ce;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=h(Ht);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(O(n=>!n))}constructor(){h(Si,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=h(pe);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=Ct.NULL){return this._injector.get(ne).run(()=>{X(10);let s=n instanceof Ja;if(!this._injector.get(tf).done){let g="";throw new _(405,g)}let c;s?c=n:c=this._injector.get(bi).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let l=uv(c)?void 0:this._injector.get(fr),u=r||c.selector,d=c.create(o,[],u,l),p=d.location.nativeElement,f=d.injector.get(Xd,null);return f?.registerApplication(p),d.onDestroy(()=>{this.detachView(d.hostView),ci(this.components,d),f?.unregisterApplication(p)}),this._loadComponent(d),X(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){X(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Yd.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new _(101,!1);let n=F(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,F(n),this.afterTick.next(),X(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(dr,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<qw;)X(14),this.synchronizeOnce(),X(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!ni(o))continue;let i=r&&!this.zonelessEnabled?0:1;Om(o,i),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>ni(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;ci(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(n),this._injector.get(tc,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>ci(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new _(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ci(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function gt(e,t,n,r){let o=B(),i=Nn();if(Nt(o,i,t)){let s=ue(),a=oi();fC(a,o,e,t,n,r)}return gt}var _k=typeof document<"u"&&typeof document?.documentElement?.getAnimations=="function";var md=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t,!0))}};function Hu(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function Zw(e,t,n){let r,o,i=0,s=e.length-1,a=void 0;if(Array.isArray(t)){let c=t.length-1;for(;i<=s&&i<=c;){let l=e.at(i),u=t[i],d=Hu(i,l,i,u,n);if(d!==0){d<0&&e.updateValue(i,u),i++;continue}let p=e.at(s),f=t[c],g=Hu(s,p,c,f,n);if(g!==0){g<0&&e.updateValue(s,f),s--,c--;continue}let S=n(i,l),C=n(s,p),E=n(i,u);if(Object.is(E,C)){let se=n(c,f);Object.is(se,S)?(e.swap(i,s),e.updateValue(s,f),c--,s--):e.move(s,i),e.updateValue(i,u),i++;continue}if(r??=new La,o??=Dg(e,i,s,n),vd(e,r,i,E))e.updateValue(i,u),i++,s++;else if(o.has(E))r.set(S,e.detach(i)),s--;else{let se=e.create(i,t[i]);e.attach(i,se),i++,s++}}for(;i<=c;)yg(e,r,n,i,t[i]),i++}else if(t!=null){let c=t[Symbol.iterator](),l=c.next();for(;!l.done&&i<=s;){let u=e.at(i),d=l.value,p=Hu(i,u,i,d,n);if(p!==0)p<0&&e.updateValue(i,d),i++,l=c.next();else{r??=new La,o??=Dg(e,i,s,n);let f=n(i,d);if(vd(e,r,i,f))e.updateValue(i,d),i++,s++,l=c.next();else if(!o.has(f))e.attach(i,e.create(i,d)),i++,s++,l=c.next();else{let g=n(i,u);r.set(g,e.detach(i)),s--}}}for(;!l.done;)yg(e,r,n,e.length,l.value),l=c.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(c=>{e.destroy(c)})}function vd(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function yg(e,t,n,r,o){if(vd(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function Dg(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var La=class{kvMap=new Map;_vMap=void 0;has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function we(e,t,n,r,o,i,s,a){kn("NgControlFlow");let c=B(),l=ue(),u=ht(l.consts,i);return pi(c,l,e,t,n,r,o,u,256,s,a),nf}function nf(e,t,n,r,o,i,s,a){kn("NgControlFlow");let c=B(),l=ue(),u=ht(l.consts,i);return pi(c,l,e,t,n,r,o,u,512,s,a),nf}function be(e,t){kn("NgControlFlow");let n=B(),r=Nn(),o=n[r]!==it?n[r]:-1,i=o!==-1?Va(n,he+o):void 0,s=0;if(Nt(n,r,e)){let a=F(null);try{if(i!==void 0&&jm(i,s),e!==-1){let c=he+e,l=Va(n,c),u=_d(n[A],c),d=Um(l,u,n),p=Ci(n,u,t,{dehydratedView:d});wi(l,p,s,Kr(u,d))}}finally{F(a)}}else if(i!==void 0){let a=Vm(i,s);a!==void 0&&(a[le]=t)}}var yd=class{lContainer;$implicit;$index;constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-ge}};var Dd=class{hasEmptyBlock;trackByFn;liveCollection;constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function xt(e,t,n,r,o,i,s,a,c,l,u,d,p){kn("NgControlFlow");let f=B(),g=ue(),S=c!==void 0,C=B(),E=a?s.bind(C[He][le]):s,se=new Dd(S,E);C[he+e]=se,pi(f,g,e+1,t,n,r,o,ht(g.consts,i),256),S&&pi(f,g,e+2,c,l,u,d,ht(g.consts,p),512)}var Ed=class extends md{lContainer;hostLView;templateTNode;operationsCounter=void 0;needsIndexUpdate=!1;constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r}get length(){return this.lContainer.length-ge}at(t){return this.getLView(t)[le].$implicit}attach(t,n){let r=n[rr];this.needsIndexUpdate||=t!==this.length,wi(this.lContainer,n,t,Kr(this.templateTNode,r))}detach(t,n){return this.needsIndexUpdate||=t!==this.length-1,n&&Yw(this.lContainer,t),Qw(this.lContainer,t)}create(t,n){let r=Na(this.lContainer,this.templateTNode.tView.ssrId),o=Ci(this.hostLView,this.templateTNode,new yd(this.lContainer,n,t),{dehydratedView:r});return this.operationsCounter?.recordCreate(),o}destroy(t){Ya(t[A],t),this.operationsCounter?.recordDestroy()}updateValue(t,n){this.getLView(t)[le].$implicit=n}reset(){this.needsIndexUpdate=!1,this.operationsCounter?.reset()}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[le].$index=t}getLView(t){return Kw(this.lContainer,t)}};function Rt(e){let t=F(null),n=ln();try{let r=B(),o=r[A],i=r[n],s=n+1,a=Va(r,s);if(i.liveCollection===void 0){let l=_d(o,s);i.liveCollection=new Ed(a,r,l)}else i.liveCollection.reset();let c=i.liveCollection;if(Zw(c,e,i.trackByFn),c.updateIndexes(),i.hasEmptyBlock){let l=Nn(),u=c.length===0;if(Nt(r,l,u)){let d=n+2,p=Va(r,d);if(u){let f=_d(o,d),g=Um(p,f,r),S=Ci(r,f,void 0,{dehydratedView:g});wi(p,S,0,Kr(f,g))}else o.firstUpdatePass&&LC(p),jm(p,0)}}}finally{F(t)}}function Va(e,t){return e[t]}function Yw(e,t){if(e.length<=ge)return;let n=ge+t,r=e[n];r&&r[Oe]&&(r[Oe].skipLeaveAnimations=!0)}function Qw(e,t){return di(e,t)}function Kw(e,t){return Vm(e,t)}function _d(e,t){return ti(e,t)}function V(e,t,n){let r=B(),o=Nn();if(Nt(r,o,t)){let i=ue(),s=oi();wm(s,r,e,t,r[te],n)}return V}function Eg(e,t,n,r,o){$d(t,e,n,o?"class":"style",r)}function m(e,t,n,r){let o=B(),i=o[A],s=e+he,a=i.firstCreatePass?Wm(s,o,2,t,Im,vu(),n,r):i.data[s];if(Sm(a,o,e,t,dv),ei(a)){let c=o[A];Hd(c,o,a),Kg(c,a,o)}return r!=null&&Ka(o,a),m}function y(){let e=ue(),t=ke(),n=Tm(t);return e.firstCreatePass&&qm(e,n),Du(n)&&Eu(),mu(),n.classesWithoutHost!=null&&LE(n)&&Eg(e,n,B(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&VE(n)&&Eg(e,n,B(),n.stylesWithoutHost,!1),y}function Se(e,t,n,r){return m(e,t,n,r),y(),Se}function Y(e,t,n,r){let o=B(),i=o[A],s=e+he,a=i.firstCreatePass?KC(s,i,2,t,n,r):i.data[s];return Sm(a,o,e,t,dv),r!=null&&Ka(o,a),Y}function ee(){let e=ke(),t=Tm(e);return Du(t)&&Eu(),mu(),ee}function gr(e,t,n,r){return Y(e,t,n,r),ee(),gr}var dv=(e,t,n,r,o)=>(da(!0),um(t[te],r,jh()));function re(){return B()}function Te(e,t,n){let r=B(),o=Nn();if(Nt(r,o,t)){let i=ue(),s=oi();bm(s,r,e,t,r[te],n)}return Te}var Ti="en-US";var Jw=Ti;function fv(e){typeof e=="string"&&(Jw=e.toLowerCase().replace(/_/g,"-"))}function H(e,t,n){let r=B(),o=ue(),i=ke();return pv(o,r,r[te],i,e,t,n),H}function mt(e,t,n){let r=B(),o=ue(),i=ke();return(i.type&3||n)&&Ym(i,o,r,n,r[te],e,t,Ca(i,r,t)),mt}function pv(e,t,n,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=Ca(r,t,i),Ym(r,e,t,s,n,o,i,c)&&(a=!1)),a){let l=r.outputs?.[o],u=r.hostDirectiveOutputs?.[o];if(u&&u.length)for(let d=0;d<u.length;d+=2){let p=u[d],f=u[d+1];c??=Ca(r,t,i),ug(r,t,p,f,o,c)}if(l&&l.length)for(let d of l)c??=Ca(r,t,i),ug(r,t,d,o,o,c)}}function R(e=1){return Vh(e)}function Xw(e,t){let n=null,r=x_(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?lm(e,i,!0):k_(r,i))return o}return n}function rf(e){let t=B()[He][Ue];if(!t.projection){let n=e?e.length:1,r=t.projection=lh(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?Xw(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function of(e,t=0,n,r,o,i){let s=B(),a=ue(),c=r?e+1:null;c!==null&&pi(s,a,c,r,o,i,null,n);let l=no(a,he+e,16,null,n||null);l.projection===null&&(l.projection=t),wu();let d=!s[rr]||yu();s[He][Ue].projection[l.projection]===null&&c!==null?eb(s,a,c):d&&!za(l)&&tC(a,s,l)}function eb(e,t,n){let r=he+n,o=t.data[r],i=e[r],s=Na(i,o.tView.ssrId),a=Ci(e,o,void 0,{dehydratedView:s});wi(i,a,0,Kr(o,s))}function vt(e,t,n){ww(e,t,n)}function st(e){let t=B(),n=ue(),r=Mu();sa(r+1);let o=Zd(n,r);if(e.dirty&&_h(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Sw(t,r);e.reset(i,KE),e.notifyOnChanges()}return!0}return!1}function at(){return _w(B(),Mu())}function va(e,t){return e<<17|t<<2}function pr(e){return e>>17&32767}function tb(e){return(e&2)==2}function nb(e,t){return e&131071|t<<17}function Cd(e){return e|2}function Xr(e){return(e&131068)>>2}function $u(e,t){return e&-131069|t<<2}function rb(e){return(e&1)===1}function wd(e){return e|1}function ob(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=pr(s),c=Xr(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||Ur(d,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let p=pr(e[a+1]);e[r+1]=va(p,a),p!==0&&(e[p+1]=$u(e[p+1],r)),e[a+1]=nb(e[a+1],r)}else e[r+1]=va(a,0),a!==0&&(e[a+1]=$u(e[a+1],r)),a=r;else e[r+1]=va(c,0),a===0?a=r:e[c+1]=$u(e[c+1],r),c=r;l&&(e[r+1]=Cd(e[r+1])),_g(e,u,r,!0),_g(e,u,r,!1),ib(t,u,e,r,i),s=va(a,c),i?t.classBindings=s:t.styleBindings=s}function ib(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Ur(i,t)>=0&&(n[r+1]=wd(n[r+1]))}function _g(e,t,n,r){let o=e[n+1],i=t===null,s=r?pr(o):Xr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];sb(c,t)&&(a=!0,e[s+1]=r?wd(l):Cd(l)),s=r?pr(l):Xr(l)}a&&(e[n+1]=r?Cd(o):wd(o))}function sb(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Ur(e,t)>=0:!1}function oe(e,t){return ab(e,t,null,!0),oe}function ab(e,t,n,r){let o=B(),i=ue(),s=Tu(2);if(i.firstUpdatePass&&lb(i,e,s,r),t!==it&&Nt(o,s,t)){let a=i.data[ln()];hb(i,a,o,o[te],e,o[s+1]=gb(t,n),r,s)}}function cb(e,t){return t>=e.expandoStartIndex}function lb(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[ln()],s=cb(e,n);mb(i,r)&&t===null&&!s&&(t=!1),t=ub(o,i,t,r),ob(o,i,t,n,s,r)}}function ub(e,t,n,r){let o=kh(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=zu(null,e,t,n,r),n=gi(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=zu(o,e,t,n,r),i===null){let c=db(e,t,r);c!==void 0&&Array.isArray(c)&&(c=zu(null,e,t,c[1],r),c=gi(c,t.attrs,r),fb(e,t,r,c))}else i=pb(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function db(e,t,n){let r=n?t.classBindings:t.styleBindings;if(Xr(r)!==0)return e[pr(r)]}function fb(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[pr(o)]=r}function pb(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=gi(r,s,n)}return gi(r,t.attrs,n)}function zu(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=gi(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function gi(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),dh(e,s,n?!0:t[++i]))}return e===void 0?null:e}function hb(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=rb(l)?Cg(c,t,n,o,Xr(l),s):void 0;if(!ja(u)){ja(i)||tb(l)&&(i=Cg(c,null,n,o,a,s));let d=uu(ln(),n);rC(r,s,d,o,i)}}function Cg(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,d=u===null,p=n[o+1];p===it&&(p=d?et:void 0);let f=d?ea(p,r):u===r?p:void 0;if(l&&!ja(f)&&(f=ea(c,r)),ja(f)&&(a=f,s))return a;let g=e[o+1];o=s?pr(g):Xr(g)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=ea(c,r))}return a}function ja(e){return e!==void 0}function gb(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=on(yi(e)))),e}function mb(e,t){return(e.flags&(t?8:16))!==0}function w(e,t=""){let n=B(),r=ue(),o=e+he,i=r.firstCreatePass?no(r,o,1,t,null):r.data[o],s=vb(r,n,i,t,e);n[o]=s,ua()&&Bd(r,n,s,i),Wr(i,!1)}var vb=(e,t,n,r,o)=>(da(!0),V_(t[te],r));function yb(e,t,n,r=""){return Nt(e,Nn(),n)?t+bn(n)+r:it}function Db(e,t,n,r,o,i=""){let s=Ah(),a=XC(e,s,n,o);return Tu(2),a?t+bn(n)+r+bn(o)+i:it}function ct(e){return ye("",e),ct}function ye(e,t,n){let r=B(),o=yb(r,e,t,n);return o!==it&&hv(r,ln(),o),ye}function nc(e,t,n,r,o){let i=B(),s=Db(i,e,t,n,r,o);return s!==it&&hv(i,ln(),s),nc}function hv(e,t,n){let r=uu(t,e);j_(e[te],r,n)}function Ve(e,t,n){ku(t)&&(t=t());let r=B(),o=Nn();if(Nt(r,o,t)){let i=ue(),s=oi();wm(s,r,e,t,r[te],n)}return Ve}function ze(e,t){let n=ku(e);return n&&e.set(t),n}function je(e,t){let n=B(),r=ue(),o=ke();return pv(r,n,n[te],o,e,t),je}function Eb(e,t,n){let r=ue();if(r.firstCreatePass){let o=Bt(e);bd(n,r.data,r.blueprint,o,!0),bd(t,r.data,r.blueprint,o,!1)}}function bd(e,t,n,r,o){if(e=xe(e),Array.isArray(e))for(let i=0;i<e.length;i++)bd(e[i],t,n,r,o);else{let i=ue(),s=B(),a=ke(),c=Xn(e)?e:xe(e.provide),l=ou(e),u=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(Xn(e)||!e.multi){let f=new lr(l,o,T,null),g=Wu(c,t,o?u:u+p,d);g===-1?(Zu(Sa(a,s),i,c),Gu(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[g]=f,s[g]=f)}else{let f=Wu(c,t,u+p,d),g=Wu(c,t,u,u+p),S=f>=0&&n[f],C=g>=0&&n[g];if(o&&!C||!o&&!S){Zu(Sa(a,s),i,c);let E=wb(o?Cb:_b,n.length,o,r,l,e);!o&&C&&(n[g].providerFactory=E),Gu(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(E),s.push(E)}else{let E=gv(n[o?g:f],l,!o&&r);Gu(i,e,f>-1?f:g,E)}!o&&r&&C&&n[g].componentProviders++}}}function Gu(e,t,n,r){let o=Xn(t),i=vh(t);if(o||i){let c=(i?xe(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function gv(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Wu(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function _b(e,t,n,r,o){return Id(this.multi,[])}function Cb(e,t,n,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=li(r,r[A],this.providerFactory.index,o);s=c.slice(0,a),Id(i,s);for(let l=a;l<c.length;l++)s.push(c[l])}else s=[],Id(i,s);return s}function Id(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function wb(e,t,n,r,o,i){let s=new lr(e,n,T,null);return s.multi=[],s.index=t,s.componentProviders=0,gv(s,o,r&&!n),s}function Wt(e,t=[]){return n=>{n.providersResolver=(r,o)=>Eb(r,o?o(e):e,t)}}function Ye(e,t,n){let r=Su()+e,o=B();return o[r]===it?Zm(o,r,n?t.call(n):t()):JC(o,r)}function ro(e,t,n,r){return Ib(B(),Su(),e,t,n,r)}function bb(e,t){let n=e[t];return n===it?void 0:n}function Ib(e,t,n,r,o,i){let s=t+n;return Nt(e,s,o)?Zm(e,s+1,i?r.call(i,o):r(o)):bb(e,s+1)}var Ba=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},sf=(()=>{class e{compileModuleSync(n){return new Pa(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=eu(n),i=am(o.declarations).reduce((s,a)=>{let c=Sn(a);return c&&s.push(new Jr(c)),s},[]);return new Ba(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Sb=(()=>{class e{zone=h(ne);changeDetectionScheduler=h(sn);applicationRef=h(Fn);applicationErrorHandler=h($e);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function mv({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new ne(G(D({},vv()),{scheduleInRootZone:n})),[{provide:ne,useFactory:e},{provide:an,multi:!0,useFactory:()=>{let r=h(Sb,{optional:!0});return()=>r.initialize()}},{provide:an,multi:!0,useFactory:()=>{let r=h(Tb);return()=>{r.initialize()}}},t===!0?{provide:Pu,useValue:!0}:[],{provide:fa,useValue:n??ov},{provide:$e,useFactory:()=>{let r=h(ne),o=h(pe),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(Ft),i.handleError(s))})}}}]}function vv(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Tb=(()=>{class e{subscription=new de;initialized=!1;zone=h(ne);pendingTasks=h(Ht);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{ne.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{ne.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var af=(()=>{class e{applicationErrorHandler=h($e);appRef=h(Fn);taskService=h(Ht);ngZone=h(ne);zonelessEnabled=h(ii);tracing=h(Si,{optional:!0});disableScheduling=h(Pu,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new de;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Fa):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(h(fa,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof hi||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?gg:iv;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Fa+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(n),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,gg(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function cf(){return kn("NgZoneless"),cn([{provide:sn,useExisting:af},{provide:ne,useClass:hi},{provide:ii,useValue:!0},{provide:fa,useValue:!1},[]])}function Mb(){return typeof $localize<"u"&&$localize.locale||Ti}var rc=new b("",{providedIn:"root",factory:()=>h(rc,{optional:!0,skipSelf:!0})||Mb()});function Me(e){return Xp(e)}function oo(e,t){return _s(e,t?.equal)}var yv=class{[Fe];constructor(t){this[Fe]=t}destroy(){this[Fe].destroy()}};var Cv=Symbol("InputSignalNode#UNSET"),qb=G(D({},Cs),{transformFn:void 0,applyValueToInputSignal(e,t){Ar(e,t)}});function wv(e,t){let n=Object.create(qb);n.value=e,n.transformFn=t?.transform;function r(){if(Sr(n),n.value===Cv){let o=null;throw new _(-950,o)}return n.value}return r[Fe]=n,r}var ic=class{attributeName;constructor(t){this.attributeName=t}__NG_ELEMENT_ID__=()=>vi(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},Zb=new b("");Zb.__NG_ELEMENT_ID__=e=>{let t=ke();if(t===null)throw new _(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new _(204,!1)};function Dv(e,t){return wv(e,t)}function Yb(e){return wv(Cv,e)}var bv=(Dv.required=Yb,Dv);var lf=new b(""),Qb=new b("");function Mi(e){return!e.moduleRef}function Kb(e){let t=Mi(e)?e.r3Injector:e.moduleRef.injector,n=t.get(ne);return n.run(()=>{Mi(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get($e),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:r})}),Mi(e)){let i=()=>t.destroy(),s=e.platformInjector.get(lf);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(lf);s.add(i),e.moduleRef.onDestroy(()=>{ci(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return Xb(r,n,()=>{let i=t.get(Ht),s=i.add(),a=t.get(tf);return a.runInitializers(),a.donePromise.then(()=>{let c=t.get(rc,Ti);if(fv(c||Ti),!t.get(Qb,!0))return Mi(e)?t.get(Fn):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Mi(e)){let u=t.get(Fn);return e.rootComponent!==void 0&&u.bootstrap(e.rootComponent),u}else return Jb?.(e.moduleRef,e.allPlatformModules),e.moduleRef}).finally(()=>void i.remove(s))})})}var Jb;function Xb(e,t,n){try{let r=n();return Pn(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e(r)),r}}var oc=null;function eI(e=[],t){return Ct.create({name:t,providers:[{provide:Yo,useValue:"platform"},{provide:lf,useValue:new Set([()=>oc=null])},...e]})}function tI(e=[]){if(oc)return oc;let t=eI(e);return oc=t,lv(),nI(t),t}function nI(e){let t=e.get(Ha,null);Re(e,()=>{t?.forEach(n=>n())})}var Ln=(()=>{class e{static __NG_ELEMENT_ID__=rI}return e})();function rI(e){return oI(ke(),B(),(e&16)===16)}function oI(e,t,n){if(An(e)&&!n){let r=pt(e.index,t);return new Rn(r,r)}else if(e.type&175){let r=t[He];return new Rn(r,t)}return null}var uf=class{constructor(){}supports(t){return t instanceof Map||qd(t)}create(){return new df}},df=class{_records=new Map;_mapHead=null;_appendAfter=null;_previousMapHead=null;_changesHead=null;_changesTail=null;_additionsHead=null;_additionsTail=null;_removalsHead=null;_removalsTail=null;get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(t){let n;for(n=this._mapHead;n!==null;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;n!==null;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;n!==null;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}diff(t){if(!t)t=new Map;else if(!(t instanceof Map||qd(t)))throw new _(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{let i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;r!==null;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){let r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){let o=this._records.get(t);this._maybeAddToChanges(o,n);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let r=new ff(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;t!==null;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;t!=null;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){this._additionsHead===null?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){this._changesHead===null?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}},ff=class{key;previousValue=null;currentValue=null;_nextPrevious=null;_next=null;_prev=null;_nextAdded=null;_nextRemoved=null;_nextChanged=null;constructor(t){this.key=t}};function Ev(){return new pf([new uf])}var pf=(()=>{class e{static \u0275prov=I({token:e,providedIn:"root",factory:Ev});factories;constructor(n){this.factories=n}static create(n,r){if(r){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:()=>{let r=h(e,{optional:!0,skipSelf:!0});return e.create(n,r||Ev())}}}find(n){let r=this.factories.find(o=>o.supports(n));if(r)return r;throw new _(901,!1)}}return e})();function Iv(e){let{rootComponent:t,appProviders:n,platformProviders:r,platformRef:o}=e;X(8);try{let i=o?.injector??tI(r),s=[mv({}),{provide:sn,useExisting:af},Uh,...n||[]],a=new fi({providers:s,parent:i,debugName:"",runEnvironmentInitializers:!1});return Kb({r3Injector:a.injector,platformInjector:i,rootComponent:t})}catch(i){return Promise.reject(i)}finally{X(9)}}function mr(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}var Mv=null;function yt(){return Mv}function hf(e){Mv??=e}var Ai=class{},gf=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>h(Av),providedIn:"platform"})}return e})();var Av=(()=>{class e extends gf{_location;_history;_doc=h(me);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return yt().getBaseHref(this._doc)}onPopState(n){let r=yt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=yt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Nv(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function Sv(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function Vn(e){return e&&e[0]!=="?"?`?${e}`:e}var io=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>h(Rv),providedIn:"root"})}return e})(),xv=new b(""),Rv=(()=>{class e extends io{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??h(me).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Nv(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+Vn(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+Vn(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+Vn(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(M(gf),M(xv,8))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),so=(()=>{class e{_subject=new ce;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=aI(Sv(Tv(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+Vn(r))}normalize(n){return e.stripTrailingSlash(sI(this._basePath,Tv(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Vn(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+Vn(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=Vn;static joinWithSlash=Nv;static stripTrailingSlash=Sv;static \u0275fac=function(r){return new(r||e)(M(io))};static \u0275prov=I({token:e,factory:()=>iI(),providedIn:"root"})}return e})();function iI(){return new so(M(io))}function sI(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Tv(e){return e.replace(/\/index.html$/,"")}function aI(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var mf=/\s+/,Ov=[],vf=(()=>{class e{_ngEl;_renderer;initialClasses=Ov;rawClass;stateMap=new Map;constructor(n,r){this._ngEl=n,this._renderer=r}set klass(n){this.initialClasses=n!=null?n.trim().split(mf):Ov}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(mf):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(mf).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(T(ot),T(fn))};static \u0275dir=ve({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var yf=(()=>{class e{_viewContainer;_context=new sc;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(n,r){this._viewContainer=n,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){kv(n,!1),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){kv(n,!1),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(n,r){return!0}static \u0275fac=function(r){return new(r||e)(T(On),T(ur))};static \u0275dir=ve({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),sc=class{$implicit=null;ngIf=null};function kv(e,t){if(e&&!e.createEmbeddedView)throw new _(2020,!1)}var Df=(()=>{class e{_ngEl;_differs;_renderer;_ngStyle=null;_differ=null;constructor(n,r,o){this._ngEl=n,this._differs=r,this._renderer=o}set ngStyle(n){this._ngStyle=n,!this._differ&&n&&(this._differ=this._differs.find(n).create())}ngDoCheck(){if(this._differ){let n=this._differ.diff(this._ngStyle);n&&this._applyChanges(n)}}_setStyle(n,r){let[o,i]=n.split("."),s=o.indexOf("-")===-1?void 0:At.DashCase;r!=null?this._renderer.setStyle(this._ngEl.nativeElement,o,i?`${r}${i}`:r,s):this._renderer.removeStyle(this._ngEl.nativeElement,o,s)}_applyChanges(n){n.forEachRemovedItem(r=>this._setStyle(r.key,null)),n.forEachAddedItem(r=>this._setStyle(r.key,r.currentValue)),n.forEachChangedItem(r=>this._setStyle(r.key,r.currentValue))}static \u0275fac=function(r){return new(r||e)(T(ot),T(pf),T(fn))};static \u0275dir=ve({type:e,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"}})}return e})();var J=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=zt({type:e});static \u0275inj=wt({})}return e})();function Ni(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var vr=class{};var Pv="browser";var lc=new b(""),bf=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new _(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(M(lc),M(ne))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),xi=class{_doc;constructor(t){this._doc=t}manager},Ef="ng-app-id";function Fv(e){for(let t of e)t.remove()}function Lv(e,t){let n=t.createElement("style");return n.textContent=e,n}function lI(e,t,n,r){let o=e.head?.querySelectorAll(`style[${Ef}="${t}"],link[${Ef}="${t}"]`);if(o)for(let i of o)i.removeAttribute(Ef),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function Cf(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var If=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,lI(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,Lv);r?.forEach(o=>this.addUsage(o,this.external,Cf))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(Fv(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])Fv(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,Lv(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,Cf(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(M(me),M(Ua),M($a,8),M(to))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),_f={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Sf=/%COMP%/g;var jv="%COMP%",uI=`_nghost-${jv}`,dI=`_ngcontent-${jv}`,fI=!0,pI=new b("",{providedIn:"root",factory:()=>fI});function hI(e){return dI.replace(Sf,e)}function gI(e){return uI.replace(Sf,e)}function Bv(e,t){return t.map(n=>n.replace(Sf,e))}var Tf=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,l=null,u=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.tracingService=u,this.platformIsServer=!1,this.defaultRenderer=new Ri(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(n,r);return o instanceof cc?o.applyToHost(n):o instanceof Oi&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer,p=this.tracingService;switch(r.encapsulation){case un.Emulated:i=new cc(c,l,r,this.appId,u,s,a,d,p);break;case un.ShadowDom:return new wf(c,l,n,r,s,a,this.nonce,d,p);default:i=new Oi(c,l,r,u,s,a,d,p);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(M(bf),M(If),M(Ua),M(pI),M(me),M(to),M(ne),M($a),M(Si,8))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),Ri=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(_f[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Vv(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Vv(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new _(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=_f[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=_f[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(At.DashCase|At.Important)?t.style.setProperty(n,r,o&At.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&At.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=yt().getGlobalEventTarget(this.doc,t),!t))throw new _(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;t(n)===!1&&n.preventDefault()}}};function Vv(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var wf=class extends Ri{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,c,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=o.styles;u=Bv(o.id,u);for(let p of u){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=p,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let p of d){let f=Cf(p,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Oi=class extends Ri{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let u=r.styles;this.styles=l?Bv(l,u):u,this.styleUrls=r.getExternalStyles?.(l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&Za.size===0&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},cc=class extends Oi{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,l){let u=o+"-"+r.id;super(t,n,r,i,s,a,c,l,u),this.contentAttr=hI(u),this.hostAttr=gI(u)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var uc=class e extends Ai{supportsDOMEvents=!0;static makeCurrent(){hf(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=mI();return n==null?null:vI(n)}resetBaseElement(){ki=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Ni(document.cookie,t)}},ki=null;function mI(){return ki=ki||document.head.querySelector("base"),ki?ki.getAttribute("href"):null}function vI(e){return new URL(e,document.baseURI).pathname}var yI=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),Hv=(()=>{class e extends xi{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(M(me))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),Uv=["alt","control","meta","shift"],DI={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},EI={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},$v=(()=>{class e extends xi{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>yt().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Uv.forEach(l=>{let u=r.indexOf(l);u>-1&&(r.splice(u,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=DI[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Uv.forEach(s=>{if(s!==o){let a=EI[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(M(me))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();function Mf(e,t,n){let r=D({rootComponent:e,platformRef:n?.platformRef},_I(t));return Iv(r)}function _I(e){return{appProviders:[...SI,...e?.providers??[]],platformProviders:II}}function CI(){uc.makeCurrent()}function wI(){return new Ft}function bI(){return Md(document),document}var II=[{provide:to,useValue:Pv},{provide:Ha,useValue:CI,multi:!0},{provide:me,useFactory:bI}];var SI=[{provide:Yo,useValue:"root"},{provide:Ft,useFactory:wI},{provide:lc,useClass:Hv,multi:!0,deps:[me]},{provide:lc,useClass:$v,multi:!0,deps:[me]},Tf,If,bf,{provide:dr,useExisting:Tf},{provide:vr,useClass:yI},[]];var co=class{},lo=class{},Ae=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(t){t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=n.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.addHeaderEntry(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}addHeaderEntry(t,n){let r=t.toLowerCase();this.maybeSetNormalizedName(t,r),this.headers.has(r)?this.headers.get(r).push(n):this.headers.set(r,[n])}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var pc=class{encodeKey(t){return zv(t)}encodeValue(t){return zv(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function TI(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var MI=/%(\d[a-f0-9])/gi,AI={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function zv(e){return encodeURIComponent(e).replace(MI,(t,n)=>AI[n]??t)}function dc(e){return`${e}`}var pn=class e{map;encoder;updates=null;cloneFrom=null;constructor(t={}){if(this.encoder=t.encoder||new pc,t.fromString){if(t.fromObject)throw new _(2805,!1);this.map=TI(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(dc):[dc(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(dc(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(dc(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var hc=class{map=new Map;set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function NI(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Gv(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function Wv(e){return typeof Blob<"u"&&e instanceof Blob}function qv(e){return typeof FormData<"u"&&e instanceof FormData}function xI(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Pi="Content-Type",gc="Accept",Of="X-Request-URL",Yv="text/plain",Qv="application/json",Kv=`${Qv}, ${Yv}, */*`,ao=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;credentials;keepalive=!1;cache;priority;mode;redirect;referrer;integrity;responseType="json";method;params;urlWithParams;transferCache;timeout;constructor(t,n,r,o){this.url=n,this.method=t.toUpperCase();let i;if(NI(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i){if(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,this.keepalive=!!i.keepalive,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),i.priority&&(this.priority=i.priority),i.cache&&(this.cache=i.cache),i.credentials&&(this.credentials=i.credentials),typeof i.timeout=="number"){if(i.timeout<1||!Number.isInteger(i.timeout))throw new _(2822,"");this.timeout=i.timeout}i.mode&&(this.mode=i.mode),i.redirect&&(this.redirect=i.redirect),i.integrity&&(this.integrity=i.integrity),i.referrer&&(this.referrer=i.referrer),this.transferCache=i.transferCache}if(this.headers??=new Ae,this.context??=new hc,!this.params)this.params=new pn,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Gv(this.body)||Wv(this.body)||qv(this.body)||xI(this.body)?this.body:this.body instanceof pn?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||qv(this.body)?null:Wv(this.body)?this.body.type||null:Gv(this.body)?null:typeof this.body=="string"?Yv:this.body instanceof pn?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?Qv:null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.keepalive??this.keepalive,s=t.priority||this.priority,a=t.cache||this.cache,c=t.mode||this.mode,l=t.redirect||this.redirect,u=t.credentials||this.credentials,d=t.referrer||this.referrer,p=t.integrity||this.integrity,f=t.transferCache??this.transferCache,g=t.timeout??this.timeout,S=t.body!==void 0?t.body:this.body,C=t.withCredentials??this.withCredentials,E=t.reportProgress??this.reportProgress,se=t.headers||this.headers,Be=t.params||this.params,De=t.context??this.context;return t.setHeaders!==void 0&&(se=Object.keys(t.setHeaders).reduce((kt,Xt)=>kt.set(Xt,t.setHeaders[Xt]),se)),t.setParams&&(Be=Object.keys(t.setParams).reduce((kt,Xt)=>kt.set(Xt,t.setParams[Xt]),Be)),new e(n,r,S,{params:Be,headers:se,context:De,reportProgress:E,responseType:o,withCredentials:C,transferCache:f,keepalive:i,cache:a,priority:s,timeout:g,mode:c,redirect:l,credentials:u,referrer:d,integrity:p})}},hn=(function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e})(hn||{}),uo=class{headers;status;statusText;url;ok;type;redirected;constructor(t,n=200,r="OK"){this.headers=t.headers||new Ae,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.redirected=t.redirected,this.ok=this.status>=200&&this.status<300}},Fi=class e extends uo{constructor(t={}){super(t)}type=hn.ResponseHeader;clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},fo=class e extends uo{body;constructor(t={}){super(t),this.body=t.body!==void 0?t.body:null}type=hn.Response;clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0,redirected:t.redirected??this.redirected})}},qt=class extends uo{name="HttpErrorResponse";message;error;ok=!1;constructor(t){super(t,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},Jv=200,RI=204;function Af(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,credentials:e.credentials,transferCache:e.transferCache,timeout:e.timeout,keepalive:e.keepalive,priority:e.priority,cache:e.cache,mode:e.mode,redirect:e.redirect,integrity:e.integrity,referrer:e.referrer}}var vc=(()=>{class e{handler;constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof ao)i=n;else{let c;o.headers instanceof Ae?c=o.headers:c=new Ae(o.headers);let l;o.params&&(o.params instanceof pn?l=o.params:l=new pn({fromObject:o.params})),i=new ao(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:l,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache,keepalive:o.keepalive,priority:o.priority,cache:o.cache,mode:o.mode,redirect:o.redirect,credentials:o.credentials,referrer:o.referrer,integrity:o.integrity,timeout:o.timeout})}let s=P(i).pipe(_n(c=>this.handler.handle(c)));if(n instanceof ao||o.observe==="events")return s;let a=s.pipe(Ze(c=>c instanceof fo));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(O(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new _(2806,!1);return c.body}));case"blob":return a.pipe(O(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new _(2807,!1);return c.body}));case"text":return a.pipe(O(c=>{if(c.body!==null&&typeof c.body!="string")throw new _(2808,!1);return c.body}));case"json":default:return a.pipe(O(c=>c.body))}case"response":return a;default:throw new _(2809,!1)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new pn().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,Af(o,r))}post(n,r,o={}){return this.request("POST",n,Af(o,r))}put(n,r,o={}){return this.request("PUT",n,Af(o,r))}static \u0275fac=function(r){return new(r||e)(M(co))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),OI=/^\)\]\}',?\n/;function Zv(e){if(e.url)return e.url;let t=Of.toLocaleLowerCase();return e.headers.get(t)}var Xv=new b(""),fc=(()=>{class e{fetchImpl=h(Nf,{optional:!0})?.fetch??((...n)=>globalThis.fetch(...n));ngZone=h(ne);destroyRef=h(Ut);destroyed=!1;constructor(){this.destroyRef.onDestroy(()=>{this.destroyed=!0})}handle(n){return new z(r=>{let o=new AbortController;this.doRequest(n,o.signal,r).then(xf,s=>r.error(new qt({error:s})));let i;return n.timeout&&(i=this.ngZone.runOutsideAngular(()=>setTimeout(()=>{o.signal.aborted||o.abort(new DOMException("signal timed out","TimeoutError"))},n.timeout))),()=>{i!==void 0&&clearTimeout(i),o.abort()}})}async doRequest(n,r,o){let i=this.createRequestInit(n),s;try{let g=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,D({signal:r},i)));kI(g),o.next({type:hn.Sent}),s=await g}catch(g){o.error(new qt({error:g,status:g.status??0,statusText:g.statusText,url:n.urlWithParams,headers:g.headers}));return}let a=new Ae(s.headers),c=s.statusText,l=Zv(s)??n.urlWithParams,u=s.status,d=null;if(n.reportProgress&&o.next(new Fi({headers:a,status:u,statusText:c,url:l})),s.body){let g=s.headers.get("content-length"),S=[],C=s.body.getReader(),E=0,se,Be,De=typeof Zone<"u"&&Zone.current,kt=!1;if(await this.ngZone.runOutsideAngular(async()=>{for(;;){if(this.destroyed){await C.cancel(),kt=!0;break}let{done:Gn,value:al}=await C.read();if(Gn)break;if(S.push(al),E+=al.length,n.reportProgress){Be=n.responseType==="text"?(Be??"")+(se??=new TextDecoder).decode(al,{stream:!0}):void 0;let vp=()=>o.next({type:hn.DownloadProgress,total:g?+g:void 0,loaded:E,partialText:Be});De?De.run(vp):vp()}}}),kt){o.complete();return}let Xt=this.concatChunks(S,E);try{let Gn=s.headers.get(Pi)??"";d=this.parseBody(n,Xt,Gn,u)}catch(Gn){o.error(new qt({error:Gn,headers:new Ae(s.headers),status:s.status,statusText:s.statusText,url:Zv(s)??n.urlWithParams}));return}}u===0&&(u=d?Jv:0);let p=u>=200&&u<300,f=s.redirected;p?(o.next(new fo({body:d,headers:a,status:u,statusText:c,url:l,redirected:f})),o.complete()):o.error(new qt({error:d,headers:a,status:u,statusText:c,url:l,redirected:f}))}parseBody(n,r,o,i){switch(n.responseType){case"json":let s=new TextDecoder().decode(r).replace(OI,"");if(s==="")return null;try{return JSON.parse(s)}catch(a){if(i<200||i>=300)return s;throw a}case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},o;if(o=n.credentials,n.withCredentials&&(o="include"),n.headers.forEach((i,s)=>r[i]=s.join(",")),n.headers.has(gc)||(r[gc]=Kv),!n.headers.has(Pi)){let i=n.detectContentTypeHeader();i!==null&&(r[Pi]=i)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:o,keepalive:n.keepalive,cache:n.cache,priority:n.priority,mode:n.mode,redirect:n.redirect,referrer:n.referrer,integrity:n.integrity}}concatChunks(n,r){let o=new Uint8Array(r),i=0;for(let s of n)o.set(s,i),i+=s.length;return o}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),Nf=class{};function xf(){}function kI(e){e.then(xf,xf)}function PI(e,t){return t(e)}function FI(e,t,n){return(r,o)=>Re(n,()=>t(r,i=>e(i,o)))}var ey=new b(""),ty=new b(""),ny=new b("",{providedIn:"root",factory:()=>!0});var mc=(()=>{class e extends co{backend;injector;chain=null;pendingTasks=h(pa);contributeToStability=h(ny);constructor(n,r){super(),this.backend=n,this.injector=r}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(ey),...this.injector.get(ty,[])]));this.chain=r.reduceRight((o,i)=>FI(o,i,this.injector),PI)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(Yn(r))}else return this.chain(n,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(M(lo),M(pe))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();var LI=/^\)\]\}',?\n/,VI=RegExp(`^${Of}:`,"m");function jI(e){return"responseURL"in e&&e.responseURL?e.responseURL:VI.test(e.getAllResponseHeaders())?e.getResponseHeader(Of):null}var Rf=(()=>{class e{xhrFactory;constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new _(-2800,!1);let r=this.xhrFactory;return P(null).pipe(Le(()=>new z(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((C,E)=>s.setRequestHeader(C,E.join(","))),n.headers.has(gc)||s.setRequestHeader(gc,Kv),!n.headers.has(Pi)){let C=n.detectContentTypeHeader();C!==null&&s.setRequestHeader(Pi,C)}if(n.timeout&&(s.timeout=n.timeout),n.responseType){let C=n.responseType.toLowerCase();s.responseType=C!=="json"?C:"text"}let a=n.serializeBody(),c=null,l=()=>{if(c!==null)return c;let C=s.statusText||"OK",E=new Ae(s.getAllResponseHeaders()),se=jI(s)||n.url;return c=new Fi({headers:E,status:s.status,statusText:C,url:se}),c},u=()=>{let{headers:C,status:E,statusText:se,url:Be}=l(),De=null;E!==RI&&(De=typeof s.response>"u"?s.responseText:s.response),E===0&&(E=De?Jv:0);let kt=E>=200&&E<300;if(n.responseType==="json"&&typeof De=="string"){let Xt=De;De=De.replace(LI,"");try{De=De!==""?JSON.parse(De):null}catch(Gn){De=Xt,kt&&(kt=!1,De={error:Gn,text:De})}}kt?(i.next(new fo({body:De,headers:C,status:E,statusText:se,url:Be||void 0})),i.complete()):i.error(new qt({error:De,headers:C,status:E,statusText:se,url:Be||void 0}))},d=C=>{let{url:E}=l(),se=new qt({error:C,status:s.status||0,statusText:s.statusText||"Unknown Error",url:E||void 0});i.error(se)},p=d;n.timeout&&(p=C=>{let{url:E}=l(),se=new qt({error:new DOMException("Request timed out","TimeoutError"),status:s.status||0,statusText:s.statusText||"Request timeout",url:E||void 0});i.error(se)});let f=!1,g=C=>{f||(i.next(l()),f=!0);let E={type:hn.DownloadProgress,loaded:C.loaded};C.lengthComputable&&(E.total=C.total),n.responseType==="text"&&s.responseText&&(E.partialText=s.responseText),i.next(E)},S=C=>{let E={type:hn.UploadProgress,loaded:C.loaded};C.lengthComputable&&(E.total=C.total),i.next(E)};return s.addEventListener("load",u),s.addEventListener("error",d),s.addEventListener("timeout",p),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",g),a!==null&&s.upload&&s.upload.addEventListener("progress",S)),s.send(a),i.next({type:hn.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",u),s.removeEventListener("timeout",p),n.reportProgress&&(s.removeEventListener("progress",g),a!==null&&s.upload&&s.upload.removeEventListener("progress",S)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(M(vr))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})(),ry=new b(""),BI="XSRF-TOKEN",UI=new b("",{providedIn:"root",factory:()=>BI}),HI="X-XSRF-TOKEN",$I=new b("",{providedIn:"root",factory:()=>HI}),Li=class{},zI=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(n,r){this.doc=n,this.cookieName=r}getToken(){let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Ni(n,this.cookieName),this.lastCookieString=n),this.lastToken}static \u0275fac=function(r){return new(r||e)(M(me),M(UI))};static \u0275prov=I({token:e,factory:e.\u0275fac})}return e})();function GI(e,t){let n=e.url.toLowerCase();if(!h(ry)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=h(Li).getToken(),o=h($I);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var kf=(function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e})(kf||{});function WI(e,t){return{\u0275kind:e,\u0275providers:t}}function Pf(...e){let t=[vc,Rf,mc,{provide:co,useExisting:mc},{provide:lo,useFactory:()=>h(Xv,{optional:!0})??h(Rf)},{provide:ey,useValue:GI,multi:!0},{provide:ry,useValue:!0},{provide:Li,useClass:zI}];for(let n of e)t.push(...n.\u0275providers);return cn(t)}function Ff(){return WI(kf.Fetch,[fc,{provide:Xv,useExisting:fc},{provide:lo,useExisting:fc}])}var oy=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(M(me))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var U="primary",Ji=Symbol("RouteTitle"),Uf=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function Er(e){return new Uf(e)}function fy(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function YI(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!Zt(e[n],t[n]))return!1;return!0}function Zt(e,t){let n=e?Hf(e):void 0,r=t?Hf(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!py(e[o],t[o]))return!1;return!0}function Hf(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function py(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function hy(e){return e.length>0?e[e.length-1]:null}function vn(e){return Il(e)?e:Pn(e)?ae(Promise.resolve(e)):P(e)}var QI={exact:my,subset:vy},gy={exact:KI,subset:JI,ignored:()=>!0};function iy(e,t,n){return QI[n.paths](e.root,t.root,n.matrixParams)&&gy[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function KI(e,t){return Zt(e,t)}function my(e,t,n){if(!yr(e.segments,t.segments)||!Ec(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!my(e.children[r],t.children[r],n))return!1;return!0}function JI(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>py(e[n],t[n]))}function vy(e,t,n){return yy(e,t,t.segments,n)}function yy(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!yr(o,n)||t.hasChildren()||!Ec(o,n,r))}else if(e.segments.length===n.length){if(!yr(e.segments,n)||!Ec(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!vy(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!yr(e.segments,o)||!Ec(e.segments,o,r)||!e.children[U]?!1:yy(e.children[U],t,i,r)}}function Ec(e,t,n){return t.every((r,o)=>gy[n](e[o].parameters,r.parameters))}var Qt=class{root;queryParams;fragment;_queryParamMap;constructor(t=new Q([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Er(this.queryParams),this._queryParamMap}toString(){return t0.serialize(this)}},Q=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return _c(this)}},jn=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=Er(this.parameters),this._parameterMap}toString(){return Ey(this)}};function XI(e,t){return yr(e,t)&&e.every((n,r)=>Zt(n.parameters,t[r].parameters))}function yr(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function e0(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===U&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==U&&(n=n.concat(t(o,r)))}),n}var Xi=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>new _r,providedIn:"root"})}return e})(),_r=class{parse(t){let n=new zf(t);return new Qt(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${Vi(t.root,!0)}`,r=o0(t.queryParams),o=typeof t.fragment=="string"?`#${n0(t.fragment)}`:"";return`${n}${r}${o}`}},t0=new _r;function _c(e){return e.segments.map(t=>Ey(t)).join("/")}function Vi(e,t){if(!e.hasChildren())return _c(e);if(t){let n=e.children[U]?Vi(e.children[U],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==U&&r.push(`${o}:${Vi(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=e0(e,(r,o)=>o===U?[Vi(e.children[U],!1)]:[`${o}:${Vi(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[U]!=null?`${_c(e)}/${n[0]}`:`${_c(e)}/(${n.join("//")})`}}function Dy(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function yc(e){return Dy(e).replace(/%3B/gi,";")}function n0(e){return encodeURI(e)}function $f(e){return Dy(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Cc(e){return decodeURIComponent(e)}function sy(e){return Cc(e.replace(/\+/g,"%20"))}function Ey(e){return`${$f(e.path)}${r0(e.parameters)}`}function r0(e){return Object.entries(e).map(([t,n])=>`;${$f(t)}=${$f(n)}`).join("")}function o0(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${yc(n)}=${yc(o)}`).join("&"):`${yc(n)}=${yc(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var i0=/^[^\/()?;#]+/;function Lf(e){let t=e.match(i0);return t?t[0]:""}var s0=/^[^\/()?;=#]+/;function a0(e){let t=e.match(s0);return t?t[0]:""}var c0=/^[^=?&#]+/;function l0(e){let t=e.match(c0);return t?t[0]:""}var u0=/^[^&#]+/;function d0(e){let t=e.match(u0);return t?t[0]:""}var zf=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new Q([],{}):new Q([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[U]=new Q(t,n)),r}parseSegment(){let t=Lf(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new _(4009,!1);return this.capture(t),new jn(Cc(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=a0(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=Lf(this.remaining);o&&(r=o,this.capture(r))}t[Cc(n)]=Cc(r)}parseQueryParam(t){let n=l0(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=d0(this.remaining);s&&(r=s,this.capture(r))}let o=sy(n),i=sy(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Lf(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new _(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=U);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[U]:new Q([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new _(4011,!1)}};function _y(e){return e.segments.length>0?new Q([],{[U]:e}):e}function Cy(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=Cy(o);if(r===U&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new Q(e.segments,t);return f0(n)}function f0(e){if(e.numberOfChildren===1&&e.children[U]){let t=e.children[U];return new Q(e.segments.concat(t.segments),t.children)}return e}function Bn(e){return e instanceof Qt}function wy(e,t,n=null,r=null){let o=by(e);return Iy(o,t,n,r)}function by(e){let t;function n(i){let s={};for(let c of i.children){let l=n(c);s[c.outlet]=l}let a=new Q(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=_y(r);return t??o}function Iy(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return Vf(o,o,o,n,r);let i=p0(t);if(i.toRoot())return Vf(o,o,new Q([],{}),n,r);let s=h0(i,o,e),a=s.processChildren?Bi(s.segmentGroup,s.index,i.commands):Ty(s.segmentGroup,s.index,i.commands);return Vf(o,s.segmentGroup,a,n,r)}function wc(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function $i(e){return typeof e=="object"&&e!=null&&e.outlets}function Vf(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(u=>`${u}`):`${l}`});let s;e===t?s=n:s=Sy(e,t,n);let a=_y(Cy(s));return new Qt(a,i,o)}function Sy(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=Sy(i,t,n)}),new Q(e.segments,r)}var bc=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&wc(r[0]))throw new _(4003,!1);let o=r.find($i);if(o&&o!==hy(r))throw new _(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function p0(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new bc(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new bc(n,t,r)}var go=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function h0(e,t,n){if(e.isAbsolute)return new go(t,!0,0);if(!n)return new go(t,!1,NaN);if(n.parent===null)return new go(n,!0,0);let r=wc(e.commands[0])?0:1,o=n.segments.length-1+r;return g0(n,o,e.numberOfDoubleDots)}function g0(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new _(4005,!1);o=r.segments.length}return new go(r,!1,o-i)}function m0(e){return $i(e[0])?e[0].outlets:{[U]:e}}function Ty(e,t,n){if(e??=new Q([],{}),e.segments.length===0&&e.hasChildren())return Bi(e,t,n);let r=v0(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new Q(e.segments.slice(0,r.pathIndex),{});return i.children[U]=new Q(e.segments.slice(r.pathIndex),e.children),Bi(i,0,o)}else return r.match&&o.length===0?new Q(e.segments,{}):r.match&&!e.hasChildren()?Gf(e,t,n):r.match?Bi(e,0,o):Gf(e,t,n)}function Bi(e,t,n){if(n.length===0)return new Q(e.segments,{});{let r=m0(n),o={};if(Object.keys(r).some(i=>i!==U)&&e.children[U]&&e.numberOfChildren===1&&e.children[U].segments.length===0){let i=Bi(e.children[U],t,n);return new Q(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Ty(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new Q(e.segments,o)}}function v0(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if($i(a))break;let c=`${a}`,l=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!cy(c,l,s))return i;r+=2}else{if(!cy(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function Gf(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if($i(i)){let c=y0(i.outlets);return new Q(r,c)}if(o===0&&wc(n[0])){let c=e.segments[t];r.push(new jn(c.path,ay(n[0]))),o++;continue}let s=$i(i)?i.outlets[U]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&wc(a)?(r.push(new jn(s,ay(a))),o+=2):(r.push(new jn(s,{})),o++)}return new Q(r,{})}function y0(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=Gf(new Q([],{}),0,r))}),t}function ay(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function cy(e,t,n){return e==n.path&&Zt(t,n.parameters)}var Ui="imperative",Ne=(function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e})(Ne||{}),ut=class{id;url;constructor(t,n){this.id=t,this.url=n}},Cr=class extends ut{type=Ne.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Kt=class extends ut{urlAfterRedirects;type=Ne.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Ge=(function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e})(Ge||{}),zi=(function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e})(zi||{}),Yt=class extends ut{reason;code;type=Ne.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},gn=class extends ut{reason;code;type=Ne.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},vo=class extends ut{error;target;type=Ne.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Gi=class extends ut{urlAfterRedirects;state;type=Ne.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ic=class extends ut{urlAfterRedirects;state;type=Ne.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Sc=class extends ut{urlAfterRedirects;state;shouldActivate;type=Ne.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Tc=class extends ut{urlAfterRedirects;state;type=Ne.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Mc=class extends ut{urlAfterRedirects;state;type=Ne.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ac=class{route;type=Ne.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Nc=class{route;type=Ne.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},xc=class{snapshot;type=Ne.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Rc=class{snapshot;type=Ne.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Oc=class{snapshot;type=Ne.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},kc=class{snapshot;type=Ne.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Wi=class{},yo=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function D0(e){return!(e instanceof Wi)&&!(e instanceof yo)}function E0(e,t){return e.providers&&!e._injector&&(e._injector=Ii(e.providers,t,`Route: ${e.path}`)),e._injector??t}function Ot(e){return e.outlet||U}function _0(e,t){let n=e.filter(r=>Ot(r)===t);return n.push(...e.filter(r=>Ot(r)!==t)),n}function _o(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var Pc=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return _o(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new Co(this.rootInjector)}},Co=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new Pc(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(M(pe))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Fc=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Wf(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Wf(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=qf(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return qf(t,this._root).map(n=>n.value)}};function Wf(e,t){if(e===t.value)return t;for(let n of t.children){let r=Wf(e,n);if(r)return r}return null}function qf(e,t){if(e===t.value)return[t];for(let n of t.children){let r=qf(e,n);if(r.length)return r.unshift(t),r}return[]}var lt=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function ho(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var qi=class extends Fc{snapshot;constructor(t,n){super(t),this.snapshot=n,tp(this,t)}toString(){return this.snapshot.toString()}};function My(e){let t=C0(e),n=new Ee([new jn("",{})]),r=new Ee({}),o=new Ee({}),i=new Ee({}),s=new Ee(""),a=new mn(n,r,i,s,o,U,e,t.root);return a.snapshot=t.root,new qi(new lt(a,[]),t)}function C0(e){let t={},n={},r={},i=new Dr([],t,r,"",n,U,e,null,{});return new Zi("",new lt(i,[]))}var mn=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(O(l=>l[Ji]))??P(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(O(t=>Er(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(O(t=>Er(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Lc(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:D(D({},t.params),e.params),data:D(D({},t.data),e.data),resolve:D(D(D(D({},e.data),t.data),o?.data),e._resolvedData)}:r={params:D({},e.params),data:D({},e.data),resolve:D(D({},e.data),e._resolvedData??{})},o&&Ny(o)&&(r.resolve[Ji]=o.title),r}var Dr=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Ji]}constructor(t,n,r,o,i,s,a,c,l){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Er(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Er(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},Zi=class extends Fc{url;constructor(t,n){super(n),this.url=t,tp(this,n)}toString(){return Ay(this._root)}};function tp(e,t){t.value._routerState=e,t.children.forEach(n=>tp(e,n))}function Ay(e){let t=e.children.length>0?` { ${e.children.map(Ay).join(", ")} } `:"";return`${e.value}${t}`}function jf(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,Zt(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),Zt(t.params,n.params)||e.paramsSubject.next(n.params),YI(t.url,n.url)||e.urlSubject.next(n.url),Zt(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Zf(e,t){let n=Zt(e.params,t.params)&&XI(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Zf(e.parent,t.parent))}function Ny(e){return typeof e.title=="string"||e.title===null}var xy=new b(""),es=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=U;activateEvents=new k;deactivateEvents=new k;attachEvents=new k;detachEvents=new k;routerOutletData=bv(void 0);parentContexts=h(Co);location=h(On);changeDetector=h(Ln);inputBinder=h(Uc,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new _(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new _(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new _(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new _(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Yf(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=ve({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[rt]})}return e})(),Yf=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===mn?this.route:t===Co?this.childContexts:t===xy?this.outletData:this.parent.get(t,n)}},Uc=new b("");var np=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=Z({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Se(0,"router-outlet")},dependencies:[es],encapsulation:2})}return e})();function rp(e){let t=e.children&&e.children.map(rp),n=t?G(D({},e),{children:t}):D({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==U&&(n.component=np),n}function w0(e,t,n){let r=Yi(e,t._root,n?n._root:void 0);return new qi(r,t)}function Yi(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=b0(e,t,n);return new lt(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>Yi(e,a)),s}}let r=I0(t.value),o=t.children.map(i=>Yi(e,i));return new lt(r,o)}}function b0(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Yi(e,r,o);return Yi(e,r)})}function I0(e){return new mn(new Ee(e.url),new Ee(e.params),new Ee(e.queryParams),new Ee(e.fragment),new Ee(e.data),e.outlet,e.component,e)}var Do=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},Ry="ngNavigationCancelingError";function Vc(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=Bn(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Oy(!1,Ge.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function Oy(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[Ry]=!0,n.cancellationCode=t,n}function S0(e){return ky(e)&&Bn(e.url)}function ky(e){return!!e&&e[Ry]}var T0=(e,t,n,r)=>O(o=>(new Qf(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),Qf=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),jf(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=ho(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=ho(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=ho(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=ho(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new kc(i.value.snapshot))}),t.children.length&&this.forwardEvent(new Rc(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(jf(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),jf(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},jc=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},mo=class{component;route;constructor(t,n){this.component=t,this.route=n}};function M0(e,t,n){let r=e._root,o=t?t._root:null;return ji(r,o,n,[r.value])}function A0(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function wo(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!$l(e)?e:t.get(e):r}function ji(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=ho(t);return e.children.forEach(s=>{N0(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Hi(a,n.getContext(s),o)),o}function N0(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=x0(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new jc(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?ji(e,t,a?a.children:null,r,o):ji(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new mo(a.outlet.component,s))}else s&&Hi(t,a,o),o.canActivateChecks.push(new jc(r)),i.component?ji(e,null,a?a.children:null,r,o):ji(e,null,n,r,o);return o}function x0(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!yr(e.url,t.url);case"pathParamsOrQueryParamsChange":return!yr(e.url,t.url)||!Zt(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Zf(e,t)||!Zt(e.queryParams,t.queryParams);case"paramsChange":default:return!Zf(e,t)}}function Hi(e,t,n){let r=ho(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?Hi(s,t.children.getContext(i),n):Hi(s,null,n):Hi(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new mo(t.outlet.component,o)):n.canDeactivateChecks.push(new mo(null,o)):n.canDeactivateChecks.push(new mo(null,o))}function ts(e){return typeof e=="function"}function R0(e){return typeof e=="boolean"}function O0(e){return e&&ts(e.canLoad)}function k0(e){return e&&ts(e.canActivate)}function P0(e){return e&&ts(e.canActivateChild)}function F0(e){return e&&ts(e.canDeactivate)}function L0(e){return e&&ts(e.canMatch)}function Py(e){return e instanceof en||e?.name==="EmptyError"}var Dc=Symbol("INITIAL_VALUE");function Eo(){return Le(e=>zs(e.map(t=>t.pipe(tn(1),Al(Dc)))).pipe(O(t=>{for(let n of t)if(n!==!0){if(n===Dc)return Dc;if(n===!1||V0(n))return n}return!0}),Ze(t=>t!==Dc),tn(1)))}function V0(e){return Bn(e)||e instanceof Do}function j0(e,t){return _e(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?P(G(D({},n),{guardsResult:!0})):B0(s,r,o,e).pipe(_e(a=>a&&R0(a)?U0(r,i,e,t):P(a)),O(a=>G(D({},n),{guardsResult:a})))})}function B0(e,t,n,r){return ae(e).pipe(_e(o=>W0(o.component,o.route,n,t,r)),nn(o=>o!==!0,!0))}function U0(e,t,n,r){return ae(t).pipe(_n(o=>Vr($0(o.route.parent,r),H0(o.route,r),G0(e,o.path,n),z0(e,o.route,n))),nn(o=>o!==!0,!0))}function H0(e,t){return e!==null&&t&&t(new Oc(e)),P(!0)}function $0(e,t){return e!==null&&t&&t(new xc(e)),P(!0)}function z0(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return P(!0);let o=r.map(i=>Ho(()=>{let s=_o(t)??n,a=wo(i,s),c=k0(a)?a.canActivate(t,e):Re(s,()=>a(t,e));return vn(c).pipe(nn())}));return P(o).pipe(Eo())}function G0(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>A0(s)).filter(s=>s!==null).map(s=>Ho(()=>{let a=s.guards.map(c=>{let l=_o(s.node)??n,u=wo(c,l),d=P0(u)?u.canActivateChild(r,e):Re(l,()=>u(r,e));return vn(d).pipe(nn())});return P(a).pipe(Eo())}));return P(i).pipe(Eo())}function W0(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return P(!0);let s=i.map(a=>{let c=_o(t)??o,l=wo(a,c),u=F0(l)?l.canDeactivate(e,t,n,r):Re(c,()=>l(e,t,n,r));return vn(u).pipe(nn())});return P(s).pipe(Eo())}function q0(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return P(!0);let i=o.map(s=>{let a=wo(s,e),c=O0(a)?a.canLoad(t,n):Re(e,()=>a(t,n));return vn(c)});return P(i).pipe(Eo(),Fy(r))}function Fy(e){return _l(Ie(t=>{if(typeof t!="boolean")throw Vc(e,t)}),O(t=>t===!0))}function Z0(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return P(!0);let i=o.map(s=>{let a=wo(s,e),c=L0(a)?a.canMatch(t,n):Re(e,()=>a(t,n));return vn(c)});return P(i).pipe(Eo(),Fy(r))}var Qi=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},Ki=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function po(e){return Lr(new Qi(e))}function Y0(e){return Lr(new _(4e3,!1))}function Q0(e){return Lr(Oy(!1,Ge.GuardRejected))}var Kf=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return P(r);if(o.numberOfChildren>1||!o.children[U])return Y0(`${t.redirectTo}`);o=o.children[U]}}applyRedirectCommands(t,n,r,o,i){return K0(n,o,i).pipe(O(s=>{if(s instanceof Qt)throw new Ki(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),t,r);if(s[0]==="/")throw new Ki(a);return a}))}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new Qt(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new Q(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new _(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}};function K0(e,t,n){if(typeof e=="string")return P(e);let r=e,{queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,params:l,data:u,title:d}=t;return vn(Re(n,()=>r({params:l,data:u,queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,title:d})))}var Jf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function J0(e,t,n,r,o){let i=Ly(e,t,n);return i.matched?(r=E0(t,r),Z0(r,t,n,o).pipe(O(s=>s===!0?i:D({},Jf)))):P(i)}function Ly(e,t,n){if(t.path==="**")return X0(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?D({},Jf):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||fy)(n,e,t);if(!o)return D({},Jf);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?D(D({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function X0(e){return{matched:!0,parameters:e.length>0?hy(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function ly(e,t,n,r){return n.length>0&&nS(e,n,r)?{segmentGroup:new Q(t,tS(r,new Q(n,e.children))),slicedSegments:[]}:n.length===0&&rS(e,n,r)?{segmentGroup:new Q(e.segments,eS(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new Q(e.segments,e.children),slicedSegments:n}}function eS(e,t,n,r){let o={};for(let i of n)if(Hc(e,t,i)&&!r[Ot(i)]){let s=new Q([],{});o[Ot(i)]=s}return D(D({},r),o)}function tS(e,t){let n={};n[U]=t;for(let r of e)if(r.path===""&&Ot(r)!==U){let o=new Q([],{});n[Ot(r)]=o}return n}function nS(e,t,n){return n.some(r=>Hc(e,t,r)&&Ot(r)!==U)}function rS(e,t,n){return n.some(r=>Hc(e,t,r))}function Hc(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function oS(e,t,n){return t.length===0&&!e.children[n]}var Xf=class{};function iS(e,t,n,r,o,i,s="emptyOnly"){return new ep(e,t,n,r,o,s,i).recognize()}var sS=31,ep=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Kf(this.urlSerializer,this.urlTree)}noMatchError(t){return new _(4002,`'${t.segmentGroup}'`)}recognize(){let t=ly(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(O(({children:n,rootSnapshot:r})=>{let o=new lt(r,n),i=new Zi("",o),s=wy(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new Dr([],Object.freeze({}),Object.freeze(D({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),U,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,U,n).pipe(O(r=>({children:r,rootSnapshot:n})),En(r=>{if(r instanceof Ki)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Qi?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(O(s=>s instanceof lt?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return ae(i).pipe(_n(s=>{let a=r.children[s],c=_0(n,s);return this.processSegmentGroup(t,c,a,s,o)}),Ml((s,a)=>(s.push(...a),s)),Cn(null),Tl(),_e(s=>{if(s===null)return po(r);let a=Vy(s);return aS(a),P(a)}))}processSegment(t,n,r,o,i,s,a){return ae(n).pipe(_n(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(En(l=>{if(l instanceof Qi)return P(null);throw l}))),nn(c=>!!c),En(c=>{if(Py(c))return oS(r,o,i)?P(new Xf):po(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return Ot(r)!==s&&(s===U||!Hc(o,i,r))?po(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):po(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:l,consumedSegments:u,positionalParamSegments:d,remainingSegments:p}=Ly(n,o,i);if(!c)return po(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>sS&&(this.allowRedirects=!1));let f=new Dr(i,l,Object.freeze(D({},this.urlTree.queryParams)),this.urlTree.fragment,uy(o),Ot(o),o.component??o._loadedComponent??null,o,dy(o)),g=Lc(f,a,this.paramsInheritanceStrategy);return f.params=Object.freeze(g.params),f.data=Object.freeze(g.data),this.applyRedirects.applyRedirectCommands(u,o.redirectTo,d,f,t).pipe(Le(C=>this.applyRedirects.lineralizeSegments(o,C)),_e(C=>this.processSegment(t,r,n,C.concat(p),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=J0(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(Le(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(Le(({routes:l})=>{let u=r._loadedInjector??t,{parameters:d,consumedSegments:p,remainingSegments:f}=c,g=new Dr(p,d,Object.freeze(D({},this.urlTree.queryParams)),this.urlTree.fragment,uy(r),Ot(r),r.component??r._loadedComponent??null,r,dy(r)),S=Lc(g,s,this.paramsInheritanceStrategy);g.params=Object.freeze(S.params),g.data=Object.freeze(S.data);let{segmentGroup:C,slicedSegments:E}=ly(n,p,f,l);if(E.length===0&&C.hasChildren())return this.processChildren(u,l,C,g).pipe(O(Be=>new lt(g,Be)));if(l.length===0&&E.length===0)return P(new lt(g,[]));let se=Ot(r)===i;return this.processSegment(u,l,C,E,se?U:i,!0,g).pipe(O(Be=>new lt(g,Be instanceof lt?[Be]:[])))}))):po(n)))}getChildConfig(t,n,r){return n.children?P({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?P({routes:n._loadedRoutes,injector:n._loadedInjector}):q0(t,n,r,this.urlSerializer).pipe(_e(o=>o?this.configLoader.loadChildren(t,n).pipe(Ie(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):Q0(n))):P({routes:[],injector:t})}};function aS(e){e.sort((t,n)=>t.value.outlet===U?-1:n.value.outlet===U?1:t.value.outlet.localeCompare(n.value.outlet))}function cS(e){let t=e.value.routeConfig;return t&&t.path===""}function Vy(e){let t=[],n=new Set;for(let r of e){if(!cS(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=Vy(r.children);t.push(new lt(r.value,o))}return t.filter(r=>!n.has(r))}function uy(e){return e.data||{}}function dy(e){return e.resolve||{}}function lS(e,t,n,r,o,i){return _e(s=>iS(e,t,n,r,s.extractedUrl,o,i).pipe(O(({state:a,tree:c})=>G(D({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function uS(e,t){return _e(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return P(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of jy(c))s.add(l);let a=0;return ae(s).pipe(_n(c=>i.has(c)?dS(c,r,e,t):(c.data=Lc(c,c.parent,e).resolve,P(void 0))),Ie(()=>a++),jr(1),_e(c=>a===s.size?P(n):We))})}function jy(e){let t=e.children.map(n=>jy(n)).flat();return[e,...t]}function dS(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!Ny(o)&&(i[Ji]=o.title),Ho(()=>(e.data=Lc(e,e.parent,n).resolve,fS(i,e,t,r).pipe(O(s=>(e._resolvedData=s,e.data=D(D({},e.data),s),null)))))}function fS(e,t,n,r){let o=Hf(e);if(o.length===0)return P({});let i={};return ae(o).pipe(_e(s=>pS(e[s],t,n,r).pipe(nn(),Ie(a=>{if(a instanceof Do)throw Vc(new _r,a);i[s]=a}))),jr(1),O(()=>i),En(s=>Py(s)?We:Lr(s)))}function pS(e,t,n,r){let o=_o(t)??r,i=wo(e,o),s=i.resolve?i.resolve(t,n):Re(o,()=>i(t,n));return vn(s)}function Bf(e){return Le(t=>{let n=e(t);return n?ae(n).pipe(O(()=>t)):P(t)})}var op=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===U);return r}getResolvedTitleForRoute(n){return n.data[Ji]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>h(By),providedIn:"root"})}return e})(),By=(()=>{class e extends op{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(M(oy))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),bo=new b("",{providedIn:"root",factory:()=>({})}),ns=new b(""),Uy=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=h(sf);loadComponent(n,r){if(this.componentLoaders.get(r))return this.componentLoaders.get(r);if(r._loadedComponent)return P(r._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(r);let o=vn(Re(n,()=>r.loadComponent())).pipe(O($y),Le(zy),Ie(s=>{this.onLoadEndListener&&this.onLoadEndListener(r),r._loadedComponent=s}),Yn(()=>{this.componentLoaders.delete(r)})),i=new Fr(o,()=>new ce).pipe(Pr());return this.componentLoaders.set(r,i),i}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return P({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=Hy(r,this.compiler,n,this.onLoadEndListener).pipe(Yn(()=>{this.childrenLoaders.delete(r)})),s=new Fr(i,()=>new ce).pipe(Pr());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Hy(e,t,n,r){return vn(Re(n,()=>e.loadChildren())).pipe(O($y),Le(zy),_e(o=>o instanceof Xa||Array.isArray(o)?P(o):ae(t.compileModuleAsync(o))),O(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(ns,[],{optional:!0,self:!0}).flat()),{routes:s.map(rp),injector:i}}))}function hS(e){return e&&typeof e=="object"&&"default"in e}function $y(e){return hS(e)?e.default:e}function zy(e){return P(e)}var $c=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>h(gS),providedIn:"root"})}return e})(),gS=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Gy=new b("");var Wy=new b(""),qy=(()=>{class e{currentNavigation=Tt(null,{equal:()=>!1});currentTransition=null;lastSuccessfulNavigation=null;events=new ce;transitionAbortWithErrorSubject=new ce;configLoader=h(Uy);environmentInjector=h(pe);destroyRef=h(Ut);urlSerializer=h(Xi);rootContexts=h(Co);location=h(so);inputBindingEnabled=h(Uc,{optional:!0})!==null;titleStrategy=h(op);options=h(bo,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=h($c);createViewTransition=h(Gy,{optional:!0});navigationErrorHandler=h(Wy,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>P(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new Ac(o)),r=o=>this.events.next(new Nc(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;Me(()=>{this.transitions?.next(G(D({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))})}setupNavigations(n){return this.transitions=new Ee(null),this.transitions.pipe(Ze(r=>r!==null),Le(r=>{let o=!1;return P(r).pipe(Le(i=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",Ge.SupersededByNewNavigation),We;this.currentTransition=r,this.currentNavigation.set({id:i.id,initialUrl:i.rawUrl,extractedUrl:i.extractedUrl,targetBrowserUrl:typeof i.extras.browserUrl=="string"?this.urlSerializer.parse(i.extras.browserUrl):i.extras.browserUrl,trigger:i.source,extras:i.extras,previousNavigation:this.lastSuccessfulNavigation?G(D({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>i.abortController.abort()});let s=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=i.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!s&&a!=="reload")return this.events.next(new gn(i.id,this.urlSerializer.serialize(i.rawUrl),"",zi.IgnoredSameUrlNavigation)),i.resolve(!1),We;if(this.urlHandlingStrategy.shouldProcessUrl(i.rawUrl))return P(i).pipe(Le(c=>(this.events.next(new Cr(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?We:Promise.resolve(c))),lS(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),Ie(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation.update(u=>(u.finalUrl=c.urlAfterRedirects,u));let l=new Gi(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(i.currentRawUrl)){let{id:c,extractedUrl:l,source:u,restoredState:d,extras:p}=i,f=new Cr(c,this.urlSerializer.serialize(l),u,d);this.events.next(f);let g=My(this.rootComponentType).snapshot;return this.currentTransition=r=G(D({},i),{targetSnapshot:g,urlAfterRedirects:l,extras:G(D({},p),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.update(S=>(S.finalUrl=l,S)),P(r)}else return this.events.next(new gn(i.id,this.urlSerializer.serialize(i.extractedUrl),"",zi.IgnoredByUrlHandlingStrategy)),i.resolve(!1),We}),Ie(i=>{let s=new Ic(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot);this.events.next(s)}),O(i=>(this.currentTransition=r=G(D({},i),{guards:M0(i.targetSnapshot,i.currentSnapshot,this.rootContexts)}),r)),j0(this.environmentInjector,i=>this.events.next(i)),Ie(i=>{if(r.guardsResult=i.guardsResult,i.guardsResult&&typeof i.guardsResult!="boolean")throw Vc(this.urlSerializer,i.guardsResult);let s=new Sc(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot,!!i.guardsResult);this.events.next(s)}),Ze(i=>i.guardsResult?!0:(this.cancelNavigationTransition(i,"",Ge.GuardRejected),!1)),Bf(i=>{if(i.guards.canActivateChecks.length!==0)return P(i).pipe(Ie(s=>{let a=new Tc(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),Le(s=>{let a=!1;return P(s).pipe(uS(this.paramsInheritanceStrategy,this.environmentInjector),Ie({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",Ge.NoDataFromResolver)}}))}),Ie(s=>{let a=new Mc(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),Bf(i=>{let s=a=>{let c=[];if(a.routeConfig?.loadComponent){let l=_o(a)??this.environmentInjector;c.push(this.configLoader.loadComponent(l,a.routeConfig).pipe(Ie(u=>{a.component=u}),O(()=>{})))}for(let l of a.children)c.push(...s(l));return c};return zs(s(i.targetSnapshot.root)).pipe(Cn(null),tn(1))}),Bf(()=>this.afterPreactivation()),Le(()=>{let{currentSnapshot:i,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,i.root,s.root);return a?ae(a).pipe(O(()=>r)):P(r)}),O(i=>{let s=w0(n.routeReuseStrategy,i.targetSnapshot,i.currentRouterState);return this.currentTransition=r=G(D({},i),{targetRouterState:s}),this.currentNavigation.update(a=>(a.targetRouterState=s,a)),r}),Ie(()=>{this.events.next(new Wi)}),T0(this.rootContexts,n.routeReuseStrategy,i=>this.events.next(i),this.inputBindingEnabled),tn(1),Ws(new z(i=>{let s=r.abortController.signal,a=()=>i.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(Ze(()=>!o&&!r.targetRouterState),Ie(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",Ge.Aborted)}))),Ie({next:i=>{o=!0,this.lastSuccessfulNavigation=Me(this.currentNavigation),this.events.next(new Kt(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects))),this.titleStrategy?.updateTitle(i.targetRouterState.snapshot),i.resolve(!0)},complete:()=>{o=!0}}),Ws(this.transitionAbortWithErrorSubject.pipe(Ie(i=>{throw i}))),Yn(()=>{o||this.cancelNavigationTransition(r,"",Ge.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation.set(null),this.currentTransition=null)}),En(i=>{if(this.destroyed)return r.resolve(!1),We;if(o=!0,ky(i))this.events.next(new Yt(r.id,this.urlSerializer.serialize(r.extractedUrl),i.message,i.cancellationCode)),S0(i)?this.events.next(new yo(i.url,i.navigationBehaviorOptions)):r.resolve(!1);else{let s=new vo(r.id,this.urlSerializer.serialize(r.extractedUrl),i,r.targetSnapshot??void 0);try{let a=Re(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof Do){let{message:c,cancellationCode:l}=Vc(this.urlSerializer,a);this.events.next(new Yt(r.id,this.urlSerializer.serialize(r.extractedUrl),c,l)),this.events.next(new yo(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),i}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return We}))}))}cancelNavigationTransition(n,r,o){let i=new Yt(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=Me(this.currentNavigation),o=r?.targetBrowserUrl??r?.extractedUrl;return n.toString()!==o?.toString()&&!r?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function mS(e){return e!==Ui}var Zy=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>h(vS),providedIn:"root"})}return e})(),Bc=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},vS=(()=>{class e extends Bc{static \u0275fac=(()=>{let n;return function(o){return(n||(n=hr(e)))(o||e)}})();static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Yy=(()=>{class e{urlSerializer=h(Xi);options=h(bo,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=h(so);urlHandlingStrategy=h($c);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Qt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof Qt?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=My(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:()=>h(yS),providedIn:"root"})}return e})(),yS=(()=>{class e extends Yy{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof Cr?this.updateStateMemento():n instanceof gn?this.commitTransition(r):n instanceof Gi?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Wi?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof Yt&&n.code!==Ge.SupersededByNewNavigation&&n.code!==Ge.Redirect?this.restoreHistory(r):n instanceof vo?this.restoreHistory(r,!0):n instanceof Kt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=D(D({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=D(D({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=hr(e)))(o||e)}})();static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ip(e,t){e.events.pipe(Ze(n=>n instanceof Kt||n instanceof Yt||n instanceof vo||n instanceof gn),O(n=>n instanceof Kt||n instanceof gn?0:(n instanceof Yt?n.code===Ge.Redirect||n.code===Ge.SupersededByNewNavigation:!1)?2:1),Ze(n=>n!==2),tn(1)).subscribe(()=>{t()})}var DS={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},ES={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},rs=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=h(Jd);stateManager=h(Yy);options=h(bo,{optional:!0})||{};pendingTasks=h(Ht);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=h(qy);urlSerializer=h(Xi);location=h(so);urlHandlingStrategy=h($c);injector=h(pe);_events=new ce;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=h(Zy);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=h(ns,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!h(Uc,{optional:!0});currentNavigation=this.navigationTransitions.currentNavigation.asReadonly();constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new de;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=Me(this.navigationTransitions.currentNavigation);if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof Yt&&r.code!==Ge.Redirect&&r.code!==Ge.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Kt)this.navigated=!0;else if(r instanceof yo){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=D({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||mS(o.source)},s);this.scheduleNavigation(a,Ui,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}D0(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortWithErrorSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Ui,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=D({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i).catch(c=>{this.disposed||this.injector.get($e)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return Me(this.navigationTransitions.currentNavigation)}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(rp),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,u=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":u=D(D({},this.currentUrlTree.queryParams),i);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=i||null}u!==null&&(u=this.removeEmptyProps(u));let d;try{let p=o?o.snapshot:this.routerState.snapshot.root;d=by(p)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return Iy(d,n,u,l??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=Bn(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Ui,null,r)}navigate(n,r={skipLocationChange:!1}){return _S(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=D({},DS):r===!1?o=D({},ES):o=r,Bn(n))return iy(this.currentUrlTree,n,o);let i=this.parseUrl(n);return iy(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((d,p)=>{a=d,c=p});let u=this.pendingTasks.add();return ip(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function _S(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new _(4008,!1)}var Jt=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;reactiveHref=Tt(null);get href(){return Me(this.reactiveHref)}set href(n){this.reactiveHref.set(n)}target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new ce;applicationErrorHandler=h($e);options=h(bo,{optional:!0});constructor(n,r,o,i,s,a){this.router=n,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a,this.reactiveHref.set(h(new ic("href"),{optional:!0}));let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area"||!!(typeof customElements=="object"&&customElements.get(c)?.observedAttributes?.includes?.("href")),this.isAnchorElement?this.setTabIndexIfNotOnNativeEl("0"):this.subscribeToNavigationEventsIfNecessary()}subscribeToNavigationEventsIfNecessary(){if(this.subscription!==void 0||!this.isAnchorElement)return;let n=this.preserveFragment,r=o=>o==="merge"||o==="preserve";n||=r(this.queryParamsHandling),n||=!this.queryParamsHandling&&!r(this.options?.defaultQueryParamsHandling),n&&(this.subscription=this.router.events.subscribe(o=>{o instanceof Kt&&this.updateHref()}))}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&(this.updateHref(),this.subscribeToNavigationEventsIfNecessary()),this.onChanges.next(this)}routerLinkInput=null;set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(Bn(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(n!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c)?.catch(l=>{this.applicationErrorHandler(l)}),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let n=this.urlTree;this.reactiveHref.set(n!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(n))??"":null)}applyAttributeValue(n,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,n,r):o.removeAttribute(i,n)}get urlTree(){return this.routerLinkInput===null?null:Bn(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(T(rs),T(mn),vi("tabindex"),T(fn),T(ot),T(io))};static \u0275dir=ve({type:e,selectors:[["","routerLink",""]],hostVars:2,hostBindings:function(r,o){r&1&&H("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&gt("href",o.reactiveHref(),Rd)("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",mr],skipLocationChange:[2,"skipLocationChange","skipLocationChange",mr],replaceUrl:[2,"replaceUrl","replaceUrl",mr],routerLink:"routerLink"},features:[rt]})}return e})();var wS=new b("");function sp(e,...t){return cn([{provide:ns,multi:!0,useValue:e},[],{provide:mn,useFactory:bS,deps:[rs]},{provide:tc,multi:!0,useFactory:IS},t.map(n=>n.\u0275providers)])}function bS(e){return e.routerState.root}function IS(){let e=h(Ct);return t=>{let n=e.get(Fn);if(t!==n.components[0])return;let r=e.get(rs),o=e.get(SS);e.get(TS)===1&&r.initialNavigation(),e.get(MS,null,{optional:!0})?.setUpPreloading(),e.get(wS,null,{optional:!0})?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var SS=new b("",{factory:()=>new ce}),TS=new b("",{providedIn:"root",factory:()=>1});var MS=new b("");var Dt=class e{constructor(t){this.http=t;this.BACKEND_URL=window.location.protocol+"//"+window.location.host.replace("5052","8080")}BACKEND_URL;apiExplorerUrl(){return this.BACKEND_URL+"/api/explorer"}apiGraphiqlUrl(){return this.BACKEND_URL+"/api/graphiql"}loadAllValue(t){let n=new Ae({Accept:"application/json"}),r=this.BACKEND_URL+t;return console.log(["RestApi.loadAllValue","URL:",r,"Backend URL:",this.BACKEND_URL]),this.http.get(r,{headers:n}).pipe(O(o=>(console.log(["RestApi.loadAllValue response","Raw response:",o,"Type:",typeof o]),o&&o.content&&Array.isArray(o.content)?(console.log(["RestApi.loadAllValue","Using paginated response",o.content]),o.content.map(i=>(delete i.links,delete i.content,i))):Array.isArray(o)?(console.log(["RestApi.loadAllValue","Using direct array response",o]),o.map(i=>(delete i.links,delete i.content,i))):(console.log(["RestApi.loadAllValue","Unexpected response format",o]),[]))))}loadOneValue(t){let n=new Ae({Accept:"application/json"});return this.http.get(this.BACKEND_URL+t,{headers:n}).pipe(O(r=>(delete r.content,delete r.links,r)))}createValue(t,n){let r=new Ae({Accept:"application/json","Content-type":"application/json"});return this.http.post(this.BACKEND_URL+t,n,{headers:r}).pipe(O(o=>(delete o.content,delete o.links,o)))}updateValue(t,n){let r=new Ae({Accept:"application/json","Content-type":"application/json"});return this.http.put(this.BACKEND_URL+t,n,{headers:r}).pipe(O(o=>(delete o.content,delete o.links,o)))}updatePatch(t,n){let r=new Ae({Accept:"application/json","Content-type":"application/merge-patch+json"});return this.http.patch(this.BACKEND_URL+t,n,{headers:r}).pipe(O(o=>(delete o.content,delete o.links,o)))}removeValue(t){let n=new Ae({Accept:"application/json"});return this.http.delete(this.BACKEND_URL+t,{headers:n}).pipe(O(r=>(delete r.content,delete r.links,r)))}version(){let t="/version",n=new Ae({Accept:"text/html"});return this.http.get(this.BACKEND_URL+t,{headers:n,responseType:"text"})}static \u0275fac=function(n){return new(n||e)(M(vc))};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})};var Ke=class e{toastSubject=new Ee([]);toasts$=this.toastSubject.asObservable();nextId=1;push(t,n="error",r=5e3){let o={id:this.nextId++,message:t,type:n,duration:r},i=this.toastSubject.value;this.toastSubject.next([...i,o]),r>0&&setTimeout(()=>{this.remove(o.id)},r)}remove(t){let n=this.toastSubject.value;this.toastSubject.next(n.filter(r=>r.id!==t))}clear(){this.toastSubject.next([])}static \u0275fac=function(n){return new(n||e)};static \u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})};function AS(e,t){if(e&1&&gr(0,"div",5),e&2){let n=R();Te("innerHTML",n.versionHtml,xd)}}function NS(e,t){e&1&&w(0," - ")}var Et=class e{constructor(t,n){this.restApi=t;this.toast=n}apiExplorer="";apiGraphiql="";versionHtml="";ngOnInit(){this.apiExplorer=this.restApi.apiExplorerUrl(),this.apiGraphiql=this.restApi.apiGraphiqlUrl(),this.restApi.version().subscribe({next:t=>{this.versionHtml=t},error:t=>{console.log(t),this.toast.push(t.toString())}})}static \u0275fac=function(n){return new(n||e)(T(Dt),T(Ke))};static \u0275cmp=Z({type:e,selectors:[["app-home"]],decls:26,vars:5,consts:[[1,"flex","flex-col","ml-2","mr-2","space-y-2"],[1,"p-4","border-2","space-y-2"],[1,"text-xs"],[1,"text-2xl"],["src","/pets.png","alt","Pets"],[3,"innerHTML"],["target","_blank",1,"underline","text-blue-600",3,"href"]],template:function(n,r){n&1&&(Y(0,"h1"),w(1,"Info"),ee(),Y(2,"div",0)(3,"fieldset",1)(4,"legend",2),w(5,"APP-Logo"),ee(),Y(6,"div",3),gr(7,"img",4),ee()(),Y(8,"fieldset",1)(9,"legend",2),w(10,"APP-Version"),ee(),Y(11,"div",3),we(12,AS,1,1,"div",5)(13,NS,1,0),ee()(),Y(14,"fieldset",1)(15,"legend",2),w(16,"API-Explorer"),ee(),Y(17,"div",3)(18,"a",6),w(19),ee()()(),Y(20,"fieldset",1)(21,"legend",2),w(22,"API-Graphiql"),ee(),Y(23,"div",3)(24,"a",6),w(25),ee()()()()),n&2&&(v(12),be(r.versionHtml?12:13),v(6),Te("href",r.apiExplorer+"/index.html#uri=/api",_i),v(),ye(" ",r.apiExplorer," "),v(5),Te("href",r.apiGraphiql,_i),v(),ye(" ",r.apiGraphiql," "))},dependencies:[J],encapsulation:2})};var oD=(()=>{class e{_renderer;_elementRef;onChange=n=>{};onTouched=()=>{};constructor(n,r){this._renderer=n,this._elementRef=r}setProperty(n,r){this._renderer.setProperty(this._elementRef.nativeElement,n,r)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}static \u0275fac=function(r){return new(r||e)(T(fn),T(ot))};static \u0275dir=ve({type:e})}return e})(),xS=(()=>{class e extends oD{static \u0275fac=(()=>{let n;return function(o){return(n||(n=hr(e)))(o||e)}})();static \u0275dir=ve({type:e,features:[Gt]})}return e})(),Ao=new b("");var RS={provide:Ao,useExisting:tt(()=>iD),multi:!0};function OS(){let e=yt()?yt().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var kS=new b(""),iD=(()=>{class e extends oD{_compositionMode;_composing=!1;constructor(n,r,o){super(n,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!OS())}writeValue(n){let r=n??"";this.setProperty("value",r)}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}static \u0275fac=function(r){return new(r||e)(T(fn),T(ot),T(kS,8))};static \u0275dir=ve({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&H("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[Wt([RS]),Gt]})}return e})();function PS(e){return e==null||FS(e)===0}function FS(e){return e==null?null:Array.isArray(e)||typeof e=="string"?e.length:e instanceof Set?e.size:null}var up=new b(""),sD=new b("");function LS(e){return PS(e.value)?{required:!0}:null}function Qy(e){return null}function aD(e){return e!=null}function cD(e){return Pn(e)?ae(e):e}function lD(e){let t={};return e.forEach(n=>{t=n!=null?D(D({},t),n):t}),Object.keys(t).length===0?null:t}function uD(e,t){return t.map(n=>n(e))}function VS(e){return!e.validate}function dD(e){return e.map(t=>VS(t)?t:n=>t.validate(n))}function jS(e){if(!e)return null;let t=e.filter(aD);return t.length==0?null:function(n){return lD(uD(n,t))}}function dp(e){return e!=null?jS(dD(e)):null}function BS(e){if(!e)return null;let t=e.filter(aD);return t.length==0?null:function(n){let r=uD(n,t).map(cD);return Sl(r).pipe(O(lD))}}function fp(e){return e!=null?BS(dD(e)):null}function Ky(e,t){return e===null?[t]:Array.isArray(e)?[...e,t]:[e,t]}function US(e){return e._rawValidators}function HS(e){return e._rawAsyncValidators}function ap(e){return e?Array.isArray(e)?e:[e]:[]}function Gc(e,t){return Array.isArray(e)?e.includes(t):e===t}function Jy(e,t){let n=ap(t);return ap(e).forEach(o=>{Gc(n,o)||n.push(o)}),n}function Xy(e,t){return ap(t).filter(n=>!Gc(e,n))}var Wc=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=dp(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=fp(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return this.control?this.control.hasError(t,n):!1}getError(t,n){return this.control?this.control.getError(t,n):null}},Mo=class extends Wc{name;get formDirective(){return null}get path(){return null}},us=class extends Wc{_parent=null;name=null;valueAccessor=null},qc=class{_cd;constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},$S={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},H$=G(D({},$S),{"[class.ng-submitted]":"isSubmitted"}),Hn=(()=>{class e extends qc{constructor(n){super(n)}static \u0275fac=function(r){return new(r||e)(T(us,2))};static \u0275dir=ve({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&oe("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[Gt]})}return e})(),No=(()=>{class e extends qc{constructor(n){super(n)}static \u0275fac=function(r){return new(r||e)(T(Mo,10))};static \u0275dir=ve({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&oe("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},standalone:!1,features:[Gt]})}return e})();var is="VALID",zc="INVALID",So="PENDING",ss="DISABLED",Un=class{},Zc=class extends Un{value;source;constructor(t,n){super(),this.value=t,this.source=n}},cs=class extends Un{pristine;source;constructor(t,n){super(),this.pristine=t,this.source=n}},ls=class extends Un{touched;source;constructor(t,n){super(),this.touched=t,this.source=n}},To=class extends Un{status;source;constructor(t,n){super(),this.status=t,this.source=n}},cp=class extends Un{source;constructor(t){super(),this.source=t}},lp=class extends Un{source;constructor(t){super(),this.source=t}};function fD(e){return(Kc(e)?e.validators:e)||null}function zS(e){return Array.isArray(e)?dp(e):e||null}function pD(e,t){return(Kc(t)?t.asyncValidators:e)||null}function GS(e){return Array.isArray(e)?fp(e):e||null}function Kc(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function WS(e,t,n){let r=e.controls;if(!(t?Object.keys(r):r).length)throw new _(1e3,"");if(!r[n])throw new _(1001,"")}function qS(e,t,n){e._forEachChild((r,o)=>{if(n[o]===void 0)throw new _(1002,"")})}var Yc=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(t,n){this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return Me(this.statusReactive)}set status(t){Me(()=>this.statusReactive.set(t))}_status=oo(()=>this.statusReactive());statusReactive=Tt(void 0);get valid(){return this.status===is}get invalid(){return this.status===zc}get pending(){return this.status==So}get disabled(){return this.status===ss}get enabled(){return this.status!==ss}errors;get pristine(){return Me(this.pristineReactive)}set pristine(t){Me(()=>this.pristineReactive.set(t))}_pristine=oo(()=>this.pristineReactive());pristineReactive=Tt(!0);get dirty(){return!this.pristine}get touched(){return Me(this.touchedReactive)}set touched(t){Me(()=>this.touchedReactive.set(t))}_touched=oo(()=>this.touchedReactive());touchedReactive=Tt(!1);get untouched(){return!this.touched}_events=new ce;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(Jy(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(Jy(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(Xy(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(Xy(t,this._rawAsyncValidators))}hasValidator(t){return Gc(this._rawValidators,t)}hasAsyncValidator(t){return Gc(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let n=this.touched===!1;this.touched=!0;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(G(D({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new ls(!0,r))}markAllAsDirty(t={}){this.markAsDirty({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsDirty(t))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(t))}markAsUntouched(t={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:r})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,r),n&&t.emitEvent!==!1&&this._events.next(new ls(!1,r))}markAsDirty(t={}){let n=this.pristine===!0;this.pristine=!1;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(G(D({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new cs(!1,r))}markAsPristine(t={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,r),n&&t.emitEvent!==!1&&this._events.next(new cs(!0,r))}markAsPending(t={}){this.status=So;let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new To(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(G(D({},t),{sourceControl:n}))}disable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=ss,this.errors=null,this._forEachChild(o=>{o.disable(G(D({},t),{onlySelf:!0}))}),this._updateValue();let r=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Zc(this.value,r)),this._events.next(new To(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(G(D({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=is,this._forEachChild(r=>{r.enable(G(D({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(G(D({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t,n){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===is||this.status===So)&&this._runAsyncValidator(r,t.emitEvent)}let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Zc(this.value,n)),this._events.next(new To(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(G(D({},t),{sourceControl:n}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?ss:is}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,n){if(this.asyncValidator){this.status=So,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1,shouldHaveEmitted:t!==!1};let r=cD(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:n,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=(this._hasOwnPendingAsyncValidator?.emitEvent||this._hasOwnPendingAsyncValidator?.shouldHaveEmitted)??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(t){let n=t;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((r,o)=>r&&r._find(o),this)}getError(t,n){let r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,n,r){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||r)&&this._events.next(new To(this.status,n)),this._parent&&this._parent._updateControlsErrors(t,n,r)}_initObservables(){this.valueChanges=new k,this.statusChanges=new k}_calculateStatus(){return this._allControlsDisabled()?ss:this.errors?zc:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(So)?So:this._anyControlsHaveStatus(zc)?zc:is}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,n){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),o&&this._events.next(new cs(this.pristine,n))}_updateTouched(t={},n){this.touched=this._anyControlsTouched(),this._events.next(new ls(this.touched,n)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n)}_onDisabledChange=[];_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){Kc(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let n=this._parent&&this._parent.dirty;return!t&&!!n&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=zS(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=GS(this._rawAsyncValidators)}},Qc=class extends Yc{constructor(t,n,r){super(fD(n),pD(r,n)),this.controls=t,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(t,n){return this.controls[t]?this.controls[t]:(this.controls[t]=n,n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange),n)}addControl(t,n,r={}){this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(t,n={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}setControl(t,n,r={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],n&&this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,n={}){qS(this,!0,t),Object.keys(t).forEach(r=>{WS(this,!0,r),this.controls[r].setValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(t,n={}){t!=null&&(Object.keys(t).forEach(r=>{let o=this.controls[r];o&&o.patchValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(t={},n={}){this._forEachChild((r,o)=>{r.reset(t?t[o]:null,{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n,this),this._updateTouched(n,this),this.updateValueAndValidity(n)}getRawValue(){return this._reduceChildren({},(t,n,r)=>(t[r]=n.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(n,r)=>r._syncPendingControls()?!0:n);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(n=>{let r=this.controls[n];r&&t(r,n)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(let[n,r]of Object.entries(this.controls))if(this.contains(n)&&t(r))return!0;return!1}_reduceValue(){let t={};return this._reduceChildren(t,(n,r,o)=>((r.enabled||this.disabled)&&(n[o]=r.value),n))}_reduceChildren(t,n){let r=t;return this._forEachChild((o,i)=>{r=n(r,o,i)}),r}_allControlsDisabled(){for(let t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}};var pp=new b("",{providedIn:"root",factory:()=>hp}),hp="always";function ZS(e,t){return[...t.path,e]}function hD(e,t,n=hp){gD(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||n==="always")&&t.valueAccessor.setDisabledState?.(e.disabled),QS(e,t),JS(e,t),KS(e,t),YS(e,t)}function eD(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function YS(e,t){if(t.valueAccessor.setDisabledState){let n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}function gD(e,t){let n=US(e);t.validator!==null?e.setValidators(Ky(n,t.validator)):typeof n=="function"&&e.setValidators([n]);let r=HS(e);t.asyncValidator!==null?e.setAsyncValidators(Ky(r,t.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();eD(t._rawValidators,o),eD(t._rawAsyncValidators,o)}function QS(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&mD(e,t)})}function KS(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&mD(e,t),e.updateOn!=="submit"&&e.markAsTouched()})}function mD(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function JS(e,t){let n=(r,o)=>{t.valueAccessor.writeValue(r),o&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}function XS(e,t){e==null,gD(e,t)}function eT(e,t){if(!e.hasOwnProperty("model"))return!1;let n=e.model;return n.isFirstChange()?!0:!Object.is(t,n.currentValue)}function tT(e){return Object.getPrototypeOf(e.constructor)===xS}function nT(e,t){e._syncPendingControls(),t.forEach(n=>{let r=n.control;r.updateOn==="submit"&&r._pendingChange&&(n.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function rT(e,t){if(!t)return null;Array.isArray(t);let n,r,o;return t.forEach(i=>{i.constructor===iD?n=i:tT(i)?r=i:o=i}),o||r||n||null}var oT={provide:Mo,useExisting:tt(()=>wr)},as=Promise.resolve(),wr=(()=>{class e extends Mo{callSetDisabledState;get submitted(){return Me(this.submittedReactive)}_submitted=oo(()=>this.submittedReactive());submittedReactive=Tt(!1);_directives=new Set;form;ngSubmit=new k;options;constructor(n,r,o){super(),this.callSetDisabledState=o,this.form=new Qc({},dp(n),fp(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(n){as.then(()=>{let r=this._findContainer(n.path);n.control=r.registerControl(n.name,n.control),hD(n.control,n,this.callSetDisabledState),n.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(n)})}getControl(n){return this.form.get(n.path)}removeControl(n){as.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name),this._directives.delete(n)})}addFormGroup(n){as.then(()=>{let r=this._findContainer(n.path),o=new Qc({});XS(o,n),r.registerControl(n.name,o),o.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(n){as.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name)})}getFormGroup(n){return this.form.get(n.path)}updateModel(n,r){as.then(()=>{this.form.get(n.path).setValue(r)})}setValue(n){this.control.setValue(n)}onSubmit(n){return this.submittedReactive.set(!0),nT(this.form,this._directives),this.ngSubmit.emit(n),this.form._events.next(new cp(this.control)),n?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this.submittedReactive.set(!1),this.form._events.next(new lp(this.form))}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(n){return n.pop(),n.length?this.form.get(n):this.form}static \u0275fac=function(r){return new(r||e)(T(up,10),T(sD,10),T(pp,8))};static \u0275dir=ve({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,o){r&1&&H("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[Wt([oT]),Gt]})}return e})();function tD(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function nD(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var iT=class extends Yc{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(t=null,n,r){super(fD(n),pD(r,n)),this._applyFormState(t),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Kc(n)&&(n.nonNullable||n.initialValueIsDefault)&&(nD(t)?this.defaultValue=t.value:this.defaultValue=t)}setValue(t,n={}){this.value=this._pendingValue=t,this._onChange.length&&n.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,n.emitViewToModelChange!==!1)),this.updateValueAndValidity(n)}patchValue(t,n={}){this.setValue(t,n)}reset(t=this.defaultValue,n={}){this._applyFormState(t),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){tD(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){tD(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(t){nD(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}};var sT={provide:us,useExisting:tt(()=>yn)},rD=Promise.resolve(),yn=(()=>{class e extends us{_changeDetectorRef;callSetDisabledState;control=new iT;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new k;constructor(n,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this._parent=n,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=rT(this,i)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){let r=n.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),eT(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){hD(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(n){rD.then(()=>{this.control.setValue(n,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(n){let r=n.isDisabled.currentValue,o=r!==0&&mr(r);rD.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(n){return this._parent?ZS(n,this._parent):[n]}static \u0275fac=function(r){return new(r||e)(T(Mo,9),T(up,10),T(sD,10),T(Ao,10),T(Ln,8),T(pp,8))};static \u0275dir=ve({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[Wt([sT]),Gt,rt]})}return e})();var xo=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=ve({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})();var aT=(()=>{class e{_validator=Qy;_onChange;_enabled;ngOnChanges(n){if(this.inputName in n){let r=this.normalizeInput(n[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):Qy,this._onChange&&this._onChange()}}validate(n){return this._validator(n)}registerOnValidatorChange(n){this._onChange=n}enabled(n){return n!=null}static \u0275fac=function(r){return new(r||e)};static \u0275dir=ve({type:e,features:[rt]})}return e})();var cT={provide:up,useExisting:tt(()=>ds),multi:!0};var ds=(()=>{class e extends aT{required;inputName="required";normalizeInput=mr;createValidator=n=>LS;enabled(n){return n}static \u0275fac=(()=>{let n;return function(o){return(n||(n=hr(e)))(o||e)}})();static \u0275dir=ve({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(r,o){r&2&&gt("required",o._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[Wt([cT]),Gt]})}return e})();var lT=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=zt({type:e});static \u0275inj=wt({})}return e})();var $n=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:pp,useValue:n.callSetDisabledState??hp}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=zt({type:e});static \u0275inj=wt({imports:[lT]})}return e})();var uT=["inputElement"];function dT(e,t){if(e&1&&(Y(0,"span",4),w(1),ee()),e&2){let n=R();oe("text-label-600",!n.elementIsFocused)("text-primary-500",n.elementIsFocused),v(),ye(" ",n.label," ")}}var zn=class e{disabled=!1;label;title;placeholder;required=!1;type="text";textChange=new k;textFocus=new k;textBlur=new k;inputElement;value="";elementIsFocused=!1;onChange=t=>{};onTouched=()=>{};focus(){this.inputElement?.nativeElement?.focus()}handleFocus(t){this.elementIsFocused=!0,this.textFocus.emit(t)}handleBlur(t){this.elementIsFocused=!1,this.onTouched(),this.textBlur.emit(t)}handleChange(t){let n=t.target;this.value=n.value,this.onChange(this.value),this.textChange.emit(t)}writeValue(t){this.value=t||""}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.disabled=t}static \u0275fac=function(n){return new(n||e)};static \u0275cmp=Z({type:e,selectors:[["app-text-field"]],viewQuery:function(n,r){if(n&1&&vt(uT,7),n&2){let o;st(o=at())&&(r.inputElement=o.first)}},inputs:{disabled:"disabled",label:"label",title:"title",placeholder:"placeholder",required:"required",type:"type"},outputs:{textChange:"textChange",textFocus:"textFocus",textBlur:"textBlur"},features:[Wt([{provide:Ao,useExisting:tt(()=>e),multi:!0}])],decls:4,vars:10,consts:[["inputElement",""],[1,"relative"],[1,"px-4","pt-2","text-xs","absolute","left-0","top-0",3,"text-label-600","text-primary-500"],[1,"disabled:opacity-50","w-full","px-4","text-black","bg-gray-100","border-0","outline-none",3,"input","focus","blur","type","title","disabled","placeholder","required","value"],[1,"px-4","pt-2","text-xs","absolute","left-0","top-0"]],template:function(n,r){if(n&1){let o=re();Y(0,"div",1),we(1,dT,2,5,"span",2),Y(2,"input",3,0),mt("input",function(s){return N(o),x(r.handleChange(s))})("focus",function(s){return N(o),x(r.handleFocus(s))})("blur",function(s){return N(o),x(r.handleBlur(s))}),ee()()}n&2&&(v(),be(r.label?1:-1),v(),oe("pt-6",r.label),Te("type",r.type)("title",r.title)("disabled",r.disabled)("placeholder",r.placeholder)("required",r.required)("value",r.value),gt("aria-label",r.label))},dependencies:[J],styles:[".relative[_ngcontent-%COMP%]{overflow:hidden}input[_ngcontent-%COMP%]{padding-bottom:.5rem;outline:2px solid transparent;outline-offset:2px;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s;min-height:2.5rem;border:none!important;box-shadow:none!important;outline:none!important}input[_ngcontent-%COMP%]:focus, input[_ngcontent-%COMP%]:active, input[_ngcontent-%COMP%]:hover{outline:2px solid transparent;outline-offset:2px;border:none!important;box-shadow:none!important;outline:none!important}input[type=text][_ngcontent-%COMP%], input[type=tel][_ngcontent-%COMP%], input[type=email][_ngcontent-%COMP%]{border:none!important;outline:none!important;box-shadow:none!important}span[_ngcontent-%COMP%]{pointer-events:none;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.2s}.mt-1[_ngcontent-%COMP%]{margin-top:0}"]})};var fT=["buttonElement"],Ro=class e{checked=!1;disabled=!1;name;outlined=!1;title;type="button";checkedChange=new k;clickedChange=new k;iconClick=new k;buttonElement;clicked=0;focus(){this.buttonElement?.nativeElement?.focus()}handleClick(t){this.checked=!this.checked,this.clicked++,this.checkedChange.emit(this.checked),this.clickedChange.emit(this.clicked),this.iconClick.emit(t)}static \u0275fac=function(n){return new(n||e)};static \u0275cmp=Z({type:e,selectors:[["app-icon"]],viewQuery:function(n,r){if(n&1&&vt(fT,7),n&2){let o;st(o=at())&&(r.buttonElement=o.first)}},inputs:{checked:"checked",disabled:"disabled",name:"name",outlined:"outlined",title:"title",type:"type"},outputs:{checkedChange:"checkedChange",clickedChange:"clickedChange",iconClick:"iconClick"},decls:5,vars:10,consts:[["buttonElement",""],[1,"text-xl","text-white","w-12","h-12","rounded-full","p-2","disabled:opacity-50","hover:opacity-90","focus:ring","bg-primary-500",3,"click","type","title","disabled"],[1,"flex","justify-center","items-center"],["aria-hidden","true",1,"material-icons","icon","text-xl","select-none",3,"title"]],template:function(n,r){if(n&1){let o=re();Y(0,"button",1,0),mt("click",function(s){return N(o),x(r.handleClick(s))}),Y(2,"div",2)(3,"i",3),w(4),ee()()()}n&2&&(oe("disabled",r.disabled)("outlined",r.outlined),Te("type",r.type)("title",r.title)("disabled",r.disabled),gt("aria-label",r.name),v(3),Te("title",r.title),v(),ye(" ",r.name," "))},dependencies:[J],encapsulation:2})};var Oo=class e{color="#FF3E00";unit="px";duration="0.75s";size="60";pause=!1;get spinnerStyle(){return{"--size":`${this.size}${this.unit}`,"--color":this.color,"--duration":this.duration}}static \u0275fac=function(n){return new(n||e)};static \u0275cmp=Z({type:e,selectors:[["app-spinner"]],inputs:{color:"color",unit:"unit",duration:"duration",size:"size",pause:"pause"},decls:1,vars:3,consts:[[1,"circle",3,"ngStyle"]],template:function(n,r){n&1&&Se(0,"div",0),n&2&&(oe("pause-animation",r.pause),V("ngStyle",r.spinnerStyle))},dependencies:[J,Df],styles:[".circle[_ngcontent-%COMP%]{height:var(--size);width:var(--size);border-color:var(--color) transparent var(--color) var(--color);border-width:calc(var(--size) / 15);border-style:solid;border-image:initial;border-radius:50%;animation:var(--duration) linear 0s infinite normal none running _ngcontent-%COMP%_rotate}.pause-animation[_ngcontent-%COMP%]{animation-play-state:paused}@keyframes _ngcontent-%COMP%_rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}"]})};var pT=["buttonElement"],hT=["*"],ko=class e{checked=!1;disabled=!1;outlined=!1;title;type="button";checkedChange=new k;clickedChange=new k;buttonClick=new k;buttonElement;clicked=0;focus(){this.buttonElement?.nativeElement?.focus()}handleClick(t){this.checked=!this.checked,this.clicked++,this.checkedChange.emit(this.checked),this.clickedChange.emit(this.clicked),this.buttonClick.emit(t)}static \u0275fac=function(n){return new(n||e)};static \u0275cmp=Z({type:e,selectors:[["app-button"]],viewQuery:function(n,r){if(n&1&&vt(pT,7),n&2){let o;st(o=at())&&(r.buttonElement=o.first)}},inputs:{checked:"checked",disabled:"disabled",outlined:"outlined",title:"title",type:"type"},outputs:{checkedChange:"checkedChange",clickedChange:"clickedChange",buttonClick:"buttonClick"},ngContentSelectors:hT,decls:3,vars:7,consts:[["buttonElement",""],[1,"text-sm","text-white","rounded","uppercase","py-2","px-4","disabled:opacity-50","hover:opacity-90","focus:underline","bg-primary-500","overflow-hidden",3,"click","type","title","disabled"]],template:function(n,r){if(n&1){let o=re();rf(),Y(0,"button",1,0),mt("click",function(s){return N(o),x(r.handleClick(s))}),of(2),ee()}n&2&&(oe("disabled",r.disabled)("outlined",r.outlined),Te("type",r.type)("title",r.title)("disabled",r.disabled))},dependencies:[J],encapsulation:2})};var gT=["nameField"],mT=["bottomDiv"],gp=()=>({standalone:!0}),Xc=class e{constructor(t,n,r){this.restApi=t;this.toast=n;this.ngZone=r}visible=!1;owner={name:"",address:"",contact:""};autofocus=!0;autoscroll=!0;visibleChange=new k;cancel=new k;create=new k;update=new k;nameField;bottomDiv;newOwnerName="";newOwnerAddress="";newOwnerContact="";clicked=!1;ngOnInit(){this.initializeForm(),this.autofocus&&this.nameField&&setTimeout(()=>this.nameField.focus(),0),this.autoscroll&&this.bottomDiv&&setTimeout(()=>this.bottomDiv.nativeElement.scrollIntoView({behavior:"smooth",block:"end"}),0)}ngOnChanges(t){(t.owner||t.visible)&&this.initializeForm()}initializeForm(){this.newOwnerName=this.owner?.name||"",this.newOwnerAddress=this.owner?.address||"",this.newOwnerContact=this.owner?.contact||""}get newOwner(){return{id:this.owner?.id,version:this.owner?.version,name:this.newOwnerName,address:this.newOwnerAddress,contact:this.newOwnerContact}}get isUpdate(){return!!this.owner?.id}onSubmit(t){if(t.preventDefault(),!this.clicked)try{this.clicked=!0,this.isUpdate?this.updateOwner():this.createOwner()}finally{}}onCancel(t){t.preventDefault(),this.closeEditor(),this.cancel.emit()}createOwner(){this.restApi.createValue("/api/owner",this.newOwner).subscribe({next:t=>{let n=t;console.log(["createOwner",this.newOwner,n]),this.closeEditor(),this.create.emit(n),this.clicked=!1,this.ngZone.runOutsideAngular(()=>{setTimeout(()=>{this.ngZone.run(()=>{this.toast.push("Owner created successfully","success")})},0)})},error:t=>{console.log(["createOwner",this.newOwner,t]),this.clicked=!1,this.ngZone.runOutsideAngular(()=>{setTimeout(()=>{this.ngZone.run(()=>{this.toast.push("Error creating owner: "+(t?.error?.message||t?.message||"Unknown error"),"error")})},0)})}})}updateOwner(){this.restApi.updatePatch("/api/owner/"+this.newOwner.id,this.newOwner).subscribe({next:t=>{let n=t;console.log(["updateOwner",this.newOwner,n]),this.closeEditor(),this.update.emit(n),this.clicked=!1,this.ngZone.runOutsideAngular(()=>{setTimeout(()=>{this.ngZone.run(()=>{this.toast.push("Owner updated successfully","success")})},0)})},error:t=>{console.log(["updateOwner",this.newOwner,t]),this.clicked=!1,this.ngZone.runOutsideAngular(()=>{setTimeout(()=>{this.ngZone.run(()=>{this.toast.push("Error updating owner: "+(t?.error?.message||t?.message||"Unknown error"),"error")})},0)})}})}closeEditor(){this.visible=!1,this.visibleChange.emit(!1)}static \u0275fac=function(n){return new(n||e)(T(Dt),T(Ke),T(ne))};static \u0275cmp=Z({type:e,selectors:[["app-owner-editor"]],viewQuery:function(n,r){if(n&1&&(vt(gT,5),vt(mT,5)),n&2){let o;st(o=at())&&(r.nameField=o.first),st(o=at())&&(r.bottomDiv=o.first)}},inputs:{visible:"visible",owner:"owner",autofocus:"autofocus",autoscroll:"autoscroll"},outputs:{visibleChange:"visibleChange",cancel:"cancel",create:"create",update:"update"},features:[rt],decls:18,vars:15,consts:[["nameField",""],["bottomDiv",""],[3,"ngSubmit"],[1,"flex","flex-col","gap-1"],[1,"w-full"],["label","Name","placeholder","Enter owner's full name",3,"ngModelChange","ngModel","ngModelOptions","required"],["label","Address","placeholder","Enter street address and city",3,"ngModelChange","ngModel","ngModelOptions","required"],["label","Contact","placeholder","Enter phone number or email","type","text",3,"ngModelChange","ngModel","ngModelOptions","required"],[1,"py-4","flex","flex-row","gap-1","items-baseline"],[1,"flex-initial"],["type","submit",3,"disabled"],["type","button",3,"click","disabled"]],template:function(n,r){if(n&1){let o=re();m(0,"form",2),H("ngSubmit",function(s){return N(o),x(r.onSubmit(s))}),m(1,"div",3)(2,"div",4)(3,"app-text-field",5,0),je("ngModelChange",function(s){return N(o),ze(r.newOwnerName,s)||(r.newOwnerName=s),x(s)}),y()(),m(5,"div",4)(6,"app-text-field",6),je("ngModelChange",function(s){return N(o),ze(r.newOwnerAddress,s)||(r.newOwnerAddress=s),x(s)}),y()(),m(7,"div",4)(8,"app-text-field",7),je("ngModelChange",function(s){return N(o),ze(r.newOwnerContact,s)||(r.newOwnerContact=s),x(s)}),y()()(),m(9,"div",8)(10,"div",9)(11,"app-button",10),w(12),y()(),m(13,"div",9)(14,"app-button",11),H("click",function(s){return N(o),x(r.onCancel(s))}),w(15," Cancel "),y()()(),Se(16,"div",null,1),y()}n&2&&(v(3),Ve("ngModel",r.newOwnerName),V("ngModelOptions",Ye(12,gp))("required",!0),v(3),Ve("ngModel",r.newOwnerAddress),V("ngModelOptions",Ye(13,gp))("required",!0),v(2),Ve("ngModel",r.newOwnerContact),V("ngModelOptions",Ye(14,gp))("required",!0),v(3),V("disabled",r.clicked),v(),ye(" ",r.isUpdate?"Update":"Create"," "),v(2),V("disabled",r.clicked))},dependencies:[J,$n,xo,Hn,No,ds,yn,wr,zn,ko],styles:["form[_ngcontent-%COMP%]{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));padding:1rem}.flex-col[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-y-reverse: 0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.flex-row[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(.5rem * var(--tw-space-x-reverse));margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))}.w-full[_ngcontent-%COMP%]{margin-bottom:0}app-text-field[_ngcontent-%COMP%]{display:block;width:100%;margin-bottom:0}app-text-field[_ngcontent-%COMP%]   .mt-1[_ngcontent-%COMP%]{margin-top:0}form[_ngcontent-%COMP%]:focus-within{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000);--tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));--tw-ring-opacity: .5 }.py-4[_ngcontent-%COMP%]{margin-top:1rem;border-top-width:1px;--tw-border-opacity: 1;border-color:rgb(243 244 246 / var(--tw-border-opacity, 1));padding-top:1rem}@media (max-width: 640px){.flex-row[_ngcontent-%COMP%]{flex-direction:column}.flex-row[_ngcontent-%COMP%] > [_ngcontent-%COMP%]:not([hidden]) ~ [_ngcontent-%COMP%]:not([hidden]){--tw-space-x-reverse: 0;margin-right:calc(0px * var(--tw-space-x-reverse));margin-left:calc(0px * calc(1 - var(--tw-space-x-reverse)));margin-top:calc(.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse));--tw-space-y-reverse: 0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.flex-initial[_ngcontent-%COMP%]{width:100%;margin-bottom:0}}"]})};var vT=["selectElement"],yT=(e,t)=>t.value;function DT(e,t){if(e&1&&(Y(0,"span",5),w(1),ee()),e&2){let n=R();oe("text-label-600",!n.elementIsFocused)("text-primary-500",n.elementIsFocused),Te("title",n.title),v(),ye(" ",n.label," ")}}function ET(e,t){e&1&&(Y(0,"option",4),w(1,"\xA0"),ee()),e&2&&Te("value",null)}function _T(e,t){if(e&1&&(Y(0,"option",4),w(1),ee()),e&2){let n=t.$implicit;Te("value",n.value),v(),ct(n.text)}}var Po=class e{allItem=[];disabled=!1;label;nullable=!1;title;valueGetter;selectChange=new k;selectFocus=new k;selectBlur=new k;selectElement;value;elementIsFocused=!1;onChange=t=>{};onTouched=()=>{};focus(){this.selectElement?.nativeElement?.focus()}get itemIsPrimitive(){return this.allItem.slice(0,1).findIndex(t=>typeof t!="object")!==-1}get allItemIndexed(){return this.allItem.map((t,n)=>this.itemIsPrimitive?{value:t,text:t}:{value:t.value||n,text:t.text})}get itemSelected(){return this.itemIsPrimitive?this.value:typeof this.valueGetter=="function"?this.allItem.find(t=>this.valueGetter(t)===this.value)?.value||this.value:this.allItem.find(t=>t.value===this.value)?.value||this.value}valueMapper(t){return typeof t?.value!="object"?t?.value:t?.value?JSON.stringify(t.value):null}handleFocus(t){this.elementIsFocused=!0,this.selectFocus.emit(t)}handleBlur(t){this.elementIsFocused=!1,this.onTouched(),this.selectBlur.emit(t)}handleChange(t){let n=t.target;if(this.itemIsPrimitive)this.value=n.value||null;else{let r=n.value,o=this.allItem.find(i=>i.value===r);typeof this.valueGetter=="function"&&o?this.value=this.valueGetter(o):this.value=r||null}this.onChange(this.value),this.selectChange.emit(t)}writeValue(t){this.value=t}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.disabled=t}static \u0275fac=function(n){return new(n||e)};static \u0275cmp=Z({type:e,selectors:[["app-select"]],viewQuery:function(n,r){if(n&1&&vt(vT,7),n&2){let o;st(o=at())&&(r.selectElement=o.first)}},inputs:{allItem:"allItem",disabled:"disabled",label:"label",nullable:"nullable",title:"title",valueGetter:"valueGetter"},outputs:{selectChange:"selectChange",selectFocus:"selectFocus",selectBlur:"selectBlur"},features:[Wt([{provide:Ao,useExisting:tt(()=>e),multi:!0}])],decls:7,vars:12,consts:[["selectElement",""],[1,"mt-1","relative"],[1,"px-4","pt-2","text-xs","absolute","left-0","top-0",3,"title","text-label-600","text-primary-500"],[1,"disabled:opacity-50","w-full","px-4","text-black","bg-gray-100",3,"change","focus","blur","title","disabled","value"],[3,"value"],[1,"px-4","pt-2","text-xs","absolute","left-0","top-0",3,"title"]],template:function(n,r){if(n&1){let o=re();Y(0,"div",1),we(1,DT,2,6,"span",2),Y(2,"select",3,0),mt("change",function(s){return N(o),x(r.handleChange(s))})("focus",function(s){return N(o),x(r.handleFocus(s))})("blur",function(s){return N(o),x(r.handleBlur(s))}),we(4,ET,2,1,"option",4),xt(5,_T,2,2,"option",4,yT),ee()()}n&2&&(v(),be(r.label?1:-1),v(),oe("pt-6",r.label)("border-0",!r.elementIsFocused)("border-b",r.label),Te("title",r.title)("disabled",r.disabled)("value",r.itemSelected),gt("aria-label",r.label),v(2),be(r.nullable?4:-1),v(),Rt(r.allItemIndexed))},dependencies:[J],encapsulation:2})};var mp=()=>({standalone:!0}),el=class e{constructor(t,n,r){this.restApi=t;this.toast=n;this.ngZone=r}visible=!1;pet;ownerId;allSpeciesEnum=[];create=new k;update=new k;close=new k;newPet={name:"",born:"",species:""};clicked=!1;ngOnInit(){this.resetForm()}ngOnChanges(){this.resetForm()}resetForm(){this.pet?this.newPet={id:this.pet.id,version:this.pet.version,owner:`/api/owner/${this.ownerId}`,name:this.pet.name||"",born:this.pet.born||"",species:this.pet.species||""}:this.newPet={owner:`/api/owner/${this.ownerId}`,name:"",born:"",species:""}}get isUpdateMode(){return!!this.pet?.id}get buttonText(){return this.isUpdateMode?"Update":"Create"}onSubmit(){this.clicked||(this.clicked=!0,this.isUpdateMode?this.updatePet():this.createPet())}createPet(){this.restApi.createValue("/api/pet",this.newPet).subscribe({next:t=>{let n=t;console.log(["createPet",this.newPet,n]),this.closeEditor(),this.create.emit(n),this.clicked=!1,this.ngZone.runOutsideAngular(()=>{setTimeout(()=>{this.ngZone.run(()=>{this.toast.push("Pet created successfully","success")})},0)})},error:t=>{console.log(["createPet",this.newPet,t]),this.clicked=!1,this.ngZone.runOutsideAngular(()=>{setTimeout(()=>{this.ngZone.run(()=>{this.toast.push("Error creating pet: "+(t?.error?.message||t?.message||"Unknown error"),"error")})},0)})}})}updatePet(){this.pet?.id&&this.restApi.updatePatch(`/api/pet/${this.pet.id}`,this.newPet).subscribe({next:t=>{let n=t;console.log(["updatePet",this.newPet,n]),this.closeEditor(),this.update.emit(n),this.clicked=!1,this.ngZone.runOutsideAngular(()=>{setTimeout(()=>{this.ngZone.run(()=>{this.toast.push("Pet updated successfully","success")})},0)})},error:t=>{console.log(["updatePet",this.newPet,t]),this.clicked=!1,this.ngZone.runOutsideAngular(()=>{setTimeout(()=>{this.ngZone.run(()=>{this.toast.push("Error updating pet: "+(t?.error?.message||t?.message||"Unknown error"),"error")})},0)})}})}onCancel(){this.closeEditor()}closeEditor(){this.visible=!1,this.close.emit(),this.resetForm()}static \u0275fac=function(n){return new(n||e)(T(Dt),T(Ke),T(ne))};static \u0275cmp=Z({type:e,selectors:[["app-pet-editor"]],inputs:{visible:"visible",pet:"pet",ownerId:"ownerId",allSpeciesEnum:"allSpeciesEnum"},outputs:{create:"create",update:"update",close:"close"},features:[rt],decls:13,vars:14,consts:[[1,"flex","flex-col","gap-4","p-4",3,"ngSubmit"],[1,"flex","flex-col","gap-3"],[1,"w-full","lg:w-1/4"],["label","Species","placeholder","Choose species",3,"ngModelChange","ngModel","ngModelOptions","allItem"],[1,"w-full","lg:w-2/4"],["label","Name","placeholder","Enter pet's name",3,"ngModelChange","ngModel","ngModelOptions","required"],["type","date","label","Born","placeholder","Select pet's birth date",3,"ngModelChange","ngModel","ngModelOptions"],[1,"flex","flex-row","gap-2","items-center","pt-2"],["type","submit",1,"flex-initial",3,"disabled"],["type","button",1,"flex-initial",3,"click","disabled"]],template:function(n,r){n&1&&(m(0,"form",0),H("ngSubmit",function(){return r.onSubmit()}),m(1,"div",1)(2,"div",2)(3,"app-select",3),je("ngModelChange",function(i){return ze(r.newPet.species,i)||(r.newPet.species=i),i}),y()(),m(4,"div",4)(5,"app-text-field",5),je("ngModelChange",function(i){return ze(r.newPet.name,i)||(r.newPet.name=i),i}),y()(),m(6,"div",2)(7,"app-text-field",6),je("ngModelChange",function(i){return ze(r.newPet.born,i)||(r.newPet.born=i),i}),y()()(),m(8,"div",7)(9,"app-button",8),w(10),y(),m(11,"app-button",9),H("click",function(){return r.onCancel()}),w(12," Cancel "),y()()()),n&2&&(v(3),Ve("ngModel",r.newPet.species),V("ngModelOptions",Ye(11,mp))("allItem",r.allSpeciesEnum),v(2),Ve("ngModel",r.newPet.name),V("ngModelOptions",Ye(12,mp))("required",!0),v(2),Ve("ngModel",r.newPet.born),V("ngModelOptions",Ye(13,mp)),v(2),V("disabled",r.clicked),v(),ye(" ",r.buttonText," "),v(),V("disabled",r.clicked))},dependencies:[J,$n,xo,Hn,No,ds,yn,wr,zn,Po,ko],styles:["form[_ngcontent-%COMP%]{background-color:#f9fafb;border-radius:.5rem;min-height:200px}.flex-col[_ngcontent-%COMP%]{display:flex;flex-direction:column}.flex-row[_ngcontent-%COMP%]{display:flex;flex-direction:row}.gap-2[_ngcontent-%COMP%]{gap:.5rem}.gap-3[_ngcontent-%COMP%]{gap:.75rem}.gap-4[_ngcontent-%COMP%]{gap:1rem}.items-center[_ngcontent-%COMP%]{align-items:center}.flex-initial[_ngcontent-%COMP%]{flex:0 1 auto}.p-4[_ngcontent-%COMP%]{padding:1rem}.pt-2[_ngcontent-%COMP%]{padding-top:.5rem}@media (min-width: 1024px){.lg\\:w-1\\/4[_ngcontent-%COMP%]{width:25%}.lg\\:w-2\\/4[_ngcontent-%COMP%]{width:50%}}app-text-field[_ngcontent-%COMP%], app-select[_ngcontent-%COMP%]{display:block;width:100%;margin-bottom:0}app-button[_ngcontent-%COMP%]{display:inline-block}"]})};var CT=()=>({standalone:!0}),wT=()=>({name:"",address:"",contact:""}),bT=e=>["/owner",e],IT=e=>["/pet",e],vD=(e,t)=>t.id,ST=(e,t)=>t.value;function TT(e,t){e&1&&(m(0,"div",10)(1,"div",11),Se(2,"app-spinner",12),y()())}function MT(e,t){if(e&1){let n=re();m(0,"tr")(1,"td",22)(2,"app-owner-editor",23),je("visibleChange",function(o){N(n);let i=R(2);return ze(i.ownerEditorCreate,o)||(i.ownerEditorCreate=o),x(o)}),H("create",function(o){N(n);let i=R(2);return x(i.onCreateOwner(o))})("cancel",function(){N(n);let o=R(2);return x(o.ownerEditorCreate=!1)}),y()()()}if(e&2){let n=R(2);v(2),Ve("visible",n.ownerEditorCreate),V("owner",Ye(2,wT))}}function AT(e,t){if(e&1&&(m(0,"div",26)(1,"a",27),w(2),y()()),e&2){let n=t.$implicit;v(),V("routerLink",ro(2,IT,n.value)),v(),ct(n.text)}}function NT(e,t){if(e&1&&xt(0,AT,3,4,"div",26,ST),e&2){let n=R().$implicit;Rt(n.allPetItem)}}function xT(e,t){e&1&&(m(0,"span"),w(1,"No pets"),y())}function RT(e,t){if(e&1){let n=re();m(0,"tr")(1,"td",22)(2,"app-owner-editor",35),je("visibleChange",function(o){N(n);let i=R(3);return ze(i.ownerEditorUpdate,o)||(i.ownerEditorUpdate=o),x(o)}),H("update",function(o){N(n);let i=R(3);return x(i.onUpdateOwner(o))})("cancel",function(){N(n);let o=R(3);return x(o.ownerEditorUpdate=!1)}),y()()()}if(e&2){let n=R().$implicit,r=R(2);v(2),Ve("visible",r.ownerEditorUpdate),V("owner",n)}}function OT(e,t){if(e&1){let n=re();m(0,"tr")(1,"td",22)(2,"app-pet-editor",36),H("create",function(o){N(n);let i=R(3);return x(i.onCreatePet(o))})("update",function(o){N(n);let i=R(3);return x(i.onUpdatePet(o))})("close",function(){N(n);let o=R(3);return x(o.onClosePetEditor())}),y()()()}if(e&2){let n=R(3);v(2),V("visible",n.petCreateEditor)("ownerId",n.ownerId)("allSpeciesEnum",n.allSpeciesEnum)}}function kT(e,t){if(e&1&&(m(0,"div",41)(1,"div",42),w(2),y(),m(3,"div",43),w(4),y(),m(5,"div",44),w(6),y()()),e&2){let n=t.$implicit;v(2),ct(n.date),v(2),nc("Pet: ",n.pet.name," (",n.pet.species,")"),v(2),ct(n.description)}}function PT(e,t){if(e&1&&(m(0,"div",39),xt(1,kT,7,4,"div",41,vD),y()),e&2){let n=R(4);v(),Rt(n.allOwnerVisit)}}function FT(e,t){e&1&&(m(0,"p"),w(1,"No visits found"),y())}function LT(e,t){if(e&1){let n=re();m(0,"tr")(1,"td",22)(2,"div",37)(3,"h3",38),w(4),y(),we(5,PT,3,0,"div",39)(6,FT,2,0,"p"),m(7,"button",40),H("click",function(){N(n);let o=R(3);return x(o.visitLister=!1)}),w(8," Close "),y()()()()}if(e&2){let n=R().$implicit,r=R(2);v(4),ye("Visits for ",n.name),v(),be(r.allOwnerVisit.length>0?5:6)}}function VT(e,t){if(e&1){let n=re();m(0,"tr",24),H("click",function(){let o=N(n).$implicit,i=R(2);return x(i.onOwnerClicked(o))}),m(1,"td",25)(2,"div",26)(3,"a",27),w(4),y()()(),m(5,"td",25)(6,"div",28),we(7,NT,2,0)(8,xT,2,0,"span"),y()(),m(9,"td",29)(10,"div",30)(11,"app-icon",31),H("click",function(o){let i=N(n).$implicit;return R(2).onVisitListerClicked(i),x(o.stopPropagation())}),y(),m(12,"app-icon",32),H("click",function(o){let i=N(n).$implicit;return R(2).onPetCreateEditorClicked(i),x(o.stopPropagation())}),y(),m(13,"app-icon",33),H("click",function(o){let i=N(n).$implicit;return R(2).onOwnerRemoveClicked(i),x(o.stopPropagation())}),y(),m(14,"app-icon",34),H("click",function(o){let i=N(n).$implicit;return R(2).onOwnerEditorUpdateClicked(i),x(o.stopPropagation())}),y()()()(),we(15,RT,3,2,"tr"),we(16,OT,3,3,"tr"),we(17,LT,9,2,"tr")}if(e&2){let n=t.$implicit,r=t.$index,o=R(2);oe("border-l-2",o.ownerId===n.id)("bg-gray-100",r%2===1),V("title",(n.id==null?null:n.id.toString())||""),v(3),V("routerLink",ro(19,bT,n.id)),v(),ct(n.name),v(3),be(n.allPetItem&&n.allPetItem.length>0?7:8),v(4),V("disabled",o.ownerEditorDisabled)("outlined",!0),v(),V("disabled",o.ownerEditorDisabled)("outlined",!0),v(),V("disabled",o.ownerEditorDisabled)("outlined",!0),v(),V("disabled",o.ownerEditorDisabled)("outlined",!0),v(),be(o.ownerEditorUpdate&&o.ownerId===n.id?15:-1),v(),be(o.petCreateEditor&&o.ownerId===n.id?16:-1),v(),be(o.visitLister&&o.ownerId===n.id?17:-1)}}function jT(e,t){e&1&&(m(0,"tr")(1,"td",45)(2,"span"),w(3,"No owners found"),y()()())}function BT(e,t){if(e&1){let n=re();m(0,"div")(1,"table",13)(2,"thead",14)(3,"tr",15)(4,"th",16)(5,"span",17),w(6,"Name"),y()(),m(7,"th",18)(8,"span",19),w(9,"Pets"),y()(),m(10,"th",20)(11,"app-icon",21),H("click",function(){N(n);let o=R();return x(o.onOwnerEditorCreateClicked())}),y()()()(),m(12,"tbody"),we(13,MT,3,3,"tr"),xt(14,VT,18,21,null,null,vD,!1,jT,4,0,"tr"),y()()()}if(e&2){let n=R();v(11),V("disabled",n.ownerEditorDisabled)("outlined",!0),v(2),be(n.ownerEditorCreate?13:-1),v(),Rt(n.allOwner)}}var tl=class e{constructor(t,n,r){this.restApi=t;this.toast=n;this.cdr=r}allVetItem=[];allSpeciesEnum=[];loading=!0;ownerId;ownerEditorCreate=!1;ownerEditorUpdate=!1;visitLister=!1;petCreateEditor=!1;ownerFilter="";allOwner=[];allOwnerVisit=[];ngOnInit(){console.log(["Owner component ngOnInit"]),this.initializeData()}initializeData(){this.loading=!0,console.log(["initializeData starting","loading set to:",this.loading]),this.loadAllOwner(),this.restApi.loadAllValue("/api/enum/species").subscribe({next:t=>{this.allSpeciesEnum=t.map(n=>({value:n.value,text:n.name})),console.log(["species loaded",this.allSpeciesEnum])},error:t=>{console.log(["initializeData species",t]),this.toast.push("Error loading species data: "+(t?.message||t?.toString()||"Unknown error"))}})}onOwnerClicked(t){this.ownerId=t.id}onOwnerRemoveClicked(t){this.ownerId=t.id,this.removeOwner(t)}onOwnerEditorCreateClicked(){this.ownerEditorCreate=!0,this.ownerEditorUpdate=!1,this.visitLister=!1,this.petCreateEditor=!1}onOwnerEditorUpdateClicked(t){this.ownerId=t.id,this.ownerEditorCreate=!1,this.ownerEditorUpdate=!0,this.visitLister=!1,this.petCreateEditor=!1}onVisitListerClicked(t){this.ownerId=t.id,this.ownerEditorCreate=!1,this.ownerEditorUpdate=!1,this.visitLister=!this.visitLister,this.petCreateEditor=!1,this.visitLister&&this.loadAllVisit()}onPetCreateEditorClicked(t){this.ownerId=t.id,this.ownerEditorCreate=!1,this.ownerEditorUpdate=!1,this.visitLister=!1,this.petCreateEditor=!0}get ownerEditorDisabled(){return this.ownerEditorCreate||this.ownerEditorUpdate||this.petCreateEditor}ownerFilterParameter(){return this.ownerFilter?"&name="+encodeURIComponent(this.ownerFilter):""}ownerSortParameter(){return"?sort=name"}onOwnerFilterClicked(t){t.preventDefault(),console.log(["onOwnerFilterClicked","Setting loading to true, current loading:",this.loading]),this.loading=!0,console.log(["onOwnerFilterClicked","Loading set to true, calling loadAllOwner"]),this.loadAllOwner()}onCreateOwner(t){this.allOwner=[t,...this.allOwner],this.ownerEditorCreate=!1,this.cdr.markForCheck()}onUpdateOwner(t){let n=this.allOwner.findIndex(r=>r.id===t.id);n>-1&&(this.allOwner=[...this.allOwner.slice(0,n),t,...this.allOwner.slice(n+1)]),this.ownerEditorUpdate=!1,this.cdr.markForCheck()}onRemoveOwner(t){let n=this.allOwner.findIndex(r=>r.id===t.id);console.log(["onRemoveOwner","Before removal, length:",this.allOwner.length,"index:",n,"owner:",t]),n>-1&&(this.allOwner=this.allOwner.filter(r=>r.id!==t.id),console.log(["onRemoveOwner","After removal, length:",this.allOwner.length]),this.cdr.markForCheck())}loadAllOwner(){let t=this.ownerSortParameter()+this.ownerFilterParameter(),n="http://localhost:8080/api/owner"+t;console.log(["loadAllOwner starting",t,"Full URL:",n,"Current loading state:",this.loading]),this.restApi.loadAllValue("/api/owner"+t).subscribe({next:r=>{console.log(["loadAllOwner success",t,"Response:",r,"Type:",typeof r,"Length:",r?.length]),this.allOwner=r,console.log(["loadAllOwner","Setting loading to false, allOwner length:",this.allOwner.length]),this.loading=!1,this.cdr.markForCheck(),console.log(["loadAllOwner","Loading state after setting to false:",this.loading])},error:r=>{console.log(["loadAllOwner error",t,"Error details:",r]),console.error("Full error object:",r),this.toast.push("Error loading owners: "+(r?.message||r?.toString()||"Unknown error")),console.log(["loadAllOwner","Setting loading to false due to error"]),this.loading=!1,this.cdr.markForCheck(),console.log(["loadAllOwner","Loading state after error:",this.loading])}})}loadAllVisit(){let t="?sort=date,desc&pet.owner.id="+this.ownerId;this.restApi.loadAllValue("/api/visit"+t).subscribe({next:n=>{console.log(["loadAllVisit",t,n]),this.allOwnerVisit=n},error:n=>{console.log(["loadAllVisit",t,n]),this.toast.push(n.toString())}})}removeOwner(t){let n=t.name||"this owner",r=n.length>30?n.substring(0,30)+"...":n;confirm(`Delete owner '${r}' permanently?`)&&this.restApi.removeValue("/api/owner/"+t.id).subscribe({next:()=>{console.log(["removeOwner",t]),this.onRemoveOwner(t),setTimeout(()=>{this.toast.push("Owner removed successfully"),this.cdr.markForCheck()},0)},error:o=>{console.log(["removeOwner",t,o]);let i="Failed to remove owner";o.status===409?i=`Cannot delete owner '${n}' because they have associated pets or visits.`:o.status===404?i=`Owner '${n}' not found. It may have already been deleted.`:o.status===500?i=`Server error while deleting owner '${n}'. Please try again later.`:o.error&&o.error.message?i=`Error: ${o.error.message}`:o.message&&(i=`Error: ${o.message}`),setTimeout(()=>{this.toast.push(i),this.cdr.markForCheck()},0)}})}onCreatePet(t){console.log(["onCreatePet",t]),this.loadAllOwner(),this.petCreateEditor=!1,this.cdr.markForCheck()}onUpdatePet(t){console.log(["onUpdatePet",t]),this.loadAllOwner(),this.petCreateEditor=!1,this.cdr.markForCheck()}onClosePetEditor(){this.petCreateEditor=!1,this.cdr.markForCheck()}static \u0275fac=function(n){return new(n||e)(T(Dt),T(Ke),T(Ln))};static \u0275cmp=Z({type:e,selectors:[["app-owner"]],decls:11,vars:6,consts:[[1,"flex","flex-col","gap-1","ml-2","mr-2"],[1,"mb-4"],[3,"ngSubmit"],[1,"flex","flex-row","gap-1","items-center","pr-2"],[1,"w-full"],["label","Filter","placeholder","Please enter filter criteria",3,"ngModelChange","ngModel","ngModelOptions"],[1,"w-min"],["type","submit","name","search",3,"outlined"],["class","h-screen flex justify-center items-center",4,"ngIf"],[4,"ngIf"],[1,"h-screen","flex","justify-center","items-center"],[1,"text-center"],["size","60","unit","px","duration","1s"],[1,"table-fixed"],[1,"justify-between"],[1,"bg-title-200"],[1,"px-2","py-3","text-left","w-1/3","table-cell"],[1,"text-title-600"],[1,"px-2","py-3","text-left","w-full","table-cell"],[1,"text-gray-600"],[1,"px-2","py-3","text-right","w-0","table-cell"],["title","Add a new owner","name","add",3,"click","disabled","outlined"],["colspan","3",1,"border-l-4","px-2"],[3,"visibleChange","create","cancel","visible","owner"],[1,"cursor-pointer","hover:bg-gray-50",3,"click","title"],[1,"px-2","py-3","text-left","table-cell"],[1,"text-sm","underline","text-blue-600"],[3,"routerLink"],[1,"flex","flex-col"],[1,"px-2","py-3","table-cell"],[1,"grid","grid-cols-1","md:grid-cols-4","items-center","gap-1","w-max"],["title","Show all visits","name","list",3,"click","disabled","outlined"],["title","Add a new pet","name","pets",3,"click","disabled","outlined"],["title","Delete an owner","name","delete",3,"click","disabled","outlined"],["title","Edit an owner","name","edit",3,"click","disabled","outlined"],[3,"visibleChange","update","cancel","visible","owner"],[3,"create","update","close","visible","ownerId","allSpeciesEnum"],[1,"p-4","bg-gray-50"],[1,"text-lg","font-semibold","mb-2"],[1,"space-y-2"],[1,"mt-2","px-4","py-2","bg-gray-500","text-white","rounded",3,"click"],[1,"border","p-2","rounded"],[1,"font-medium"],[1,"text-sm","text-gray-600"],[1,"text-sm"],["colspan","3",1,"px-2","py-3","text-center"]],template:function(n,r){n&1&&(m(0,"div",0)(1,"h1",1),w(2,"OWNER"),y(),m(3,"form",2),H("ngSubmit",function(i){return r.onOwnerFilterClicked(i)}),m(4,"div",3)(5,"div",4)(6,"app-text-field",5),je("ngModelChange",function(i){return ze(r.ownerFilter,i)||(r.ownerFilter=i),i}),y()(),m(7,"div",6),Se(8,"app-icon",7),y()()(),ec(9,TT,3,0,"div",8)(10,BT,17,4,"div",9),y()),n&2&&(v(6),Ve("ngModel",r.ownerFilter),V("ngModelOptions",Ye(5,CT)),v(2),V("outlined",!0),v(),V("ngIf",r.loading),v(),V("ngIf",!r.loading))},dependencies:[J,yf,$n,xo,Hn,No,yn,wr,Jt,zn,Ro,Oo,Xc,el],encapsulation:2})};var UT=()=>({standalone:!0}),HT=e=>["/pet",e],$T=(e,t)=>t.id;function zT(e,t){e&1&&(m(0,"div",1),Se(1,"app-spinner",2),y())}function GT(e,t){if(e&1){let n=re();m(0,"tr",14),H("click",function(){let o=N(n).$implicit,i=R(2);return x(i.onPetClicked(o))}),m(1,"td",15)(2,"span"),w(3),y()(),m(4,"td",15)(5,"div",16)(6,"a",17),w(7),y()()(),m(8,"td",15)(9,"span"),w(10),y()(),m(11,"td",18)(12,"div",19),Se(13,"app-icon",20)(14,"app-icon",21),y()()()}if(e&2){let n=t.$implicit,r=t.$index,o=R(2);oe("border-l-2",o.petId===n.id)("bg-gray-100",r%2===1),V("title",n.id.toString()),v(3),ct(n.species),v(3),V("routerLink",ro(11,HT,n.id)),v(),ct(n.name),v(3),ct(n.birthDate),v(3),V("outlined",!0),v(),V("outlined",!0)}}function WT(e,t){e&1&&(m(0,"tr")(1,"td",22)(2,"span"),w(3,"No pets found"),y()()())}function qT(e,t){if(e&1){let n=re();m(0,"div",3)(1,"app-select",4),je("ngModelChange",function(o){N(n);let i=R();return ze(i.petOwnerId,o)||(i.petOwnerId=o),x(o)}),y()(),m(2,"table",5)(3,"thead",6)(4,"tr",7)(5,"th",8)(6,"span",9),w(7,"Species"),y()(),m(8,"th",8)(9,"span",9),w(10,"Name"),y()(),m(11,"th",8)(12,"span",10),w(13,"Born"),y()(),m(14,"th",11),Se(15,"app-icon",12),y()()(),m(16,"tbody"),xt(17,GT,15,13,"tr",13,$T,!1,WT,4,0,"tr"),y()()}if(e&2){let n=R();v(),Ve("ngModel",n.petOwnerId),V("ngModelOptions",Ye(5,UT))("allItem",n.allOwnerItem),v(14),V("outlined",!0),v(2),Rt(n.allPet)}}var nl=class e{constructor(t,n){this.restApi=t;this.toast=n}loading=!0;allOwnerItem=[];allSpeciesEnum=[];allPet=[];petOwnerId;petId;ngOnInit(){this.initializeData()}initializeData(){try{this.loading=!0,this.restApi.loadAllValue("/api/owner?sort=name,asc").subscribe({next:t=>{this.allOwnerItem=t.map(n=>({value:n.id,text:n.name+", "+n.address})),this.restApi.loadAllValue("/api/enum/species").subscribe({next:n=>{this.allSpeciesEnum=n.map(r=>({value:r.value,text:r.name})),console.log(["initializeData",this.allOwnerItem,this.allSpeciesEnum]),this.loading=!1},error:n=>{console.log(["initializeData species",n]),this.toast.push(n?.toString()||"Error loading species data"),this.loading=!1}})},error:t=>{console.log(["initializeData owner",t]),this.toast.push(t?.toString()||"Error loading owner data"),this.loading=!1}})}catch(t){console.log(["initializeData",t]),this.toast.push(t?.toString()||"Error loading data"),this.loading=!1}}onPetClicked(t){this.petId=t.id}static \u0275fac=function(n){return new(n||e)(T(Dt),T(Ke))};static \u0275cmp=Z({type:e,selectors:[["app-pet"]],decls:5,vars:1,consts:[[1,"flex","flex-col","gap-1","ml-2","mr-2"],[1,"h-screen","flex","justify-center","items-center"],["size","60","unit","px","duration","1s"],[1,"mb-4"],["label","Owner",3,"ngModelChange","ngModel","ngModelOptions","allItem"],[1,"table-fixed"],[1,"justify-between"],[1,"bg-title-200"],[1,"px-2","py-3","text-left","w-1/4","table-cell"],[1,"text-title-600"],[1,"text-gray-600"],[1,"px-2","py-3","text-right","w-0","table-cell"],["title","add a new Pet","name","add",3,"outlined"],[1,"cursor-pointer","hover:bg-gray-50",3,"title","border-l-2","bg-gray-100"],[1,"cursor-pointer","hover:bg-gray-50",3,"click","title"],[1,"px-2","py-3","text-left","table-cell"],[1,"text-sm","underline","text-blue-600"],[3,"routerLink"],[1,"px-2","py-3","table-cell"],[1,"grid","grid-cols-1","md:grid-cols-2","items-center","gap-1","w-max"],["title","Delete a pet","name","delete",3,"outlined"],["title","Edit a pet","name","edit",3,"outlined"],["colspan","4",1,"px-2","py-3","text-center"]],template:function(n,r){n&1&&(m(0,"h1"),w(1,"PET"),y(),m(2,"div",0),we(3,zT,2,0,"div",1)(4,qT,20,6),y()),n&2&&(v(3),be(r.loading?3:4))},dependencies:[J,$n,Hn,yn,Jt,Ro,Oo,Po],encapsulation:2})};var yD=[{path:"",redirectTo:"/home",pathMatch:"full"},{path:"home",component:Et},{path:"help",component:Et},{path:"owner",component:tl},{path:"owner/:id",component:Et},{path:"pet",component:nl},{path:"pet/:id",component:Et},{path:"visit",component:Et},{path:"vet",component:Et},{path:"vet/:id",component:Et},{path:"enum/skill",component:Et},{path:"enum/species",component:Et},{path:"**",redirectTo:"/home"}];var DD={providers:[Ru(),cf(),sp(yD),Pf(Ff())]};var ZT=(e,t)=>t.id;function YT(e,t){if(e&1){let n=re();m(0,"li",1)(1,"div",2),w(2),y(),m(3,"div",3),H("click",function(){let o=N(n).$implicit,i=R();return x(i.removeToast(o.id))})("keydown.escape",function(){let o=N(n).$implicit,i=R();return x(i.removeToast(o.id))}),m(4,"span"),w(5,"\u2715"),y()()()}if(e&2){let n=t.$implicit,r=R();V("ngClass",r.getToastClass(n.type)),v(2),ye(" ",n.message," ")}}var rl=class e{constructor(t){this.toastService=t}toasts=[];subscription;ngOnInit(){this.subscription=this.toastService.toasts$.subscribe(t=>{this.toasts=t})}ngOnDestroy(){this.subscription?.unsubscribe()}removeToast(t){this.toastService.remove(t)}getToastClass(t){switch(t){case"success":return"bg-green-800";case"warning":return"bg-yellow-800";case"info":return"bg-blue-800";default:return"bg-error-800"}}static \u0275fac=function(n){return new(n||e)(T(Ke))};static \u0275cmp=Z({type:e,selectors:[["app-toast"]],decls:3,vars:0,consts:[[1,"toast-container"],["role","alert",1,"toast-item","text-white","z-30",3,"ngClass"],["role","status",1,"toast-msg"],["role","button","tabindex","-1",1,"toast-btn",3,"click","keydown.escape"]],template:function(n,r){n&1&&(m(0,"ul",0),xt(1,YT,6,2,"li",1,ZT),y()),n&2&&(v(),Rt(r.toasts))},dependencies:[J,vf],styles:[".toast-container[_ngcontent-%COMP%]{top:1.5rem;right:2rem;bottom:auto;left:auto;position:fixed;margin:0;padding:0;list-style-type:none;pointer-events:none;z-index:99}.toast-item[_ngcontent-%COMP%]{width:20rem;height:auto;min-height:3.5rem;margin:0 0 .5rem;padding:0;border:none;border-radius:.125rem;position:relative;display:flex;flex-direction:row;align-items:center;overflow:hidden;will-change:transform,opacity;pointer-events:auto}.toast-msg[_ngcontent-%COMP%]{padding:.75rem .5rem;flex:1 1 0%;white-space:pre-line;word-break:break-all}.toast-btn[_ngcontent-%COMP%]{width:2rem;height:100%;font:1rem sans-serif;display:flex;align-items:center;justify-content:center;cursor:pointer;outline:none}"]})};var ol=class e{open=!1;openChange=new k;toggleOpen(){this.open=!this.open,this.openChange.emit(this.open)}static \u0275fac=function(n){return new(n||e)};static \u0275cmp=Z({type:e,selectors:[["app-icon"]],inputs:{open:"open"},outputs:{openChange:"openChange"},decls:5,vars:2,consts:[["aria-label","Menu",1,"text-title-500","hover:text-title-700","cursor-pointer","border-none","focus:outline-none","flex","items-center","justify-center","p-1",3,"click"],["width","32","height","24"],["id","top","x1","0","y1","2","x2","32","y2","2"],["id","middle","x1","0","y1","12","x2","24","y2","12"],["id","bottom","x1","0","y1","22","x2","32","y2","22"]],template:function(n,r){n&1&&(Y(0,"button",0),mt("click",function(){return r.toggleOpen()}),la(),Y(1,"svg",1),gr(2,"line",2)(3,"line",3)(4,"line",4),ee()()),n&2&&oe("open",r.open)},dependencies:[J],styles:["svg[_ngcontent-%COMP%]{min-height:24px;transition:transform .3s ease-in-out;display:block;vertical-align:middle}svg[_ngcontent-%COMP%]   line[_ngcontent-%COMP%]{stroke:currentColor;stroke-width:3;transition:transform .3s ease-in-out}.open[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{transform:scale(.7)}.open[_ngcontent-%COMP%]   #top[_ngcontent-%COMP%]{transform:translate(6px) rotate(45deg)}.open[_ngcontent-%COMP%]   #middle[_ngcontent-%COMP%]{opacity:0}.open[_ngcontent-%COMP%]   #bottom[_ngcontent-%COMP%]{transform:translate(-12px,9px) rotate(-45deg)}"]})};var il=class e{open=!1;openChange=new k;handleClick(){this.open=!1,this.openChange.emit(this.open)}static \u0275fac=function(n){return new(n||e)};static \u0275cmp=Z({type:e,selectors:[["app-logo"]],inputs:{open:"open"},outputs:{openChange:"openChange"},decls:3,vars:2,consts:[["routerLink","/home",1,"text-title-500","hover:text-title-700","cursor-pointer","border-none","focus:outline-none","flex","items-center",3,"click"]],template:function(n,r){n&1&&(m(0,"a",0),H("click",function(){return r.handleClick()}),m(1,"span"),w(2,"Petclinic"),y()()),n&2&&oe("open",r.open)},dependencies:[J,Jt],encapsulation:2})};function QT(e,t){if(e&1){let n=re();m(0,"aside",7)(1,"nav",10)(2,"div",11)(3,"div",12)(4,"span",13),w(5,"Client"),y(),m(6,"div",14)(7,"a",15),H("click",function(){N(n);let o=R();return x(o.handleClick())}),w(8,"Owner"),y(),m(9,"a",16),H("click",function(){N(n);let o=R();return x(o.handleClick())}),w(10,"Pet"),y()()(),m(11,"div",12)(12,"span",13),w(13,"Clinic"),y(),m(14,"div",14)(15,"a",17),H("click",function(){N(n);let o=R();return x(o.handleClick())}),w(16,"Visit"),y(),m(17,"a",18),H("click",function(){N(n);let o=R();return x(o.handleClick())}),w(18,"Vet"),y(),m(19,"a",19),H("click",function(){N(n);let o=R();return x(o.handleClick())}),w(20,"Skill"),y(),m(21,"a",20),H("click",function(){N(n);let o=R();return x(o.handleClick())}),w(22,"Species"),y()()()()()()}}var sl=class e{menuVisible=!1;handleClick(){this.menuVisible=!1}static \u0275fac=function(n){return new(n||e)};static \u0275cmp=Z({type:e,selectors:[["app-root"]],decls:16,vars:3,consts:[[1,"flex","flex-col","h-screen"],[1,"flex","justify-between","items-center","bg-title-200","px-4","py-2","h-12"],[1,"flex","flex-row","items-center","text-lg","text-title-600","gap-2"],[3,"openChange","open"],[1,"flex","flex-row","items-center","text-lg","text-title-600"],["routerLink","/help","title","Help",1,"flex","items-center","justify-center","w-8","h-8","rounded-full","hover:bg-title-300","transition-colors","duration-200",3,"click"],[1,"flex-1","overflow-y-auto"],[1,"w-72","h-full","pointer-events-none","menu-transition"],[1,"flex","justify-center","bg-title-200","p-2","h-10"],[1,"flex","flex-row","text-sm","text-title-600","gap-1"],[1,"absolute","flex","w-full","h-full","pointer-events-auto","z-10","bg-white"],[1,"w-full"],[1,"flex","flex-col","p-2","text-gray-600","gap-1"],[1,"text-lg","text-gray-900","capitalize"],[1,"flex","flex-col","p-4","text-gray-600","gap-1"],["routerLink","/owner",3,"click"],["routerLink","/pet",3,"click"],["routerLink","/visit",3,"click"],["routerLink","/vet",3,"click"],["routerLink","/enum/skill",3,"click"],["routerLink","/enum/species",3,"click"]],template:function(n,r){n&1&&(m(0,"article",0)(1,"header",1)(2,"nav",2)(3,"app-icon",3),H("openChange",function(i){return r.menuVisible=i}),y(),m(4,"app-logo",3),H("openChange",function(i){return r.menuVisible=i}),y()(),m(5,"nav",4)(6,"a",5),H("click",function(){return r.handleClick()}),w(7," ? "),y()()(),m(8,"main",6),Se(9,"app-toast"),we(10,QT,23,0,"aside",7),Se(11,"router-outlet"),y(),m(12,"footer",8)(13,"nav",9)(14,"span"),w(15,"Petclinic Angular Client"),y()()()()),n&2&&(v(3),V("open",r.menuVisible),v(),V("open",r.menuVisible),v(6),be(r.menuVisible?10:-1))},dependencies:[es,Jt,J,rl,ol,il],styles:[".menu-transition[_ngcontent-%COMP%]{transition:transform .2s ease-in-out;transform:translate(-300px);opacity:1}"]})};Mf(sl,DD).catch(e=>console.error(e));
