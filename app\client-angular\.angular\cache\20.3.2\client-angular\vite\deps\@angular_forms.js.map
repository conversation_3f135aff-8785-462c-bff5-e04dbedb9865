{"version": 3, "sources": ["../../../../../../node_modules/@angular/forms/fesm2022/forms.mjs"], "sourcesContent": ["/**\n * @license Angular v20.3.1\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, forwardRef, Optional, Inject, ɵisPromise as _isPromise, ɵisSubscribable as _isSubscribable, ɵRuntimeError as _RuntimeError, Self, untracked, computed, signal, EventEmitter, Input, Host, SkipSelf, booleanAttribute, ChangeDetectorRef, Output, Injectable, inject, ApplicationRef, DestroyRef, afterNextRender, NgModule, Version } from '@angular/core';\nimport { ɵgetDOM as _getDOM } from '@angular/common';\nimport { forkJoin, from, Subject } from 'rxjs';\nimport { map } from 'rxjs/operators';\n\n/**\n * Base class for all ControlValueAccessor classes defined in Forms package.\n * Contains common logic and utility functions.\n *\n * Note: this is an *internal-only* class and should not be extended or used directly in\n * applications code.\n */\nclass BaseControlValueAccessor {\n  _renderer;\n  _elementRef;\n  /**\n   * The registered callback function called when a change or input event occurs on the input\n   * element.\n   * @docs-private\n   */\n  onChange = _ => {};\n  /**\n   * The registered callback function called when a blur event occurs on the input element.\n   * @docs-private\n   */\n  onTouched = () => {};\n  constructor(_renderer, _elementRef) {\n    this._renderer = _renderer;\n    this._elementRef = _elementRef;\n  }\n  /**\n   * Helper method that sets a property on a target element using the current Renderer\n   * implementation.\n   * @docs-private\n   */\n  setProperty(key, value) {\n    this._renderer.setProperty(this._elementRef.nativeElement, key, value);\n  }\n  /**\n   * Registers a function called when the control is touched.\n   * @docs-private\n   */\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  /**\n   * Registers a function called when the control value changes.\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  /**\n   * Sets the \"disabled\" property on the range input element.\n   * @docs-private\n   */\n  setDisabledState(isDisabled) {\n    this.setProperty('disabled', isDisabled);\n  }\n  static ɵfac = function BaseControlValueAccessor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BaseControlValueAccessor)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BaseControlValueAccessor\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BaseControlValueAccessor, [{\n    type: Directive\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], null);\n})();\n/**\n * Base class for all built-in ControlValueAccessor classes (except DefaultValueAccessor, which is\n * used in case no other CVAs can be found). We use this class to distinguish between default CVA,\n * built-in CVAs and custom CVAs, so that Forms logic can recognize built-in CVAs and treat custom\n * ones with higher priority (when both built-in and custom CVAs are present).\n *\n * Note: this is an *internal-only* class and should not be extended or used directly in\n * applications code.\n */\nclass BuiltInControlValueAccessor extends BaseControlValueAccessor {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵBuiltInControlValueAccessor_BaseFactory;\n    return function BuiltInControlValueAccessor_Factory(__ngFactoryType__) {\n      return (ɵBuiltInControlValueAccessor_BaseFactory || (ɵBuiltInControlValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(BuiltInControlValueAccessor)))(__ngFactoryType__ || BuiltInControlValueAccessor);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: BuiltInControlValueAccessor,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BuiltInControlValueAccessor, [{\n    type: Directive\n  }], null, null);\n})();\n/**\n * Used to provide a `ControlValueAccessor` for form controls.\n *\n * See `DefaultValueAccessor` for how to implement one.\n *\n * @publicApi\n */\nconst NG_VALUE_ACCESSOR = new InjectionToken(ngDevMode ? 'NgValueAccessor' : '');\nconst CHECKBOX_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => CheckboxControlValueAccessor),\n  multi: true\n};\n/**\n * @description\n * A `ControlValueAccessor` for writing a value and listening to changes on a checkbox input\n * element.\n *\n * @usageNotes\n *\n * ### Using a checkbox with a reactive form.\n *\n * The following example shows how to use a checkbox with a reactive form.\n *\n * ```ts\n * const rememberLoginControl = new FormControl();\n * ```\n *\n * ```html\n * <input type=\"checkbox\" [formControl]=\"rememberLoginControl\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass CheckboxControlValueAccessor extends BuiltInControlValueAccessor {\n  /**\n   * Sets the \"checked\" property on the input element.\n   * @docs-private\n   */\n  writeValue(value) {\n    this.setProperty('checked', value);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵCheckboxControlValueAccessor_BaseFactory;\n    return function CheckboxControlValueAccessor_Factory(__ngFactoryType__) {\n      return (ɵCheckboxControlValueAccessor_BaseFactory || (ɵCheckboxControlValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(CheckboxControlValueAccessor)))(__ngFactoryType__ || CheckboxControlValueAccessor);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CheckboxControlValueAccessor,\n    selectors: [[\"input\", \"type\", \"checkbox\", \"formControlName\", \"\"], [\"input\", \"type\", \"checkbox\", \"formControl\", \"\"], [\"input\", \"type\", \"checkbox\", \"ngModel\", \"\"]],\n    hostBindings: function CheckboxControlValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function CheckboxControlValueAccessor_change_HostBindingHandler($event) {\n          return ctx.onChange($event.target.checked);\n        })(\"blur\", function CheckboxControlValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([CHECKBOX_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CheckboxControlValueAccessor, [{\n    type: Directive,\n    args: [{\n      selector: 'input[type=checkbox][formControlName],input[type=checkbox][formControl],input[type=checkbox][ngModel]',\n      host: {\n        '(change)': 'onChange($any($event.target).checked)',\n        '(blur)': 'onTouched()'\n      },\n      providers: [CHECKBOX_VALUE_ACCESSOR],\n      standalone: false\n    }]\n  }], null, null);\n})();\nconst DEFAULT_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => DefaultValueAccessor),\n  multi: true\n};\n/**\n * We must check whether the agent is Android because composition events\n * behave differently between iOS and Android.\n */\nfunction _isAndroid() {\n  const userAgent = _getDOM() ? _getDOM().getUserAgent() : '';\n  return /android (\\d+)/.test(userAgent.toLowerCase());\n}\n/**\n * @description\n * Provide this token to control if form directives buffer IME input until\n * the \"compositionend\" event occurs.\n * @publicApi\n */\nconst COMPOSITION_BUFFER_MODE = new InjectionToken(ngDevMode ? 'CompositionEventMode' : '');\n/**\n * The default `ControlValueAccessor` for writing a value and listening to changes on input\n * elements. The accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n *\n * @usageNotes\n *\n * ### Using the default value accessor\n *\n * The following example shows how to use an input element that activates the default value accessor\n * (in this case, a text field).\n *\n * ```ts\n * const firstNameControl = new FormControl();\n * ```\n *\n * ```html\n * <input type=\"text\" [formControl]=\"firstNameControl\">\n * ```\n *\n * This value accessor is used by default for `<input type=\"text\">` and `<textarea>` elements, but\n * you could also use it for custom components that have similar behavior and do not require special\n * processing. In order to attach the default value accessor to a custom element, add the\n * `ngDefaultControl` attribute as shown below.\n *\n * ```html\n * <custom-input-component ngDefaultControl [(ngModel)]=\"value\"></custom-input-component>\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass DefaultValueAccessor extends BaseControlValueAccessor {\n  _compositionMode;\n  /** Whether the user is creating a composition string (IME events). */\n  _composing = false;\n  constructor(renderer, elementRef, _compositionMode) {\n    super(renderer, elementRef);\n    this._compositionMode = _compositionMode;\n    if (this._compositionMode == null) {\n      this._compositionMode = !_isAndroid();\n    }\n  }\n  /**\n   * Sets the \"value\" property on the input element.\n   * @docs-private\n   */\n  writeValue(value) {\n    const normalizedValue = value == null ? '' : value;\n    this.setProperty('value', normalizedValue);\n  }\n  /** @internal */\n  _handleInput(value) {\n    if (!this._compositionMode || this._compositionMode && !this._composing) {\n      this.onChange(value);\n    }\n  }\n  /** @internal */\n  _compositionStart() {\n    this._composing = true;\n  }\n  /** @internal */\n  _compositionEnd(value) {\n    this._composing = false;\n    this._compositionMode && this.onChange(value);\n  }\n  static ɵfac = function DefaultValueAccessor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DefaultValueAccessor)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(COMPOSITION_BUFFER_MODE, 8));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DefaultValueAccessor,\n    selectors: [[\"input\", \"formControlName\", \"\", 3, \"type\", \"checkbox\"], [\"textarea\", \"formControlName\", \"\"], [\"input\", \"formControl\", \"\", 3, \"type\", \"checkbox\"], [\"textarea\", \"formControl\", \"\"], [\"input\", \"ngModel\", \"\", 3, \"type\", \"checkbox\"], [\"textarea\", \"ngModel\", \"\"], [\"\", \"ngDefaultControl\", \"\"]],\n    hostBindings: function DefaultValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function DefaultValueAccessor_input_HostBindingHandler($event) {\n          return ctx._handleInput($event.target.value);\n        })(\"blur\", function DefaultValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        })(\"compositionstart\", function DefaultValueAccessor_compositionstart_HostBindingHandler() {\n          return ctx._compositionStart();\n        })(\"compositionend\", function DefaultValueAccessor_compositionend_HostBindingHandler($event) {\n          return ctx._compositionEnd($event.target.value);\n        });\n      }\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([DEFAULT_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultValueAccessor, [{\n    type: Directive,\n    args: [{\n      selector: 'input:not([type=checkbox])[formControlName],textarea[formControlName],input:not([type=checkbox])[formControl],textarea[formControl],input:not([type=checkbox])[ngModel],textarea[ngModel],[ngDefaultControl]',\n      // TODO: vsavkin replace the above selector with the one below it once\n      // https://github.com/angular/angular/issues/3011 is implemented\n      // selector: '[ngModel],[formControl],[formControlName]',\n      host: {\n        '(input)': '_handleInput($any($event.target).value)',\n        '(blur)': 'onTouched()',\n        '(compositionstart)': '_compositionStart()',\n        '(compositionend)': '_compositionEnd($any($event.target).value)'\n      },\n      providers: [DEFAULT_VALUE_ACCESSOR],\n      standalone: false\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [COMPOSITION_BUFFER_MODE]\n    }]\n  }], null);\n})();\nfunction isEmptyInputValue(value) {\n  return value == null || lengthOrSize(value) === 0;\n}\n/**\n * Extract the length property in case it's an array or a string.\n * Extract the size property in case it's a set.\n * Return null else.\n * @param value Either an array, set or undefined.\n */\nfunction lengthOrSize(value) {\n  // non-strict comparison is intentional, to check for both `null` and `undefined` values\n  if (value == null) {\n    return null;\n  } else if (Array.isArray(value) || typeof value === 'string') {\n    return value.length;\n  } else if (value instanceof Set) {\n    return value.size;\n  }\n  return null;\n}\n/**\n * @description\n * An `InjectionToken` for registering additional synchronous validators used with\n * `AbstractControl`s.\n *\n * @see {@link NG_ASYNC_VALIDATORS}\n *\n * @usageNotes\n *\n * ### Providing a custom validator\n *\n * The following example registers a custom validator directive. Adding the validator to the\n * existing collection of validators requires the `multi: true` option.\n *\n * ```ts\n * @Directive({\n *   selector: '[customValidator]',\n *   providers: [{provide: NG_VALIDATORS, useExisting: forwardRef(() => CustomValidatorDirective), multi: true}]\n * })\n * class CustomValidatorDirective implements Validator {\n *   validate(control: AbstractControl): ValidationErrors | null {\n *     return { 'custom': true };\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nconst NG_VALIDATORS = new InjectionToken(ngDevMode ? 'NgValidators' : '');\n/**\n * @description\n * An `InjectionToken` for registering additional asynchronous validators used with\n * `AbstractControl`s.\n *\n * @see {@link NG_VALIDATORS}\n *\n * @usageNotes\n *\n * ### Provide a custom async validator directive\n *\n * The following example implements the `AsyncValidator` interface to create an\n * async validator directive with a custom error key.\n *\n * ```ts\n * @Directive({\n *   selector: '[customAsyncValidator]',\n *   providers: [{provide: NG_ASYNC_VALIDATORS, useExisting: CustomAsyncValidatorDirective, multi:\n * true}]\n * })\n * class CustomAsyncValidatorDirective implements AsyncValidator {\n *   validate(control: AbstractControl): Promise<ValidationErrors|null> {\n *     return Promise.resolve({'custom': true});\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nconst NG_ASYNC_VALIDATORS = new InjectionToken(ngDevMode ? 'NgAsyncValidators' : '');\n/**\n * A regular expression that matches valid e-mail addresses.\n *\n * At a high level, this regexp matches e-mail addresses of the format `local-part@tld`, where:\n * - `local-part` consists of one or more of the allowed characters (alphanumeric and some\n *   punctuation symbols).\n * - `local-part` cannot begin or end with a period (`.`).\n * - `local-part` cannot be longer than 64 characters.\n * - `tld` consists of one or more `labels` separated by periods (`.`). For example `localhost` or\n *   `foo.com`.\n * - A `label` consists of one or more of the allowed characters (alphanumeric, dashes (`-`) and\n *   periods (`.`)).\n * - A `label` cannot begin or end with a dash (`-`) or a period (`.`).\n * - A `label` cannot be longer than 63 characters.\n * - The whole address cannot be longer than 254 characters.\n *\n * ## Implementation background\n *\n * This regexp was ported over from AngularJS (see there for git history):\n * https://github.com/angular/angular.js/blob/c133ef836/src/ng/directive/input.js#L27\n * It is based on the\n * [WHATWG version](https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address) with\n * some enhancements to incorporate more RFC rules (such as rules related to domain names and the\n * lengths of different parts of the address). The main differences from the WHATWG version are:\n *   - Disallow `local-part` to begin or end with a period (`.`).\n *   - Disallow `local-part` length to exceed 64 characters.\n *   - Disallow total address length to exceed 254 characters.\n *\n * See [this commit](https://github.com/angular/angular.js/commit/f3f5cf72e) for more details.\n */\nconst EMAIL_REGEXP = /^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n/**\n * @description\n * Provides a set of built-in validators that can be used by form controls.\n *\n * A validator is a function that processes a `FormControl` or collection of\n * controls and returns an error map or null. A null map means that validation has passed.\n *\n * @see [Form Validation](guide/forms/form-validation)\n *\n * @publicApi\n */\nclass Validators {\n  /**\n   * @description\n   * Validator that requires the control's value to be greater than or equal to the provided number.\n   *\n   * @usageNotes\n   *\n   * ### Validate against a minimum of 3\n   *\n   * ```ts\n   * const control = new FormControl(2, Validators.min(3));\n   *\n   * console.log(control.errors); // {min: {min: 3, actual: 2}}\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `min` property if the validation check fails, otherwise `null`.\n   *\n   * @see {@link /api/forms/AbstractControl#updateValueAndValidity updateValueAndValidity}\n   *\n   */\n  static min(min) {\n    return minValidator(min);\n  }\n  /**\n   * @description\n   * Validator that requires the control's value to be less than or equal to the provided number.\n   *\n   * @usageNotes\n   *\n   * ### Validate against a maximum of 15\n   *\n   * ```ts\n   * const control = new FormControl(16, Validators.max(15));\n   *\n   * console.log(control.errors); // {max: {max: 15, actual: 16}}\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `max` property if the validation check fails, otherwise `null`.\n   *\n   * @see {@link /api/forms/AbstractControl#updateValueAndValidity updateValueAndValidity}\n   *\n   */\n  static max(max) {\n    return maxValidator(max);\n  }\n  /**\n   * @description\n   * Validator that requires the control have a non-empty value.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field is non-empty\n   *\n   * ```ts\n   * const control = new FormControl('', Validators.required);\n   *\n   * console.log(control.errors); // {required: true}\n   * ```\n   *\n   * @returns An error map with the `required` property\n   * if the validation check fails, otherwise `null`.\n   *\n   * @see {@link /api/forms/AbstractControl#updateValueAndValidity updateValueAndValidity}\n   *\n   */\n  static required(control) {\n    return requiredValidator(control);\n  }\n  /**\n   * @description\n   * Validator that requires the control's value be true. This validator is commonly\n   * used for required checkboxes.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field value is true\n   *\n   * ```ts\n   * const control = new FormControl('some value', Validators.requiredTrue);\n   *\n   * console.log(control.errors); // {required: true}\n   * ```\n   *\n   * @returns An error map that contains the `required` property\n   * set to `true` if the validation check fails, otherwise `null`.\n   *\n   * @see {@link /api/forms/AbstractControl#updateValueAndValidity updateValueAndValidity}\n   *\n   */\n  static requiredTrue(control) {\n    return requiredTrueValidator(control);\n  }\n  /**\n   * @description\n   * Validator that requires the control's value pass an email validation test.\n   *\n   * Tests the value using a [regular\n   * expression](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions)\n   * pattern suitable for common use cases. The pattern is based on the definition of a valid email\n   * address in the [WHATWG HTML\n   * specification](https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address) with\n   * some enhancements to incorporate more RFC rules (such as rules related to domain names and the\n   * lengths of different parts of the address).\n   *\n   * The differences from the WHATWG version include:\n   * - Disallow `local-part` (the part before the `@` symbol) to begin or end with a period (`.`).\n   * - Disallow `local-part` to be longer than 64 characters.\n   * - Disallow the whole address to be longer than 254 characters.\n   *\n   * If this pattern does not satisfy your business needs, you can use `Validators.pattern()` to\n   * validate the value against a different pattern.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field matches a valid email pattern\n   *\n   * ```ts\n   * const control = new FormControl('bad@', Validators.email);\n   *\n   * console.log(control.errors); // {email: true}\n   * ```\n   *\n   * @returns An error map with the `email` property\n   * if the validation check fails, otherwise `null`.\n   *\n   * @see {@link /api/forms/AbstractControl#updateValueAndValidity updateValueAndValidity}\n   *\n   */\n  static email(control) {\n    return emailValidator(control);\n  }\n  /**\n   * @description\n   * Validator that requires the number of items in the control's value to be greater than or equal\n   * to the provided minimum length. This validator is also provided by default if you use\n   * the HTML5 `minlength` attribute. Note that the `minLength` validator is intended to be used\n   * only for types that have a numeric `length` or `size` property, such as strings, arrays or\n   * sets. The `minLength` validator logic is also not invoked for values when their `length` or\n   * `size` property is 0 (for example in case of an empty string or an empty array), to support\n   * optional controls. You can use the standard `required` validator if empty values should not be\n   * considered valid.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field has a minimum of 3 characters\n   *\n   * ```ts\n   * const control = new FormControl('ng', Validators.minLength(3));\n   *\n   * console.log(control.errors); // {minlength: {requiredLength: 3, actualLength: 2}}\n   * ```\n   *\n   * ```html\n   * <input minlength=\"5\">\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `minlength` property if the validation check fails, otherwise `null`.\n   *\n   * @see {@link /api/forms/AbstractControl#updateValueAndValidity updateValueAndValidity}\n   *\n   */\n  static minLength(minLength) {\n    return minLengthValidator(minLength);\n  }\n  /**\n   * @description\n   * Validator that requires the number of items in the control's value to be less than or equal\n   * to the provided maximum length. This validator is also provided by default if you use\n   * the HTML5 `maxlength` attribute. Note that the `maxLength` validator is intended to be used\n   * only for types that have a numeric `length` or `size` property, such as strings, arrays or\n   * sets.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field has maximum of 5 characters\n   *\n   * ```ts\n   * const control = new FormControl('Angular', Validators.maxLength(5));\n   *\n   * console.log(control.errors); // {maxlength: {requiredLength: 5, actualLength: 7}}\n   * ```\n   *\n   * ```html\n   * <input maxlength=\"5\">\n   * ```\n   *\n   * @returns A validator function that returns an error map with the\n   * `maxlength` property if the validation check fails, otherwise `null`.\n   *\n   * @see {@link /api/forms/AbstractControl#updateValueAndValidity updateValueAndValidity}\n   *\n   */\n  static maxLength(maxLength) {\n    return maxLengthValidator(maxLength);\n  }\n  /**\n   * @description\n   * Validator that requires the control's value to match a regex pattern. This validator is also\n   * provided by default if you use the HTML5 `pattern` attribute.\n   *\n   * @usageNotes\n   *\n   * ### Validate that the field only contains letters or spaces\n   *\n   * ```ts\n   * const control = new FormControl('1', Validators.pattern('[a-zA-Z ]*'));\n   *\n   * console.log(control.errors); // {pattern: {requiredPattern: '^[a-zA-Z ]*$', actualValue: '1'}}\n   * ```\n   *\n   * ```html\n   * <input pattern=\"[a-zA-Z ]*\">\n   * ```\n   *\n   * ### Pattern matching with the global or sticky flag\n   *\n   * `RegExp` objects created with the `g` or `y` flags that are passed into `Validators.pattern`\n   * can produce different results on the same input when validations are run consecutively. This is\n   * due to how the behavior of `RegExp.prototype.test` is\n   * specified in [ECMA-262](https://tc39.es/ecma262/#sec-regexpbuiltinexec)\n   * (`RegExp` preserves the index of the last match when the global or sticky flag is used).\n   * Due to this behavior, it is recommended that when using\n   * `Validators.pattern` you **do not** pass in a `RegExp` object with either the global or sticky\n   * flag enabled.\n   *\n   * ```ts\n   * // Not recommended (since the `g` flag is used)\n   * const controlOne = new FormControl('1', Validators.pattern(/foo/g));\n   *\n   * // Good\n   * const controlTwo = new FormControl('1', Validators.pattern(/foo/));\n   * ```\n   *\n   * @param pattern A regular expression to be used as is to test the values, or a string.\n   * If a string is passed, the `^` character is prepended and the `$` character is\n   * appended to the provided string (if not already present), and the resulting regular\n   * expression is used to test the values.\n   *\n   * @returns A validator function that returns an error map with the\n   * `pattern` property if the validation check fails, otherwise `null`.\n   *\n   * @see {@link /api/forms/AbstractControl#updateValueAndValidity updateValueAndValidity}\n   *\n   */\n  static pattern(pattern) {\n    return patternValidator(pattern);\n  }\n  /**\n   * @description\n   * Validator that performs no operation.\n   *\n   * @see {@link /api/forms/AbstractControl#updateValueAndValidity updateValueAndValidity}\n   *\n   */\n  static nullValidator(control) {\n    return nullValidator();\n  }\n  static compose(validators) {\n    return compose(validators);\n  }\n  /**\n   * @description\n   * Compose multiple async validators into a single function that returns the union\n   * of the individual error objects for the provided control.\n   *\n   * @returns A validator function that returns an error map with the\n   * merged error objects of the async validators if the validation check fails, otherwise `null`.\n   *\n   * @see {@link /api/forms/AbstractControl#updateValueAndValidity updateValueAndValidity}\n   *\n   */\n  static composeAsync(validators) {\n    return composeAsync(validators);\n  }\n}\n/**\n * Validator that requires the control's value to be greater than or equal to the provided number.\n * See `Validators.min` for additional information.\n */\nfunction minValidator(min) {\n  return control => {\n    if (control.value == null || min == null) {\n      return null; // don't validate empty values to allow optional controls\n    }\n    const value = parseFloat(control.value);\n    // Controls with NaN values after parsing should be treated as not having a\n    // minimum, per the HTML forms spec: https://www.w3.org/TR/html5/forms.html#attr-input-min\n    return !isNaN(value) && value < min ? {\n      'min': {\n        'min': min,\n        'actual': control.value\n      }\n    } : null;\n  };\n}\n/**\n * Validator that requires the control's value to be less than or equal to the provided number.\n * See `Validators.max` for additional information.\n */\nfunction maxValidator(max) {\n  return control => {\n    if (control.value == null || max == null) {\n      return null; // don't validate empty values to allow optional controls\n    }\n    const value = parseFloat(control.value);\n    // Controls with NaN values after parsing should be treated as not having a\n    // maximum, per the HTML forms spec: https://www.w3.org/TR/html5/forms.html#attr-input-max\n    return !isNaN(value) && value > max ? {\n      'max': {\n        'max': max,\n        'actual': control.value\n      }\n    } : null;\n  };\n}\n/**\n * Validator that requires the control have a non-empty value.\n * See `Validators.required` for additional information.\n */\nfunction requiredValidator(control) {\n  return isEmptyInputValue(control.value) ? {\n    'required': true\n  } : null;\n}\n/**\n * Validator that requires the control's value be true. This validator is commonly\n * used for required checkboxes.\n * See `Validators.requiredTrue` for additional information.\n */\nfunction requiredTrueValidator(control) {\n  return control.value === true ? null : {\n    'required': true\n  };\n}\n/**\n * Validator that requires the control's value pass an email validation test.\n * See `Validators.email` for additional information.\n */\nfunction emailValidator(control) {\n  if (isEmptyInputValue(control.value)) {\n    return null; // don't validate empty values to allow optional controls\n  }\n  return EMAIL_REGEXP.test(control.value) ? null : {\n    'email': true\n  };\n}\n/**\n * Validator that requires the number of items in the control's value to be greater than or equal\n * to the provided minimum length. See `Validators.minLength` for additional information.\n *\n * The minLengthValidator respects every length property in an object, regardless of whether it's an array.\n * For example, the object {id: 1, length: 0, width: 0} should be validated.\n */\nfunction minLengthValidator(minLength) {\n  return control => {\n    const length = control.value?.length ?? lengthOrSize(control.value);\n    if (length === null || length === 0) {\n      // don't validate empty values to allow optional controls\n      // don't validate values without `length` or `size` property\n      return null;\n    }\n    return length < minLength ? {\n      'minlength': {\n        'requiredLength': minLength,\n        'actualLength': length\n      }\n    } : null;\n  };\n}\n/**\n * Validator that requires the number of items in the control's value to be less than or equal\n * to the provided maximum length. See `Validators.maxLength` for additional information.\n *\n * The maxLengthValidator respects every length property in an object, regardless of whether it's an array.\n * For example, the object {id: 1, length: 0, width: 0} should be validated.\n */\nfunction maxLengthValidator(maxLength) {\n  return control => {\n    const length = control.value?.length ?? lengthOrSize(control.value);\n    if (length !== null && length > maxLength) {\n      return {\n        'maxlength': {\n          'requiredLength': maxLength,\n          'actualLength': length\n        }\n      };\n    }\n    return null;\n  };\n}\n/**\n * Validator that requires the control's value to match a regex pattern.\n * See `Validators.pattern` for additional information.\n */\nfunction patternValidator(pattern) {\n  if (!pattern) return nullValidator;\n  let regex;\n  let regexStr;\n  if (typeof pattern === 'string') {\n    regexStr = '';\n    if (pattern.charAt(0) !== '^') regexStr += '^';\n    regexStr += pattern;\n    if (pattern.charAt(pattern.length - 1) !== '$') regexStr += '$';\n    regex = new RegExp(regexStr);\n  } else {\n    regexStr = pattern.toString();\n    regex = pattern;\n  }\n  return control => {\n    if (isEmptyInputValue(control.value)) {\n      return null; // don't validate empty values to allow optional controls\n    }\n    const value = control.value;\n    return regex.test(value) ? null : {\n      'pattern': {\n        'requiredPattern': regexStr,\n        'actualValue': value\n      }\n    };\n  };\n}\n/**\n * Function that has `ValidatorFn` shape, but performs no operation.\n */\nfunction nullValidator(control) {\n  return null;\n}\nfunction isPresent(o) {\n  return o != null;\n}\nfunction toObservable(value) {\n  const obs = _isPromise(value) ? from(value) : value;\n  if ((typeof ngDevMode === 'undefined' || ngDevMode) && !_isSubscribable(obs)) {\n    let errorMessage = `Expected async validator to return Promise or Observable.`;\n    // A synchronous validator will return object or null.\n    if (typeof value === 'object') {\n      errorMessage += ' Are you using a synchronous validator where an async validator is expected?';\n    }\n    throw new _RuntimeError(-1101 /* RuntimeErrorCode.WRONG_VALIDATOR_RETURN_TYPE */, errorMessage);\n  }\n  return obs;\n}\nfunction mergeErrors(arrayOfErrors) {\n  let res = {};\n  arrayOfErrors.forEach(errors => {\n    res = errors != null ? {\n      ...res,\n      ...errors\n    } : res;\n  });\n  return Object.keys(res).length === 0 ? null : res;\n}\nfunction executeValidators(control, validators) {\n  return validators.map(validator => validator(control));\n}\nfunction isValidatorFn(validator) {\n  return !validator.validate;\n}\n/**\n * Given the list of validators that may contain both functions as well as classes, return the list\n * of validator functions (convert validator classes into validator functions). This is needed to\n * have consistent structure in validators list before composing them.\n *\n * @param validators The set of validators that may contain validators both in plain function form\n *     as well as represented as a validator class.\n */\nfunction normalizeValidators(validators) {\n  return validators.map(validator => {\n    return isValidatorFn(validator) ? validator : c => validator.validate(c);\n  });\n}\n/**\n * Merges synchronous validators into a single validator function.\n * See `Validators.compose` for additional information.\n */\nfunction compose(validators) {\n  if (!validators) return null;\n  const presentValidators = validators.filter(isPresent);\n  if (presentValidators.length == 0) return null;\n  return function (control) {\n    return mergeErrors(executeValidators(control, presentValidators));\n  };\n}\n/**\n * Accepts a list of validators of different possible shapes (`Validator` and `ValidatorFn`),\n * normalizes the list (converts everything to `ValidatorFn`) and merges them into a single\n * validator function.\n */\nfunction composeValidators(validators) {\n  return validators != null ? compose(normalizeValidators(validators)) : null;\n}\n/**\n * Merges asynchronous validators into a single validator function.\n * See `Validators.composeAsync` for additional information.\n */\nfunction composeAsync(validators) {\n  if (!validators) return null;\n  const presentValidators = validators.filter(isPresent);\n  if (presentValidators.length == 0) return null;\n  return function (control) {\n    const observables = executeValidators(control, presentValidators).map(toObservable);\n    return forkJoin(observables).pipe(map(mergeErrors));\n  };\n}\n/**\n * Accepts a list of async validators of different possible shapes (`AsyncValidator` and\n * `AsyncValidatorFn`), normalizes the list (converts everything to `AsyncValidatorFn`) and merges\n * them into a single validator function.\n */\nfunction composeAsyncValidators(validators) {\n  return validators != null ? composeAsync(normalizeValidators(validators)) : null;\n}\n/**\n * Merges raw control validators with a given directive validator and returns the combined list of\n * validators as an array.\n */\nfunction mergeValidators(controlValidators, dirValidator) {\n  if (controlValidators === null) return [dirValidator];\n  return Array.isArray(controlValidators) ? [...controlValidators, dirValidator] : [controlValidators, dirValidator];\n}\n/**\n * Retrieves the list of raw synchronous validators attached to a given control.\n */\nfunction getControlValidators(control) {\n  return control._rawValidators;\n}\n/**\n * Retrieves the list of raw asynchronous validators attached to a given control.\n */\nfunction getControlAsyncValidators(control) {\n  return control._rawAsyncValidators;\n}\n/**\n * Accepts a singleton validator, an array, or null, and returns an array type with the provided\n * validators.\n *\n * @param validators A validator, validators, or null.\n * @returns A validators array.\n */\nfunction makeValidatorsArray(validators) {\n  if (!validators) return [];\n  return Array.isArray(validators) ? validators : [validators];\n}\n/**\n * Determines whether a validator or validators array has a given validator.\n *\n * @param validators The validator or validators to compare against.\n * @param validator The validator to check.\n * @returns Whether the validator is present.\n */\nfunction hasValidator(validators, validator) {\n  return Array.isArray(validators) ? validators.includes(validator) : validators === validator;\n}\n/**\n * Combines two arrays of validators into one. If duplicates are provided, only one will be added.\n *\n * @param validators The new validators.\n * @param currentValidators The base array of current validators.\n * @returns An array of validators.\n */\nfunction addValidators(validators, currentValidators) {\n  const current = makeValidatorsArray(currentValidators);\n  const validatorsToAdd = makeValidatorsArray(validators);\n  validatorsToAdd.forEach(v => {\n    // Note: if there are duplicate entries in the new validators array,\n    // only the first one would be added to the current list of validators.\n    // Duplicate ones would be ignored since `hasValidator` would detect\n    // the presence of a validator function and we update the current list in place.\n    if (!hasValidator(current, v)) {\n      current.push(v);\n    }\n  });\n  return current;\n}\nfunction removeValidators(validators, currentValidators) {\n  return makeValidatorsArray(currentValidators).filter(v => !hasValidator(validators, v));\n}\n\n/**\n * @description\n * Base class for control directives.\n *\n * This class is only used internally in the `ReactiveFormsModule` and the `FormsModule`.\n *\n * @publicApi\n */\nclass AbstractControlDirective {\n  /**\n   * @description\n   * Reports the value of the control if it is present, otherwise null.\n   */\n  get value() {\n    return this.control ? this.control.value : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is valid. A control is considered valid if no\n   * validation errors exist with the current value.\n   * If the control is not present, null is returned.\n   */\n  get valid() {\n    return this.control ? this.control.valid : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is invalid, meaning that an error exists in the input value.\n   * If the control is not present, null is returned.\n   */\n  get invalid() {\n    return this.control ? this.control.invalid : null;\n  }\n  /**\n   * @description\n   * Reports whether a control is pending, meaning that async validation is occurring and\n   * errors are not yet available for the input value. If the control is not present, null is\n   * returned.\n   */\n  get pending() {\n    return this.control ? this.control.pending : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is disabled, meaning that the control is disabled\n   * in the UI and is exempt from validation checks and excluded from aggregate\n   * values of ancestor controls. If the control is not present, null is returned.\n   */\n  get disabled() {\n    return this.control ? this.control.disabled : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is enabled, meaning that the control is included in ancestor\n   * calculations of validity or value. If the control is not present, null is returned.\n   */\n  get enabled() {\n    return this.control ? this.control.enabled : null;\n  }\n  /**\n   * @description\n   * Reports the control's validation errors. If the control is not present, null is returned.\n   */\n  get errors() {\n    return this.control ? this.control.errors : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is pristine, meaning that the user has not yet changed\n   * the value in the UI. If the control is not present, null is returned.\n   */\n  get pristine() {\n    return this.control ? this.control.pristine : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is dirty, meaning that the user has changed\n   * the value in the UI. If the control is not present, null is returned.\n   */\n  get dirty() {\n    return this.control ? this.control.dirty : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is touched, meaning that the user has triggered\n   * a `blur` event on it. If the control is not present, null is returned.\n   */\n  get touched() {\n    return this.control ? this.control.touched : null;\n  }\n  /**\n   * @description\n   * Reports the validation status of the control. Possible values include:\n   * 'VALID', 'INVALID', 'DISABLED', and 'PENDING'.\n   * If the control is not present, null is returned.\n   */\n  get status() {\n    return this.control ? this.control.status : null;\n  }\n  /**\n   * @description\n   * Reports whether the control is untouched, meaning that the user has not yet triggered\n   * a `blur` event on it. If the control is not present, null is returned.\n   */\n  get untouched() {\n    return this.control ? this.control.untouched : null;\n  }\n  /**\n   * @description\n   * Returns a multicasting observable that emits a validation status whenever it is\n   * calculated for the control. If the control is not present, null is returned.\n   */\n  get statusChanges() {\n    return this.control ? this.control.statusChanges : null;\n  }\n  /**\n   * @description\n   * Returns a multicasting observable of value changes for the control that emits every time the\n   * value of the control changes in the UI or programmatically.\n   * If the control is not present, null is returned.\n   */\n  get valueChanges() {\n    return this.control ? this.control.valueChanges : null;\n  }\n  /**\n   * @description\n   * Returns an array that represents the path from the top-level form to this control.\n   * Each index is the string name of the control on that level.\n   */\n  get path() {\n    return null;\n  }\n  /**\n   * Contains the result of merging synchronous validators into a single validator function\n   * (combined using `Validators.compose`).\n   */\n  _composedValidatorFn;\n  /**\n   * Contains the result of merging asynchronous validators into a single validator function\n   * (combined using `Validators.composeAsync`).\n   */\n  _composedAsyncValidatorFn;\n  /**\n   * Set of synchronous validators as they were provided while calling `setValidators` function.\n   * @internal\n   */\n  _rawValidators = [];\n  /**\n   * Set of asynchronous validators as they were provided while calling `setAsyncValidators`\n   * function.\n   * @internal\n   */\n  _rawAsyncValidators = [];\n  /**\n   * Sets synchronous validators for this directive.\n   * @internal\n   */\n  _setValidators(validators) {\n    this._rawValidators = validators || [];\n    this._composedValidatorFn = composeValidators(this._rawValidators);\n  }\n  /**\n   * Sets asynchronous validators for this directive.\n   * @internal\n   */\n  _setAsyncValidators(validators) {\n    this._rawAsyncValidators = validators || [];\n    this._composedAsyncValidatorFn = composeAsyncValidators(this._rawAsyncValidators);\n  }\n  /**\n   * @description\n   * Synchronous validator function composed of all the synchronous validators registered with this\n   * directive.\n   */\n  get validator() {\n    return this._composedValidatorFn || null;\n  }\n  /**\n   * @description\n   * Asynchronous validator function composed of all the asynchronous validators registered with\n   * this directive.\n   */\n  get asyncValidator() {\n    return this._composedAsyncValidatorFn || null;\n  }\n  /*\n   * The set of callbacks to be invoked when directive instance is being destroyed.\n   */\n  _onDestroyCallbacks = [];\n  /**\n   * Internal function to register callbacks that should be invoked\n   * when directive instance is being destroyed.\n   * @internal\n   */\n  _registerOnDestroy(fn) {\n    this._onDestroyCallbacks.push(fn);\n  }\n  /**\n   * Internal function to invoke all registered \"on destroy\" callbacks.\n   * Note: calling this function also clears the list of callbacks.\n   * @internal\n   */\n  _invokeOnDestroyCallbacks() {\n    this._onDestroyCallbacks.forEach(fn => fn());\n    this._onDestroyCallbacks = [];\n  }\n  /**\n   * @description\n   * Resets the control with the provided value if the control is present.\n   */\n  reset(value = undefined) {\n    if (this.control) this.control.reset(value);\n  }\n  /**\n   * @description\n   * Reports whether the control with the given path has the error specified.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```ts\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * If no path is given, this method checks for the error on the current control.\n   *\n   * @returns whether the given error is present in the control at the given path.\n   *\n   * If the control is not present, false is returned.\n   */\n  hasError(errorCode, path) {\n    return this.control ? this.control.hasError(errorCode, path) : false;\n  }\n  /**\n   * @description\n   * Reports error data for the control with the given path.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```ts\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * @returns error data for that particular error. If the control or error is not present,\n   * null is returned.\n   */\n  getError(errorCode, path) {\n    return this.control ? this.control.getError(errorCode, path) : null;\n  }\n}\n\n/**\n * @description\n * A base class for directives that contain multiple registered instances of `NgControl`.\n * Only used by the forms module.\n *\n * @publicApi\n */\nclass ControlContainer extends AbstractControlDirective {\n  /**\n   * @description\n   * The name for the control\n   */\n  name;\n  /**\n   * @description\n   * The top-level form directive for the control.\n   */\n  get formDirective() {\n    return null;\n  }\n  /**\n   * @description\n   * The path to this group.\n   */\n  get path() {\n    return null;\n  }\n}\n\n/**\n * @description\n * A base class that all `FormControl`-based directives extend. It binds a `FormControl`\n * object to a DOM element.\n *\n * @publicApi\n */\nclass NgControl extends AbstractControlDirective {\n  /**\n   * @description\n   * The parent form for the control.\n   *\n   * @internal\n   */\n  _parent = null;\n  /**\n   * @description\n   * The name for the control\n   */\n  name = null;\n  /**\n   * @description\n   * The value accessor for the control\n   */\n  valueAccessor = null;\n}\n\n// DO NOT REFACTOR!\n// Each status is represented by a separate function to make sure that\n// advanced Closure Compiler optimizations related to property renaming\n// can work correctly.\nclass AbstractControlStatus {\n  _cd;\n  constructor(cd) {\n    this._cd = cd;\n  }\n  get isTouched() {\n    // track the touched signal\n    this._cd?.control?._touched?.();\n    return !!this._cd?.control?.touched;\n  }\n  get isUntouched() {\n    return !!this._cd?.control?.untouched;\n  }\n  get isPristine() {\n    // track the pristine signal\n    this._cd?.control?._pristine?.();\n    return !!this._cd?.control?.pristine;\n  }\n  get isDirty() {\n    // pristine signal already tracked above\n    return !!this._cd?.control?.dirty;\n  }\n  get isValid() {\n    // track the status signal\n    this._cd?.control?._status?.();\n    return !!this._cd?.control?.valid;\n  }\n  get isInvalid() {\n    // status signal already tracked above\n    return !!this._cd?.control?.invalid;\n  }\n  get isPending() {\n    // status signal already tracked above\n    return !!this._cd?.control?.pending;\n  }\n  get isSubmitted() {\n    // track the submitted signal\n    this._cd?._submitted?.();\n    // We check for the `submitted` field from `NgForm` and `FormGroupDirective` classes, but\n    // we avoid instanceof checks to prevent non-tree-shakable references to those types.\n    return !!this._cd?.submitted;\n  }\n}\nconst ngControlStatusHost = {\n  '[class.ng-untouched]': 'isUntouched',\n  '[class.ng-touched]': 'isTouched',\n  '[class.ng-pristine]': 'isPristine',\n  '[class.ng-dirty]': 'isDirty',\n  '[class.ng-valid]': 'isValid',\n  '[class.ng-invalid]': 'isInvalid',\n  '[class.ng-pending]': 'isPending'\n};\nconst ngGroupStatusHost = {\n  ...ngControlStatusHost,\n  '[class.ng-submitted]': 'isSubmitted'\n};\n/**\n * @description\n * Directive automatically applied to Angular form controls that sets CSS classes\n * based on control status.\n *\n * @usageNotes\n *\n * ### CSS classes applied\n *\n * The following classes are applied as the properties become true:\n *\n * * ng-valid\n * * ng-invalid\n * * ng-pending\n * * ng-pristine\n * * ng-dirty\n * * ng-untouched\n * * ng-touched\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass NgControlStatus extends AbstractControlStatus {\n  constructor(cd) {\n    super(cd);\n  }\n  static ɵfac = function NgControlStatus_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgControlStatus)(i0.ɵɵdirectiveInject(NgControl, 2));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgControlStatus,\n    selectors: [[\"\", \"formControlName\", \"\"], [\"\", \"ngModel\", \"\"], [\"\", \"formControl\", \"\"]],\n    hostVars: 14,\n    hostBindings: function NgControlStatus_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ng-untouched\", ctx.isUntouched)(\"ng-touched\", ctx.isTouched)(\"ng-pristine\", ctx.isPristine)(\"ng-dirty\", ctx.isDirty)(\"ng-valid\", ctx.isValid)(\"ng-invalid\", ctx.isInvalid)(\"ng-pending\", ctx.isPending);\n      }\n    },\n    standalone: false,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgControlStatus, [{\n    type: Directive,\n    args: [{\n      selector: '[formControlName],[ngModel],[formControl]',\n      host: ngControlStatusHost,\n      standalone: false\n    }]\n  }], () => [{\n    type: NgControl,\n    decorators: [{\n      type: Self\n    }]\n  }], null);\n})();\n/**\n * @description\n * Directive automatically applied to Angular form groups that sets CSS classes\n * based on control status (valid/invalid/dirty/etc). On groups, this includes the additional\n * class ng-submitted.\n *\n * @see {@link NgControlStatus}\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass NgControlStatusGroup extends AbstractControlStatus {\n  constructor(cd) {\n    super(cd);\n  }\n  static ɵfac = function NgControlStatusGroup_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgControlStatusGroup)(i0.ɵɵdirectiveInject(ControlContainer, 10));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgControlStatusGroup,\n    selectors: [[\"\", \"formGroupName\", \"\"], [\"\", \"formArrayName\", \"\"], [\"\", \"ngModelGroup\", \"\"], [\"\", \"formGroup\", \"\"], [\"form\", 3, \"ngNoForm\", \"\"], [\"\", \"ngForm\", \"\"]],\n    hostVars: 16,\n    hostBindings: function NgControlStatusGroup_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"ng-untouched\", ctx.isUntouched)(\"ng-touched\", ctx.isTouched)(\"ng-pristine\", ctx.isPristine)(\"ng-dirty\", ctx.isDirty)(\"ng-valid\", ctx.isValid)(\"ng-invalid\", ctx.isInvalid)(\"ng-pending\", ctx.isPending)(\"ng-submitted\", ctx.isSubmitted);\n      }\n    },\n    standalone: false,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgControlStatusGroup, [{\n    type: Directive,\n    args: [{\n      selector: '[formGroupName],[formArrayName],[ngModelGroup],[formGroup],form:not([ngNoForm]),[ngForm]',\n      host: ngGroupStatusHost,\n      standalone: false\n    }]\n  }], () => [{\n    type: ControlContainer,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }]\n  }], null);\n})();\nconst formControlNameExample = `\n  <div [formGroup]=\"myGroup\">\n    <input formControlName=\"firstName\">\n  </div>\n\n  In your class:\n\n  this.myGroup = new FormGroup({\n      firstName: new FormControl()\n  });`;\nconst formGroupNameExample = `\n  <div [formGroup]=\"myGroup\">\n      <div formGroupName=\"person\">\n        <input formControlName=\"firstName\">\n      </div>\n  </div>\n\n  In your class:\n\n  this.myGroup = new FormGroup({\n      person: new FormGroup({ firstName: new FormControl() })\n  });`;\nconst formArrayNameExample = `\n  <div [formGroup]=\"myGroup\">\n    <div formArrayName=\"cities\">\n      <div *ngFor=\"let city of cityArray.controls; index as i\">\n        <input [formControlName]=\"i\">\n      </div>\n    </div>\n  </div>\n\n  In your class:\n\n  this.cityArray = new FormArray([new FormControl('SF')]);\n  this.myGroup = new FormGroup({\n    cities: this.cityArray\n  });`;\nconst ngModelGroupExample = `\n  <form>\n      <div ngModelGroup=\"person\">\n        <input [(ngModel)]=\"person.name\" name=\"firstName\">\n      </div>\n  </form>`;\nconst ngModelWithFormGroupExample = `\n  <div [formGroup]=\"myGroup\">\n      <input formControlName=\"firstName\">\n      <input [(ngModel)]=\"showMoreControls\" [ngModelOptions]=\"{standalone: true}\">\n  </div>\n`;\nfunction controlParentException(nameOrIndex) {\n  return new _RuntimeError(1050 /* RuntimeErrorCode.FORM_CONTROL_NAME_MISSING_PARENT */, `formControlName must be used with a parent formGroup directive. You'll want to add a formGroup\n      directive and pass it an existing FormGroup instance (you can create one in your class).\n\n      ${describeFormControl(nameOrIndex)}\n\n    Example:\n\n    ${formControlNameExample}`);\n}\nfunction describeFormControl(nameOrIndex) {\n  if (nameOrIndex == null || nameOrIndex === '') {\n    return '';\n  }\n  const valueType = typeof nameOrIndex === 'string' ? 'name' : 'index';\n  return `Affected Form Control ${valueType}: \"${nameOrIndex}\"`;\n}\nfunction ngModelGroupException() {\n  return new _RuntimeError(1051 /* RuntimeErrorCode.FORM_CONTROL_NAME_INSIDE_MODEL_GROUP */, `formControlName cannot be used with an ngModelGroup parent. It is only compatible with parents\n      that also have a \"form\" prefix: formGroupName, formArrayName, or formGroup.\n\n      Option 1:  Update the parent to be formGroupName (reactive form strategy)\n\n      ${formGroupNameExample}\n\n      Option 2: Use ngModel instead of formControlName (template-driven strategy)\n\n      ${ngModelGroupExample}`);\n}\nfunction missingFormException() {\n  return new _RuntimeError(1052 /* RuntimeErrorCode.FORM_GROUP_MISSING_INSTANCE */, `formGroup expects a FormGroup instance. Please pass one in.\n\n      Example:\n\n      ${formControlNameExample}`);\n}\nfunction groupParentException() {\n  return new _RuntimeError(1053 /* RuntimeErrorCode.FORM_GROUP_NAME_MISSING_PARENT */, `formGroupName must be used with a parent formGroup directive.  You'll want to add a formGroup\n    directive and pass it an existing FormGroup instance (you can create one in your class).\n\n    Example:\n\n    ${formGroupNameExample}`);\n}\nfunction arrayParentException() {\n  return new _RuntimeError(1054 /* RuntimeErrorCode.FORM_ARRAY_NAME_MISSING_PARENT */, `formArrayName must be used with a parent formGroup directive.  You'll want to add a formGroup\n      directive and pass it an existing FormGroup instance (you can create one in your class).\n\n      Example:\n\n      ${formArrayNameExample}`);\n}\nconst disabledAttrWarning = `\n  It looks like you're using the disabled attribute with a reactive form directive. If you set disabled to true\n  when you set up this control in your component class, the disabled attribute will actually be set in the DOM for\n  you. We recommend using this approach to avoid 'changed after checked' errors.\n\n  Example:\n  // Specify the \\`disabled\\` property at control creation time:\n  form = new FormGroup({\n    first: new FormControl({value: 'Nancy', disabled: true}, Validators.required),\n    last: new FormControl('Drew', Validators.required)\n  });\n\n  // Controls can also be enabled/disabled after creation:\n  form.get('first')?.enable();\n  form.get('last')?.disable();\n`;\nconst asyncValidatorsDroppedWithOptsWarning = `\n  It looks like you're constructing using a FormControl with both an options argument and an\n  async validators argument. Mixing these arguments will cause your async validators to be dropped.\n  You should either put all your validators in the options object, or in separate validators\n  arguments. For example:\n\n  // Using validators arguments\n  fc = new FormControl(42, Validators.required, myAsyncValidator);\n\n  // Using AbstractControlOptions\n  fc = new FormControl(42, {validators: Validators.required, asyncValidators: myAV});\n\n  // Do NOT mix them: async validators will be dropped!\n  fc = new FormControl(42, {validators: Validators.required}, /* Oops! */ myAsyncValidator);\n`;\nfunction ngModelWarning(directiveName) {\n  return `\n  It looks like you're using ngModel on the same form field as ${directiveName}.\n  Support for using the ngModel input property and ngModelChange event with\n  reactive form directives has been deprecated in Angular v6 and will be removed\n  in a future version of Angular.\n\n  For more information on this, see our API docs here:\n  https://angular.io/api/forms/${directiveName === 'formControl' ? 'FormControlDirective' : 'FormControlName'}#use-with-ngmodel\n  `;\n}\nfunction describeKey(isFormGroup, key) {\n  return isFormGroup ? `with name: '${key}'` : `at index: ${key}`;\n}\nfunction noControlsError(isFormGroup) {\n  return `\n    There are no form controls registered with this ${isFormGroup ? 'group' : 'array'} yet. If you're using ngModel,\n    you may want to check next tick (e.g. use setTimeout).\n  `;\n}\nfunction missingControlError(isFormGroup, key) {\n  return `Cannot find form control ${describeKey(isFormGroup, key)}`;\n}\nfunction missingControlValueError(isFormGroup, key) {\n  return `Must supply a value for form control ${describeKey(isFormGroup, key)}`;\n}\n\n/**\n * Reports that a control is valid, meaning that no errors exist in the input value.\n *\n * @see {@link status}\n */\nconst VALID = 'VALID';\n/**\n * Reports that a control is invalid, meaning that an error exists in the input value.\n *\n * @see {@link status}\n */\nconst INVALID = 'INVALID';\n/**\n * Reports that a control is pending, meaning that async validation is occurring and\n * errors are not yet available for the input value.\n *\n * @see {@link markAsPending}\n * @see {@link status}\n */\nconst PENDING = 'PENDING';\n/**\n * Reports that a control is disabled, meaning that the control is exempt from ancestor\n * calculations of validity or value.\n *\n * @see {@link markAsDisabled}\n * @see {@link status}\n */\nconst DISABLED = 'DISABLED';\n/**\n * Base class for every event sent by `AbstractControl.events()`\n *\n * @publicApi\n */\nclass ControlEvent {}\n/**\n * Event fired when the value of a control changes.\n *\n * @publicApi\n */\nclass ValueChangeEvent extends ControlEvent {\n  value;\n  source;\n  constructor(value, source) {\n    super();\n    this.value = value;\n    this.source = source;\n  }\n}\n/**\n * Event fired when the control's pristine state changes (pristine <=> dirty).\n *\n * @publicApi */\nclass PristineChangeEvent extends ControlEvent {\n  pristine;\n  source;\n  constructor(pristine, source) {\n    super();\n    this.pristine = pristine;\n    this.source = source;\n  }\n}\n/**\n * Event fired when the control's touched status changes (touched <=> untouched).\n *\n * @publicApi\n */\nclass TouchedChangeEvent extends ControlEvent {\n  touched;\n  source;\n  constructor(touched, source) {\n    super();\n    this.touched = touched;\n    this.source = source;\n  }\n}\n/**\n * Event fired when the control's status changes.\n *\n * @publicApi\n */\nclass StatusChangeEvent extends ControlEvent {\n  status;\n  source;\n  constructor(status, source) {\n    super();\n    this.status = status;\n    this.source = source;\n  }\n}\n/**\n * Event fired when a form is submitted\n *\n * @publicApi\n */\nclass FormSubmittedEvent extends ControlEvent {\n  source;\n  constructor(source) {\n    super();\n    this.source = source;\n  }\n}\n/**\n * Event fired when a form is reset.\n *\n * @publicApi\n */\nclass FormResetEvent extends ControlEvent {\n  source;\n  constructor(source) {\n    super();\n    this.source = source;\n  }\n}\n/**\n * Gets validators from either an options object or given validators.\n */\nfunction pickValidators(validatorOrOpts) {\n  return (isOptionsObj(validatorOrOpts) ? validatorOrOpts.validators : validatorOrOpts) || null;\n}\n/**\n * Creates validator function by combining provided validators.\n */\nfunction coerceToValidator(validator) {\n  return Array.isArray(validator) ? composeValidators(validator) : validator || null;\n}\n/**\n * Gets async validators from either an options object or given validators.\n */\nfunction pickAsyncValidators(asyncValidator, validatorOrOpts) {\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    if (isOptionsObj(validatorOrOpts) && asyncValidator) {\n      console.warn(asyncValidatorsDroppedWithOptsWarning);\n    }\n  }\n  return (isOptionsObj(validatorOrOpts) ? validatorOrOpts.asyncValidators : asyncValidator) || null;\n}\n/**\n * Creates async validator function by combining provided async validators.\n */\nfunction coerceToAsyncValidator(asyncValidator) {\n  return Array.isArray(asyncValidator) ? composeAsyncValidators(asyncValidator) : asyncValidator || null;\n}\nfunction isOptionsObj(validatorOrOpts) {\n  return validatorOrOpts != null && !Array.isArray(validatorOrOpts) && typeof validatorOrOpts === 'object';\n}\nfunction assertControlPresent(parent, isGroup, key) {\n  const controls = parent.controls;\n  const collection = isGroup ? Object.keys(controls) : controls;\n  if (!collection.length) {\n    throw new _RuntimeError(1000 /* RuntimeErrorCode.NO_CONTROLS */, typeof ngDevMode === 'undefined' || ngDevMode ? noControlsError(isGroup) : '');\n  }\n  if (!controls[key]) {\n    throw new _RuntimeError(1001 /* RuntimeErrorCode.MISSING_CONTROL */, typeof ngDevMode === 'undefined' || ngDevMode ? missingControlError(isGroup, key) : '');\n  }\n}\nfunction assertAllValuesPresent(control, isGroup, value) {\n  control._forEachChild((_, key) => {\n    if (value[key] === undefined) {\n      throw new _RuntimeError(1002 /* RuntimeErrorCode.MISSING_CONTROL_VALUE */, typeof ngDevMode === 'undefined' || ngDevMode ? missingControlValueError(isGroup, key) : '');\n    }\n  });\n}\n/**\n * This is the base class for `FormControl`, `FormGroup`, and `FormArray`.\n *\n * It provides some of the shared behavior that all controls and groups of controls have, like\n * running validators, calculating status, and resetting state. It also defines the properties\n * that are shared between all sub-classes, like `value`, `valid`, and `dirty`. It shouldn't be\n * instantiated directly.\n *\n * The first type parameter TValue represents the value type of the control (`control.value`).\n * The optional type parameter TRawValue  represents the raw value type (`control.getRawValue()`).\n *\n * @see [Forms Guide](guide/forms)\n * @see [Reactive Forms Guide](guide/forms/reactive-forms)\n * @see [Dynamic Forms Guide](guide/forms/dynamic-forms)\n *\n * @publicApi\n */\nclass AbstractControl {\n  /** @internal */\n  _pendingDirty = false;\n  /**\n   * Indicates that a control has its own pending asynchronous validation in progress.\n   * It also stores if the control should emit events when the validation status changes.\n   *\n   * @internal\n   */\n  _hasOwnPendingAsyncValidator = null;\n  /** @internal */\n  _pendingTouched = false;\n  /** @internal */\n  _onCollectionChange = () => {};\n  /** @internal */\n  _updateOn;\n  _parent = null;\n  _asyncValidationSubscription;\n  /**\n   * Contains the result of merging synchronous validators into a single validator function\n   * (combined using `Validators.compose`).\n   *\n   * @internal\n   */\n  _composedValidatorFn;\n  /**\n   * Contains the result of merging asynchronous validators into a single validator function\n   * (combined using `Validators.composeAsync`).\n   *\n   * @internal\n   */\n  _composedAsyncValidatorFn;\n  /**\n   * Synchronous validators as they were provided:\n   *  - in `AbstractControl` constructor\n   *  - as an argument while calling `setValidators` function\n   *  - while calling the setter on the `validator` field (e.g. `control.validator = validatorFn`)\n   *\n   * @internal\n   */\n  _rawValidators;\n  /**\n   * Asynchronous validators as they were provided:\n   *  - in `AbstractControl` constructor\n   *  - as an argument while calling `setAsyncValidators` function\n   *  - while calling the setter on the `asyncValidator` field (e.g. `control.asyncValidator =\n   * asyncValidatorFn`)\n   *\n   * @internal\n   */\n  _rawAsyncValidators;\n  /**\n   * The current value of the control.\n   *\n   * * For a `FormControl`, the current value.\n   * * For an enabled `FormGroup`, the values of enabled controls as an object\n   * with a key-value pair for each member of the group.\n   * * For a disabled `FormGroup`, the values of all controls as an object\n   * with a key-value pair for each member of the group.\n   * * For a `FormArray`, the values of enabled controls as an array.\n   *\n   */\n  value;\n  /**\n   * Initialize the AbstractControl instance.\n   *\n   * @param validators The function or array of functions that is used to determine the validity of\n   *     this control synchronously.\n   * @param asyncValidators The function or array of functions that is used to determine validity of\n   *     this control asynchronously.\n   */\n  constructor(validators, asyncValidators) {\n    this._assignValidators(validators);\n    this._assignAsyncValidators(asyncValidators);\n  }\n  /**\n   * Returns the function that is used to determine the validity of this control synchronously.\n   * If multiple validators have been added, this will be a single composed function.\n   * See `Validators.compose()` for additional information.\n   */\n  get validator() {\n    return this._composedValidatorFn;\n  }\n  set validator(validatorFn) {\n    this._rawValidators = this._composedValidatorFn = validatorFn;\n  }\n  /**\n   * Returns the function that is used to determine the validity of this control asynchronously.\n   * If multiple validators have been added, this will be a single composed function.\n   * See `Validators.compose()` for additional information.\n   */\n  get asyncValidator() {\n    return this._composedAsyncValidatorFn;\n  }\n  set asyncValidator(asyncValidatorFn) {\n    this._rawAsyncValidators = this._composedAsyncValidatorFn = asyncValidatorFn;\n  }\n  /**\n   * The parent control.\n   */\n  get parent() {\n    return this._parent;\n  }\n  /**\n   * The validation status of the control.\n   *\n   * @see {@link FormControlStatus}\n   *\n   * These status values are mutually exclusive, so a control cannot be\n   * both valid AND invalid or invalid AND disabled.\n   */\n  get status() {\n    return untracked(this.statusReactive);\n  }\n  set status(v) {\n    untracked(() => this.statusReactive.set(v));\n  }\n  /** @internal */\n  _status = computed(() => this.statusReactive(), ...(ngDevMode ? [{\n    debugName: \"_status\"\n  }] : []));\n  statusReactive = signal(undefined, ...(ngDevMode ? [{\n    debugName: \"statusReactive\"\n  }] : []));\n  /**\n   * A control is `valid` when its `status` is `VALID`.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if the control has passed all of its validation tests,\n   * false otherwise.\n   */\n  get valid() {\n    return this.status === VALID;\n  }\n  /**\n   * A control is `invalid` when its `status` is `INVALID`.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if this control has failed one or more of its validation checks,\n   * false otherwise.\n   */\n  get invalid() {\n    return this.status === INVALID;\n  }\n  /**\n   * A control is `pending` when its `status` is `PENDING`.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if this control is in the process of conducting a validation check,\n   * false otherwise.\n   */\n  get pending() {\n    return this.status == PENDING;\n  }\n  /**\n   * A control is `disabled` when its `status` is `DISABLED`.\n   *\n   * Disabled controls are exempt from validation checks and\n   * are not included in the aggregate value of their ancestor\n   * controls.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @returns True if the control is disabled, false otherwise.\n   */\n  get disabled() {\n    return this.status === DISABLED;\n  }\n  /**\n   * A control is `enabled` as long as its `status` is not `DISABLED`.\n   *\n   * @returns True if the control has any status other than 'DISABLED',\n   * false if the status is 'DISABLED'.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   */\n  get enabled() {\n    return this.status !== DISABLED;\n  }\n  /**\n   * An object containing any errors generated by failing validation,\n   * or null if there are no errors.\n   */\n  errors;\n  /**\n   * A control is `pristine` if the user has not yet changed\n   * the value in the UI.\n   *\n   * @returns True if the user has not yet changed the value in the UI; compare `dirty`.\n   * Programmatic changes to a control's value do not mark it dirty.\n   */\n  get pristine() {\n    return untracked(this.pristineReactive);\n  }\n  set pristine(v) {\n    untracked(() => this.pristineReactive.set(v));\n  }\n  /** @internal */\n  _pristine = computed(() => this.pristineReactive(), ...(ngDevMode ? [{\n    debugName: \"_pristine\"\n  }] : []));\n  pristineReactive = signal(true, ...(ngDevMode ? [{\n    debugName: \"pristineReactive\"\n  }] : []));\n  /**\n   * A control is `dirty` if the user has changed the value\n   * in the UI.\n   *\n   * @returns True if the user has changed the value of this control in the UI; compare `pristine`.\n   * Programmatic changes to a control's value do not mark it dirty.\n   */\n  get dirty() {\n    return !this.pristine;\n  }\n  /**\n   * True if the control is marked as `touched`.\n   *\n   * A control is marked `touched` once the user has triggered\n   * a `blur` event on it.\n   */\n  get touched() {\n    return untracked(this.touchedReactive);\n  }\n  set touched(v) {\n    untracked(() => this.touchedReactive.set(v));\n  }\n  /** @internal */\n  _touched = computed(() => this.touchedReactive(), ...(ngDevMode ? [{\n    debugName: \"_touched\"\n  }] : []));\n  touchedReactive = signal(false, ...(ngDevMode ? [{\n    debugName: \"touchedReactive\"\n  }] : []));\n  /**\n   * True if the control has not been marked as touched\n   *\n   * A control is `untouched` if the user has not yet triggered\n   * a `blur` event on it.\n   */\n  get untouched() {\n    return !this.touched;\n  }\n  /**\n   * Exposed as observable, see below.\n   *\n   * @internal\n   */\n  _events = new Subject();\n  /**\n   * A multicasting observable that emits an event every time the state of the control changes.\n   * It emits for value, status, pristine or touched changes.\n   *\n   * **Note**: On value change, the emit happens right after a value of this control is updated. The\n   * value of a parent control (for example if this FormControl is a part of a FormGroup) is updated\n   * later, so accessing a value of a parent control (using the `value` property) from the callback\n   * of this event might result in getting a value that has not been updated yet. Subscribe to the\n   * `events` of the parent control instead.\n   * For other event types, the events are emitted after the parent control has been updated.\n   *\n   */\n  events = this._events.asObservable();\n  /**\n   * A multicasting observable that emits an event every time the value of the control changes, in\n   * the UI or programmatically. It also emits an event each time you call enable() or disable()\n   * without passing along {emitEvent: false} as a function argument.\n   *\n   * **Note**: the emit happens right after a value of this control is updated. The value of a\n   * parent control (for example if this FormControl is a part of a FormGroup) is updated later, so\n   * accessing a value of a parent control (using the `value` property) from the callback of this\n   * event might result in getting a value that has not been updated yet. Subscribe to the\n   * `valueChanges` event of the parent control instead.\n   */\n  valueChanges;\n  /**\n   * A multicasting observable that emits an event every time the validation `status` of the control\n   * recalculates.\n   *\n   * @see {@link FormControlStatus}\n   * @see {@link AbstractControl.status}\n   */\n  statusChanges;\n  /**\n   * Reports the update strategy of the `AbstractControl` (meaning\n   * the event on which the control updates itself).\n   * Possible values: `'change'` | `'blur'` | `'submit'`\n   * Default value: `'change'`\n   */\n  get updateOn() {\n    return this._updateOn ? this._updateOn : this.parent ? this.parent.updateOn : 'change';\n  }\n  /**\n   * Sets the synchronous validators that are active on this control.  Calling\n   * this overwrites any existing synchronous validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * If you want to add a new validator without affecting existing ones, consider\n   * using `addValidators()` method instead.\n   */\n  setValidators(validators) {\n    this._assignValidators(validators);\n  }\n  /**\n   * Sets the asynchronous validators that are active on this control. Calling this\n   * overwrites any existing asynchronous validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * If you want to add a new validator without affecting existing ones, consider\n   * using `addAsyncValidators()` method instead.\n   */\n  setAsyncValidators(validators) {\n    this._assignAsyncValidators(validators);\n  }\n  /**\n   * Add a synchronous validator or validators to this control, without affecting other validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * Adding a validator that already exists will have no effect. If duplicate validator functions\n   * are present in the `validators` array, only the first instance would be added to a form\n   * control.\n   *\n   * @param validators The new validator function or functions to add to this control.\n   */\n  addValidators(validators) {\n    this.setValidators(addValidators(validators, this._rawValidators));\n  }\n  /**\n   * Add an asynchronous validator or validators to this control, without affecting other\n   * validators.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * Adding a validator that already exists will have no effect.\n   *\n   * @param validators The new asynchronous validator function or functions to add to this control.\n   */\n  addAsyncValidators(validators) {\n    this.setAsyncValidators(addValidators(validators, this._rawAsyncValidators));\n  }\n  /**\n   * Remove a synchronous validator from this control, without affecting other validators.\n   * Validators are compared by function reference; you must pass a reference to the exact same\n   * validator function as the one that was originally set. If a provided validator is not found,\n   * it is ignored.\n   *\n   * @usageNotes\n   *\n   * ### Reference to a ValidatorFn\n   *\n   * ```\n   * // Reference to the RequiredValidator\n   * const ctrl = new FormControl<string | null>('', Validators.required);\n   * ctrl.removeValidators(Validators.required);\n   *\n   * // Reference to anonymous function inside MinValidator\n   * const minValidator = Validators.min(3);\n   * const ctrl = new FormControl<string | null>('', minValidator);\n   * expect(ctrl.hasValidator(minValidator)).toEqual(true)\n   * expect(ctrl.hasValidator(Validators.min(3))).toEqual(false)\n   *\n   * ctrl.removeValidators(minValidator);\n   * ```\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * @param validators The validator or validators to remove.\n   */\n  removeValidators(validators) {\n    this.setValidators(removeValidators(validators, this._rawValidators));\n  }\n  /**\n   * Remove an asynchronous validator from this control, without affecting other validators.\n   * Validators are compared by function reference; you must pass a reference to the exact same\n   * validator function as the one that was originally set. If a provided validator is not found, it\n   * is ignored.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   * @param validators The asynchronous validator or validators to remove.\n   */\n  removeAsyncValidators(validators) {\n    this.setAsyncValidators(removeValidators(validators, this._rawAsyncValidators));\n  }\n  /**\n   * Check whether a synchronous validator function is present on this control. The provided\n   * validator must be a reference to the exact same function that was provided.\n   *\n   * @usageNotes\n   *\n   * ### Reference to a ValidatorFn\n   *\n   * ```\n   * // Reference to the RequiredValidator\n   * const ctrl = new FormControl<number | null>(0, Validators.required);\n   * expect(ctrl.hasValidator(Validators.required)).toEqual(true)\n   *\n   * // Reference to anonymous function inside MinValidator\n   * const minValidator = Validators.min(3);\n   * const ctrl = new FormControl<number | null>(0, minValidator);\n   * expect(ctrl.hasValidator(minValidator)).toEqual(true)\n   * expect(ctrl.hasValidator(Validators.min(3))).toEqual(false)\n   * ```\n   *\n   * @param validator The validator to check for presence. Compared by function reference.\n   * @returns Whether the provided validator was found on this control.\n   */\n  hasValidator(validator) {\n    return hasValidator(this._rawValidators, validator);\n  }\n  /**\n   * Check whether an asynchronous validator function is present on this control. The provided\n   * validator must be a reference to the exact same function that was provided.\n   *\n   * @param validator The asynchronous validator to check for presence. Compared by function\n   *     reference.\n   * @returns Whether the provided asynchronous validator was found on this control.\n   */\n  hasAsyncValidator(validator) {\n    return hasValidator(this._rawAsyncValidators, validator);\n  }\n  /**\n   * Empties out the synchronous validator list.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   */\n  clearValidators() {\n    this.validator = null;\n  }\n  /**\n   * Empties out the async validator list.\n   *\n   * When you add or remove a validator at run time, you must call\n   * `updateValueAndValidity()` for the new validation to take effect.\n   *\n   */\n  clearAsyncValidators() {\n    this.asyncValidator = null;\n  }\n  markAsTouched(opts = {}) {\n    const changed = this.touched === false;\n    this.touched = true;\n    const sourceControl = opts.sourceControl ?? this;\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsTouched({\n        ...opts,\n        sourceControl\n      });\n    }\n    if (changed && opts.emitEvent !== false) {\n      this._events.next(new TouchedChangeEvent(true, sourceControl));\n    }\n  }\n  /**\n   * Marks the control and all its descendant controls as `dirty`.\n   * @see {@link markAsDirty()}\n   *\n   * @param opts Configuration options that determine how the control propagates changes\n   * and emits events after marking is applied.\n   * * `emitEvent`: When true or not supplied (the default), the `events`\n   * observable emits a `PristineChangeEvent` with the `pristine` property being `false`.\n   * When false, no events are emitted.\n   */\n  markAllAsDirty(opts = {}) {\n    this.markAsDirty({\n      onlySelf: true,\n      emitEvent: opts.emitEvent,\n      sourceControl: this\n    });\n    this._forEachChild(control => control.markAllAsDirty(opts));\n  }\n  /**\n   * Marks the control and all its descendant controls as `touched`.\n   * @see {@link markAsTouched()}\n   *\n   * @param opts Configuration options that determine how the control propagates changes\n   * and emits events after marking is applied.\n   * * `emitEvent`: When true or not supplied (the default), the `events`\n   * observable emits a `TouchedChangeEvent` with the `touched` property being `true`.\n   * When false, no events are emitted.\n   */\n  markAllAsTouched(opts = {}) {\n    this.markAsTouched({\n      onlySelf: true,\n      emitEvent: opts.emitEvent,\n      sourceControl: this\n    });\n    this._forEachChild(control => control.markAllAsTouched(opts));\n  }\n  markAsUntouched(opts = {}) {\n    const changed = this.touched === true;\n    this.touched = false;\n    this._pendingTouched = false;\n    const sourceControl = opts.sourceControl ?? this;\n    this._forEachChild(control => {\n      control.markAsUntouched({\n        onlySelf: true,\n        emitEvent: opts.emitEvent,\n        sourceControl\n      });\n    });\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updateTouched(opts, sourceControl);\n    }\n    if (changed && opts.emitEvent !== false) {\n      this._events.next(new TouchedChangeEvent(false, sourceControl));\n    }\n  }\n  markAsDirty(opts = {}) {\n    const changed = this.pristine === true;\n    this.pristine = false;\n    const sourceControl = opts.sourceControl ?? this;\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsDirty({\n        ...opts,\n        sourceControl\n      });\n    }\n    if (changed && opts.emitEvent !== false) {\n      this._events.next(new PristineChangeEvent(false, sourceControl));\n    }\n  }\n  markAsPristine(opts = {}) {\n    const changed = this.pristine === false;\n    this.pristine = true;\n    this._pendingDirty = false;\n    const sourceControl = opts.sourceControl ?? this;\n    this._forEachChild(control => {\n      /** We don't propagate the source control downwards */\n      control.markAsPristine({\n        onlySelf: true,\n        emitEvent: opts.emitEvent\n      });\n    });\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updatePristine(opts, sourceControl);\n    }\n    if (changed && opts.emitEvent !== false) {\n      this._events.next(new PristineChangeEvent(true, sourceControl));\n    }\n  }\n  markAsPending(opts = {}) {\n    this.status = PENDING;\n    const sourceControl = opts.sourceControl ?? this;\n    if (opts.emitEvent !== false) {\n      this._events.next(new StatusChangeEvent(this.status, sourceControl));\n      this.statusChanges.emit(this.status);\n    }\n    if (this._parent && !opts.onlySelf) {\n      this._parent.markAsPending({\n        ...opts,\n        sourceControl\n      });\n    }\n  }\n  disable(opts = {}) {\n    // If parent has been marked artificially dirty we don't want to re-calculate the\n    // parent's dirtiness based on the children.\n    const skipPristineCheck = this._parentMarkedDirty(opts.onlySelf);\n    this.status = DISABLED;\n    this.errors = null;\n    this._forEachChild(control => {\n      /** We don't propagate the source control downwards */\n      control.disable({\n        ...opts,\n        onlySelf: true\n      });\n    });\n    this._updateValue();\n    const sourceControl = opts.sourceControl ?? this;\n    if (opts.emitEvent !== false) {\n      this._events.next(new ValueChangeEvent(this.value, sourceControl));\n      this._events.next(new StatusChangeEvent(this.status, sourceControl));\n      this.valueChanges.emit(this.value);\n      this.statusChanges.emit(this.status);\n    }\n    this._updateAncestors({\n      ...opts,\n      skipPristineCheck\n    }, this);\n    this._onDisabledChange.forEach(changeFn => changeFn(true));\n  }\n  /**\n   * Enables the control. This means the control is included in validation checks and\n   * the aggregate value of its parent. Its status recalculates based on its value and\n   * its validators.\n   *\n   * By default, if the control has children, all children are enabled.\n   *\n   * @see {@link AbstractControl.status}\n   *\n   * @param opts Configure options that control how the control propagates changes and\n   * emits events when marked as untouched\n   * * `onlySelf`: When true, mark only this control. When false or not supplied,\n   * marks all direct ancestors. Default is false.\n   * * `emitEvent`: When true or not supplied (the default), the `statusChanges`,\n   * `valueChanges` and `events`\n   * observables emit events with the latest status and value when the control is enabled.\n   * When false, no events are emitted.\n   */\n  enable(opts = {}) {\n    // If parent has been marked artificially dirty we don't want to re-calculate the\n    // parent's dirtiness based on the children.\n    const skipPristineCheck = this._parentMarkedDirty(opts.onlySelf);\n    this.status = VALID;\n    this._forEachChild(control => {\n      control.enable({\n        ...opts,\n        onlySelf: true\n      });\n    });\n    this.updateValueAndValidity({\n      onlySelf: true,\n      emitEvent: opts.emitEvent\n    });\n    this._updateAncestors({\n      ...opts,\n      skipPristineCheck\n    }, this);\n    this._onDisabledChange.forEach(changeFn => changeFn(false));\n  }\n  _updateAncestors(opts, sourceControl) {\n    if (this._parent && !opts.onlySelf) {\n      this._parent.updateValueAndValidity(opts);\n      if (!opts.skipPristineCheck) {\n        this._parent._updatePristine({}, sourceControl);\n      }\n      this._parent._updateTouched({}, sourceControl);\n    }\n  }\n  /**\n   * Sets the parent of the control\n   *\n   * @param parent The new parent.\n   */\n  setParent(parent) {\n    this._parent = parent;\n  }\n  /**\n   * The raw value of this control. For most control implementations, the raw value will include\n   * disabled children.\n   */\n  getRawValue() {\n    return this.value;\n  }\n  updateValueAndValidity(opts = {}) {\n    this._setInitialStatus();\n    this._updateValue();\n    if (this.enabled) {\n      const shouldHaveEmitted = this._cancelExistingSubscription();\n      this.errors = this._runValidator();\n      this.status = this._calculateStatus();\n      if (this.status === VALID || this.status === PENDING) {\n        // If the canceled subscription should have emitted\n        // we make sure the async validator emits the status change on completion\n        this._runAsyncValidator(shouldHaveEmitted, opts.emitEvent);\n      }\n    }\n    const sourceControl = opts.sourceControl ?? this;\n    if (opts.emitEvent !== false) {\n      this._events.next(new ValueChangeEvent(this.value, sourceControl));\n      this._events.next(new StatusChangeEvent(this.status, sourceControl));\n      this.valueChanges.emit(this.value);\n      this.statusChanges.emit(this.status);\n    }\n    if (this._parent && !opts.onlySelf) {\n      this._parent.updateValueAndValidity({\n        ...opts,\n        sourceControl\n      });\n    }\n  }\n  /** @internal */\n  _updateTreeValidity(opts = {\n    emitEvent: true\n  }) {\n    this._forEachChild(ctrl => ctrl._updateTreeValidity(opts));\n    this.updateValueAndValidity({\n      onlySelf: true,\n      emitEvent: opts.emitEvent\n    });\n  }\n  _setInitialStatus() {\n    this.status = this._allControlsDisabled() ? DISABLED : VALID;\n  }\n  _runValidator() {\n    return this.validator ? this.validator(this) : null;\n  }\n  _runAsyncValidator(shouldHaveEmitted, emitEvent) {\n    if (this.asyncValidator) {\n      this.status = PENDING;\n      this._hasOwnPendingAsyncValidator = {\n        emitEvent: emitEvent !== false,\n        shouldHaveEmitted: shouldHaveEmitted !== false\n      };\n      const obs = toObservable(this.asyncValidator(this));\n      this._asyncValidationSubscription = obs.subscribe(errors => {\n        this._hasOwnPendingAsyncValidator = null;\n        // This will trigger the recalculation of the validation status, which depends on\n        // the state of the asynchronous validation (whether it is in progress or not). So, it is\n        // necessary that we have updated the `_hasOwnPendingAsyncValidator` boolean flag first.\n        this.setErrors(errors, {\n          emitEvent,\n          shouldHaveEmitted\n        });\n      });\n    }\n  }\n  _cancelExistingSubscription() {\n    if (this._asyncValidationSubscription) {\n      this._asyncValidationSubscription.unsubscribe();\n      // we're cancelling the validator subscribtion, we keep if it should have emitted\n      // because we want to emit eventually if it was required at least once.\n      const shouldHaveEmitted = (this._hasOwnPendingAsyncValidator?.emitEvent || this._hasOwnPendingAsyncValidator?.shouldHaveEmitted) ?? false;\n      this._hasOwnPendingAsyncValidator = null;\n      return shouldHaveEmitted;\n    }\n    return false;\n  }\n  setErrors(errors, opts = {}) {\n    this.errors = errors;\n    this._updateControlsErrors(opts.emitEvent !== false, this, opts.shouldHaveEmitted);\n  }\n  /**\n   * Retrieves a child control given the control's name or path.\n   *\n   * @param path A dot-delimited string or array of string/number values that define the path to the\n   * control. If a string is provided, passing it as a string literal will result in improved type\n   * information. Likewise, if an array is provided, passing it `as const` will cause improved type\n   * information to be available.\n   *\n   * @usageNotes\n   * ### Retrieve a nested control\n   *\n   * For example, to get a `name` control nested within a `person` sub-group:\n   *\n   * * `this.form.get('person.name');`\n   *\n   * -OR-\n   *\n   * * `this.form.get(['person', 'name'] as const);` // `as const` gives improved typings\n   *\n   * ### Retrieve a control in a FormArray\n   *\n   * When accessing an element inside a FormArray, you can use an element index.\n   * For example, to get a `price` control from the first element in an `items` array you can use:\n   *\n   * * `this.form.get('items.0.price');`\n   *\n   * -OR-\n   *\n   * * `this.form.get(['items', 0, 'price']);`\n   */\n  get(path) {\n    let currPath = path;\n    if (currPath == null) return null;\n    if (!Array.isArray(currPath)) currPath = currPath.split('.');\n    if (currPath.length === 0) return null;\n    return currPath.reduce((control, name) => control && control._find(name), this);\n  }\n  /**\n   * @description\n   * Reports error data for the control with the given path.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```ts\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * @returns error data for that particular error. If the control or error is not present,\n   * null is returned.\n   */\n  getError(errorCode, path) {\n    const control = path ? this.get(path) : this;\n    return control && control.errors ? control.errors[errorCode] : null;\n  }\n  /**\n   * @description\n   * Reports whether the control with the given path has the error specified.\n   *\n   * @param errorCode The code of the error to check\n   * @param path A list of control names that designates how to move from the current control\n   * to the control that should be queried for errors.\n   *\n   * @usageNotes\n   * For example, for the following `FormGroup`:\n   *\n   * ```ts\n   * form = new FormGroup({\n   *   address: new FormGroup({ street: new FormControl() })\n   * });\n   * ```\n   *\n   * The path to the 'street' control from the root form would be 'address' -> 'street'.\n   *\n   * It can be provided to this method in one of two formats:\n   *\n   * 1. An array of string control names, e.g. `['address', 'street']`\n   * 1. A period-delimited list of control names in one string, e.g. `'address.street'`\n   *\n   * If no path is given, this method checks for the error on the current control.\n   *\n   * @returns whether the given error is present in the control at the given path.\n   *\n   * If the control is not present, false is returned.\n   */\n  hasError(errorCode, path) {\n    return !!this.getError(errorCode, path);\n  }\n  /**\n   * Retrieves the top-level ancestor of this control.\n   */\n  get root() {\n    let x = this;\n    while (x._parent) {\n      x = x._parent;\n    }\n    return x;\n  }\n  /** @internal */\n  _updateControlsErrors(emitEvent, changedControl, shouldHaveEmitted) {\n    this.status = this._calculateStatus();\n    if (emitEvent) {\n      this.statusChanges.emit(this.status);\n    }\n    // The Events Observable expose a slight different bevahior than the statusChanges obs\n    // An async validator will still emit a StatusChangeEvent is a previously cancelled\n    // async validator has emitEvent set to true\n    if (emitEvent || shouldHaveEmitted) {\n      this._events.next(new StatusChangeEvent(this.status, changedControl));\n    }\n    if (this._parent) {\n      this._parent._updateControlsErrors(emitEvent, changedControl, shouldHaveEmitted);\n    }\n  }\n  /** @internal */\n  _initObservables() {\n    // TODO: this should be piped from events() but is breaking in G3\n    this.valueChanges = new EventEmitter();\n    this.statusChanges = new EventEmitter();\n  }\n  _calculateStatus() {\n    if (this._allControlsDisabled()) return DISABLED;\n    if (this.errors) return INVALID;\n    if (this._hasOwnPendingAsyncValidator || this._anyControlsHaveStatus(PENDING)) return PENDING;\n    if (this._anyControlsHaveStatus(INVALID)) return INVALID;\n    return VALID;\n  }\n  /** @internal */\n  _anyControlsHaveStatus(status) {\n    return this._anyControls(control => control.status === status);\n  }\n  /** @internal */\n  _anyControlsDirty() {\n    return this._anyControls(control => control.dirty);\n  }\n  /** @internal */\n  _anyControlsTouched() {\n    return this._anyControls(control => control.touched);\n  }\n  /** @internal */\n  _updatePristine(opts, changedControl) {\n    const newPristine = !this._anyControlsDirty();\n    const changed = this.pristine !== newPristine;\n    this.pristine = newPristine;\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updatePristine(opts, changedControl);\n    }\n    if (changed) {\n      this._events.next(new PristineChangeEvent(this.pristine, changedControl));\n    }\n  }\n  /** @internal */\n  _updateTouched(opts = {}, changedControl) {\n    this.touched = this._anyControlsTouched();\n    this._events.next(new TouchedChangeEvent(this.touched, changedControl));\n    if (this._parent && !opts.onlySelf) {\n      this._parent._updateTouched(opts, changedControl);\n    }\n  }\n  /** @internal */\n  _onDisabledChange = [];\n  /** @internal */\n  _registerOnCollectionChange(fn) {\n    this._onCollectionChange = fn;\n  }\n  /** @internal */\n  _setUpdateStrategy(opts) {\n    if (isOptionsObj(opts) && opts.updateOn != null) {\n      this._updateOn = opts.updateOn;\n    }\n  }\n  /**\n   * Check to see if parent has been marked artificially dirty.\n   *\n   * @internal\n   */\n  _parentMarkedDirty(onlySelf) {\n    const parentDirty = this._parent && this._parent.dirty;\n    return !onlySelf && !!parentDirty && !this._parent._anyControlsDirty();\n  }\n  /** @internal */\n  _find(name) {\n    return null;\n  }\n  /**\n   * Internal implementation of the `setValidators` method. Needs to be separated out into a\n   * different method, because it is called in the constructor and it can break cases where\n   * a control is extended.\n   */\n  _assignValidators(validators) {\n    this._rawValidators = Array.isArray(validators) ? validators.slice() : validators;\n    this._composedValidatorFn = coerceToValidator(this._rawValidators);\n  }\n  /**\n   * Internal implementation of the `setAsyncValidators` method. Needs to be separated out into a\n   * different method, because it is called in the constructor and it can break cases where\n   * a control is extended.\n   */\n  _assignAsyncValidators(validators) {\n    this._rawAsyncValidators = Array.isArray(validators) ? validators.slice() : validators;\n    this._composedAsyncValidatorFn = coerceToAsyncValidator(this._rawAsyncValidators);\n  }\n}\n\n/**\n * Tracks the value and validity state of a group of `FormControl` instances.\n *\n * A `FormGroup` aggregates the values of each child `FormControl` into one object,\n * with each control name as the key.  It calculates its status by reducing the status values\n * of its children. For example, if one of the controls in a group is invalid, the entire\n * group becomes invalid.\n *\n * `FormGroup` is one of the four fundamental building blocks used to define forms in Angular,\n * along with `FormControl`, `FormArray`, and `FormRecord`.\n *\n * When instantiating a `FormGroup`, pass in a collection of child controls as the first\n * argument. The key for each child registers the name for the control.\n *\n * `FormGroup` is intended for use cases where the keys are known ahead of time.\n * If you need to dynamically add and remove controls, use {@link FormRecord} instead.\n *\n * `FormGroup` accepts an optional type parameter `TControl`, which is an object type with inner\n * control types as values.\n *\n * @usageNotes\n *\n * ### Create a form group with 2 controls\n *\n * ```ts\n * const form = new FormGroup({\n *   first: new FormControl('Nancy', Validators.minLength(2)),\n *   last: new FormControl('Drew'),\n * });\n *\n * console.log(form.value);   // {first: 'Nancy', last; 'Drew'}\n * console.log(form.status);  // 'VALID'\n * ```\n *\n * ### The type argument, and optional controls\n *\n * `FormGroup` accepts one generic argument, which is an object containing its inner controls.\n * This type will usually be inferred automatically, but you can always specify it explicitly if you\n * wish.\n *\n * If you have controls that are optional (i.e. they can be removed, you can use the `?` in the\n * type):\n *\n * ```ts\n * const form = new FormGroup<{\n *   first: FormControl<string|null>,\n *   middle?: FormControl<string|null>, // Middle name is optional.\n *   last: FormControl<string|null>,\n * }>({\n *   first: new FormControl('Nancy'),\n *   last: new FormControl('Drew'),\n * });\n * ```\n *\n * ### Create a form group with a group-level validator\n *\n * You include group-level validators as the second arg, or group-level async\n * validators as the third arg. These come in handy when you want to perform validation\n * that considers the value of more than one child control.\n *\n * ```ts\n * const form = new FormGroup({\n *   password: new FormControl('', Validators.minLength(2)),\n *   passwordConfirm: new FormControl('', Validators.minLength(2)),\n * }, passwordMatchValidator);\n *\n *\n * function passwordMatchValidator(g: FormGroup) {\n *    return g.get('password').value === g.get('passwordConfirm').value\n *       ? null : {'mismatch': true};\n * }\n * ```\n *\n * Like `FormControl` instances, you choose to pass in\n * validators and async validators as part of an options object.\n *\n * ```ts\n * const form = new FormGroup({\n *   password: new FormControl('')\n *   passwordConfirm: new FormControl('')\n * }, { validators: passwordMatchValidator, asyncValidators: otherValidator });\n * ```\n *\n * ### Set the updateOn property for all controls in a form group\n *\n * The options object is used to set a default value for each child\n * control's `updateOn` property. If you set `updateOn` to `'blur'` at the\n * group level, all child controls default to 'blur', unless the child\n * has explicitly specified a different `updateOn` value.\n *\n * ```ts\n * const c = new FormGroup({\n *   one: new FormControl()\n * }, { updateOn: 'blur' });\n * ```\n *\n * ### Using a FormGroup with optional controls\n *\n * It is possible to have optional controls in a FormGroup. An optional control can be removed later\n * using `removeControl`, and can be omitted when calling `reset`. Optional controls must be\n * declared optional in the group's type.\n *\n * ```ts\n * const c = new FormGroup<{one?: FormControl<string>}>({\n *   one: new FormControl('')\n * });\n * ```\n *\n * Notice that `c.value.one` has type `string|null|undefined`. This is because calling `c.reset({})`\n * without providing the optional key `one` will cause it to become `null`.\n *\n * @publicApi\n */\nclass FormGroup extends AbstractControl {\n  /**\n   * Creates a new `FormGroup` instance.\n   *\n   * @param controls A collection of child controls. The key for each child is the name\n   * under which it is registered.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of\n   * such functions, or an `AbstractControlOptions` object that contains validation functions\n   * and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator functions\n   *\n   */\n  constructor(controls, validatorOrOpts, asyncValidator) {\n    super(pickValidators(validatorOrOpts), pickAsyncValidators(asyncValidator, validatorOrOpts));\n    (typeof ngDevMode === 'undefined' || ngDevMode) && validateFormGroupControls(controls);\n    this.controls = controls;\n    this._initObservables();\n    this._setUpdateStrategy(validatorOrOpts);\n    this._setUpControls();\n    this.updateValueAndValidity({\n      onlySelf: true,\n      // If `asyncValidator` is present, it will trigger control status change from `PENDING` to\n      // `VALID` or `INVALID`. The status should be broadcasted via the `statusChanges` observable,\n      // so we set `emitEvent` to `true` to allow that during the control creation process.\n      emitEvent: !!this.asyncValidator\n    });\n  }\n  controls;\n  registerControl(name, control) {\n    if (this.controls[name]) return this.controls[name];\n    this.controls[name] = control;\n    control.setParent(this);\n    control._registerOnCollectionChange(this._onCollectionChange);\n    return control;\n  }\n  addControl(name, control, options = {}) {\n    this.registerControl(name, control);\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n    this._onCollectionChange();\n  }\n  /**\n   * Remove a control from this group. In a strongly-typed group, required controls cannot be\n   * removed.\n   *\n   * This method also updates the value and validity of the control.\n   *\n   * @param name The control name to remove from the collection\n   * @param options Specifies whether this FormGroup instance should emit events after a\n   *     control is removed.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * removed. When false, no events are emitted.\n   */\n  removeControl(name, options = {}) {\n    if (this.controls[name]) this.controls[name]._registerOnCollectionChange(() => {});\n    delete this.controls[name];\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n    this._onCollectionChange();\n  }\n  setControl(name, control, options = {}) {\n    if (this.controls[name]) this.controls[name]._registerOnCollectionChange(() => {});\n    delete this.controls[name];\n    if (control) this.registerControl(name, control);\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n    this._onCollectionChange();\n  }\n  contains(controlName) {\n    return this.controls.hasOwnProperty(controlName) && this.controls[controlName].enabled;\n  }\n  /**\n   * Sets the value of the `FormGroup`. It accepts an object that matches\n   * the structure of the group, with control names as keys.\n   *\n   * @usageNotes\n   * ### Set the complete value for the form group\n   *\n   * ```ts\n   * const form = new FormGroup({\n   *   first: new FormControl(),\n   *   last: new FormControl()\n   * });\n   *\n   * console.log(form.value);   // {first: null, last: null}\n   *\n   * form.setValue({first: 'Nancy', last: 'Drew'});\n   * console.log(form.value);   // {first: 'Nancy', last: 'Drew'}\n   * ```\n   *\n   * @throws When strict checks fail, such as setting the value of a control\n   * that doesn't exist or if you exclude a value of a control that does exist.\n   *\n   * @param value The new value for the control that matches the structure of the group.\n   * @param options Configuration options that determine how the control propagates changes\n   * and emits events after the value changes.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default is\n   * false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control value is updated.\n   * When false, no events are emitted.\n   */\n  setValue(value, options = {}) {\n    assertAllValuesPresent(this, true, value);\n    Object.keys(value).forEach(name => {\n      assertControlPresent(this, true, name);\n      this.controls[name].setValue(value[name], {\n        onlySelf: true,\n        emitEvent: options.emitEvent\n      });\n    });\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * Patches the value of the `FormGroup`. It accepts an object with control\n   * names as keys, and does its best to match the values to the correct controls\n   * in the group.\n   *\n   * It accepts both super-sets and sub-sets of the group without throwing an error.\n   *\n   * @usageNotes\n   * ### Patch the value for a form group\n   *\n   * ```ts\n   * const form = new FormGroup({\n   *    first: new FormControl(),\n   *    last: new FormControl()\n   * });\n   * console.log(form.value);   // {first: null, last: null}\n   *\n   * form.patchValue({first: 'Nancy'});\n   * console.log(form.value);   // {first: 'Nancy', last: null}\n   * ```\n   *\n   * @param value The object that matches the structure of the group.\n   * @param options Configuration options that determine how the control propagates changes and\n   * emits events after the value is patched.\n   * * `onlySelf`: When true, each change only affects this control and not its parent. Default is\n   * true.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control value\n   * is updated. When false, no events are emitted. The configuration options are passed to\n   * the {@link AbstractControl#updateValueAndValidity updateValueAndValidity} method.\n   */\n  patchValue(value, options = {}) {\n    // Even though the `value` argument type doesn't allow `null` and `undefined` values, the\n    // `patchValue` can be called recursively and inner data structures might have these values, so\n    // we just ignore such cases when a field containing FormGroup instance receives `null` or\n    // `undefined` as a value.\n    if (value == null /* both `null` and `undefined` */) return;\n    Object.keys(value).forEach(name => {\n      // The compiler cannot see through the uninstantiated conditional type of `this.controls`, so\n      // `as any` is required.\n      const control = this.controls[name];\n      if (control) {\n        control.patchValue(/* Guaranteed to be present, due to the outer forEach. */value[name], {\n          onlySelf: true,\n          emitEvent: options.emitEvent\n        });\n      }\n    });\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * Resets the `FormGroup`, marks all descendants `pristine` and `untouched` and sets\n   * the value of all descendants to their default values, or null if no defaults were provided.\n   *\n   * You reset to a specific form state by passing in a map of states\n   * that matches the structure of your form, with control names as keys. The state\n   * is a standalone value or a form state object with both a value and a disabled\n   * status.\n   *\n   * @param value Resets the control with an initial value,\n   * or an object that defines the initial value and disabled state.\n   *\n   * @param options Configuration options that determine how the control propagates changes\n   * and emits events when the group is reset.\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default is\n   * false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is reset.\n   * When false, no events are emitted.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   *\n   * @usageNotes\n   *\n   * ### Reset the form group values\n   *\n   * ```ts\n   * const form = new FormGroup({\n   *   first: new FormControl('first name'),\n   *   last: new FormControl('last name')\n   * });\n   *\n   * console.log(form.value);  // {first: 'first name', last: 'last name'}\n   *\n   * form.reset({ first: 'name', last: 'last name' });\n   *\n   * console.log(form.value);  // {first: 'name', last: 'last name'}\n   * ```\n   *\n   * ### Reset the form group values and disabled status\n   *\n   * ```ts\n   * const form = new FormGroup({\n   *   first: new FormControl('first name'),\n   *   last: new FormControl('last name')\n   * });\n   *\n   * form.reset({\n   *   first: {value: 'name', disabled: true},\n   *   last: 'last'\n   * });\n   *\n   * console.log(form.value);  // {last: 'last'}\n   * console.log(form.get('first').status);  // 'DISABLED'\n   * ```\n   */\n  reset(value = {}, options = {}) {\n    this._forEachChild((control, name) => {\n      control.reset(value ? value[name] : null, {\n        onlySelf: true,\n        emitEvent: options.emitEvent\n      });\n    });\n    this._updatePristine(options, this);\n    this._updateTouched(options, this);\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * The aggregate value of the `FormGroup`, including any disabled controls.\n   *\n   * Retrieves all values regardless of disabled status.\n   */\n  getRawValue() {\n    return this._reduceChildren({}, (acc, control, name) => {\n      acc[name] = control.getRawValue();\n      return acc;\n    });\n  }\n  /** @internal */\n  _syncPendingControls() {\n    let subtreeUpdated = this._reduceChildren(false, (updated, child) => {\n      return child._syncPendingControls() ? true : updated;\n    });\n    if (subtreeUpdated) this.updateValueAndValidity({\n      onlySelf: true\n    });\n    return subtreeUpdated;\n  }\n  /** @internal */\n  _forEachChild(cb) {\n    Object.keys(this.controls).forEach(key => {\n      // The list of controls can change (for ex. controls might be removed) while the loop\n      // is running (as a result of invoking Forms API in `valueChanges` subscription), so we\n      // have to null check before invoking the callback.\n      const control = this.controls[key];\n      control && cb(control, key);\n    });\n  }\n  /** @internal */\n  _setUpControls() {\n    this._forEachChild(control => {\n      control.setParent(this);\n      control._registerOnCollectionChange(this._onCollectionChange);\n    });\n  }\n  /** @internal */\n  _updateValue() {\n    this.value = this._reduceValue();\n  }\n  /** @internal */\n  _anyControls(condition) {\n    for (const [controlName, control] of Object.entries(this.controls)) {\n      if (this.contains(controlName) && condition(control)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  /** @internal */\n  _reduceValue() {\n    let acc = {};\n    return this._reduceChildren(acc, (acc, control, name) => {\n      if (control.enabled || this.disabled) {\n        acc[name] = control.value;\n      }\n      return acc;\n    });\n  }\n  /** @internal */\n  _reduceChildren(initValue, fn) {\n    let res = initValue;\n    this._forEachChild((control, name) => {\n      res = fn(res, control, name);\n    });\n    return res;\n  }\n  /** @internal */\n  _allControlsDisabled() {\n    for (const controlName of Object.keys(this.controls)) {\n      if (this.controls[controlName].enabled) {\n        return false;\n      }\n    }\n    return Object.keys(this.controls).length > 0 || this.disabled;\n  }\n  /** @internal */\n  _find(name) {\n    return this.controls.hasOwnProperty(name) ? this.controls[name] : null;\n  }\n}\n/**\n * Will validate that none of the controls has a key with a dot\n * Throws other wise\n */\nfunction validateFormGroupControls(controls) {\n  const invalidKeys = Object.keys(controls).filter(key => key.includes('.'));\n  if (invalidKeys.length > 0) {\n    // TODO: make this an error once there are no more uses in G3\n    console.warn(`FormGroup keys cannot include \\`.\\`, please replace the keys for: ${invalidKeys.join(',')}.`);\n  }\n}\nconst UntypedFormGroup = FormGroup;\n/**\n * @description\n * Asserts that the given control is an instance of `FormGroup`\n *\n * @publicApi\n */\nconst isFormGroup = control => control instanceof FormGroup;\n/**\n * Tracks the value and validity state of a collection of `FormControl` instances, each of which has\n * the same value type.\n *\n * `FormRecord` is very similar to {@link FormGroup}, except it can be used with a dynamic keys,\n * with controls added and removed as needed.\n *\n * `FormRecord` accepts one generic argument, which describes the type of the controls it contains.\n *\n * @usageNotes\n *\n * ```ts\n * let numbers = new FormRecord({bill: new FormControl('415-123-456')});\n * numbers.addControl('bob', new FormControl('415-234-567'));\n * numbers.removeControl('bill');\n * ```\n *\n * @publicApi\n */\nclass FormRecord extends FormGroup {}\n/**\n * @description\n * Asserts that the given control is an instance of `FormRecord`\n *\n * @publicApi\n */\nconst isFormRecord = control => control instanceof FormRecord;\n\n/**\n * Token to provide to allow SetDisabledState to always be called when a CVA is added, regardless of\n * whether the control is disabled or enabled.\n *\n * @see {@link FormsModule#withconfig}\n */\nconst CALL_SET_DISABLED_STATE = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'CallSetDisabledState' : '', {\n  providedIn: 'root',\n  factory: () => setDisabledStateDefault\n});\n/**\n * Whether to use the fixed setDisabledState behavior by default.\n */\nconst setDisabledStateDefault = 'always';\nfunction controlPath(name, parent) {\n  return [...parent.path, name];\n}\n/**\n * Links a Form control and a Form directive by setting up callbacks (such as `onChange`) on both\n * instances. This function is typically invoked when form directive is being initialized.\n *\n * @param control Form control instance that should be linked.\n * @param dir Directive that should be linked with a given control.\n */\nfunction setUpControl(control, dir, callSetDisabledState = setDisabledStateDefault) {\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    if (!control) _throwError(dir, 'Cannot find control with');\n    if (!dir.valueAccessor) _throwMissingValueAccessorError(dir);\n  }\n  setUpValidators(control, dir);\n  dir.valueAccessor.writeValue(control.value);\n  // The legacy behavior only calls the CVA's `setDisabledState` if the control is disabled.\n  // If the `callSetDisabledState` option is set to `always`, then this bug is fixed and\n  // the method is always called.\n  if (control.disabled || callSetDisabledState === 'always') {\n    dir.valueAccessor.setDisabledState?.(control.disabled);\n  }\n  setUpViewChangePipeline(control, dir);\n  setUpModelChangePipeline(control, dir);\n  setUpBlurPipeline(control, dir);\n  setUpDisabledChangeHandler(control, dir);\n}\n/**\n * Reverts configuration performed by the `setUpControl` control function.\n * Effectively disconnects form control with a given form directive.\n * This function is typically invoked when corresponding form directive is being destroyed.\n *\n * @param control Form control which should be cleaned up.\n * @param dir Directive that should be disconnected from a given control.\n * @param validateControlPresenceOnChange Flag that indicates whether onChange handler should\n *     contain asserts to verify that it's not called once directive is destroyed. We need this flag\n *     to avoid potentially breaking changes caused by better control cleanup introduced in #39235.\n */\nfunction cleanUpControl(control, dir, validateControlPresenceOnChange = true) {\n  const noop = () => {\n    if (validateControlPresenceOnChange && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      _noControlError(dir);\n    }\n  };\n  // The `valueAccessor` field is typically defined on FromControl and FormControlName directive\n  // instances and there is a logic in `selectValueAccessor` function that throws if it's not the\n  // case. We still check the presence of `valueAccessor` before invoking its methods to make sure\n  // that cleanup works correctly if app code or tests are setup to ignore the error thrown from\n  // `selectValueAccessor`. See https://github.com/angular/angular/issues/40521.\n  if (dir.valueAccessor) {\n    dir.valueAccessor.registerOnChange(noop);\n    dir.valueAccessor.registerOnTouched(noop);\n  }\n  cleanUpValidators(control, dir);\n  if (control) {\n    dir._invokeOnDestroyCallbacks();\n    control._registerOnCollectionChange(() => {});\n  }\n}\nfunction registerOnValidatorChange(validators, onChange) {\n  validators.forEach(validator => {\n    if (validator.registerOnValidatorChange) validator.registerOnValidatorChange(onChange);\n  });\n}\n/**\n * Sets up disabled change handler function on a given form control if ControlValueAccessor\n * associated with a given directive instance supports the `setDisabledState` call.\n *\n * @param control Form control where disabled change handler should be setup.\n * @param dir Corresponding directive instance associated with this control.\n */\nfunction setUpDisabledChangeHandler(control, dir) {\n  if (dir.valueAccessor.setDisabledState) {\n    const onDisabledChange = isDisabled => {\n      dir.valueAccessor.setDisabledState(isDisabled);\n    };\n    control.registerOnDisabledChange(onDisabledChange);\n    // Register a callback function to cleanup disabled change handler\n    // from a control instance when a directive is destroyed.\n    dir._registerOnDestroy(() => {\n      control._unregisterOnDisabledChange(onDisabledChange);\n    });\n  }\n}\n/**\n * Sets up sync and async directive validators on provided form control.\n * This function merges validators from the directive into the validators of the control.\n *\n * @param control Form control where directive validators should be setup.\n * @param dir Directive instance that contains validators to be setup.\n */\nfunction setUpValidators(control, dir) {\n  const validators = getControlValidators(control);\n  if (dir.validator !== null) {\n    control.setValidators(mergeValidators(validators, dir.validator));\n  } else if (typeof validators === 'function') {\n    // If sync validators are represented by a single validator function, we force the\n    // `Validators.compose` call to happen by executing the `setValidators` function with\n    // an array that contains that function. We need this to avoid possible discrepancies in\n    // validators behavior, so sync validators are always processed by the `Validators.compose`.\n    // Note: we should consider moving this logic inside the `setValidators` function itself, so we\n    // have consistent behavior on AbstractControl API level. The same applies to the async\n    // validators logic below.\n    control.setValidators([validators]);\n  }\n  const asyncValidators = getControlAsyncValidators(control);\n  if (dir.asyncValidator !== null) {\n    control.setAsyncValidators(mergeValidators(asyncValidators, dir.asyncValidator));\n  } else if (typeof asyncValidators === 'function') {\n    control.setAsyncValidators([asyncValidators]);\n  }\n  // Re-run validation when validator binding changes, e.g. minlength=3 -> minlength=4\n  const onValidatorChange = () => control.updateValueAndValidity();\n  registerOnValidatorChange(dir._rawValidators, onValidatorChange);\n  registerOnValidatorChange(dir._rawAsyncValidators, onValidatorChange);\n}\n/**\n * Cleans up sync and async directive validators on provided form control.\n * This function reverts the setup performed by the `setUpValidators` function, i.e.\n * removes directive-specific validators from a given control instance.\n *\n * @param control Form control from where directive validators should be removed.\n * @param dir Directive instance that contains validators to be removed.\n * @returns true if a control was updated as a result of this action.\n */\nfunction cleanUpValidators(control, dir) {\n  let isControlUpdated = false;\n  if (control !== null) {\n    if (dir.validator !== null) {\n      const validators = getControlValidators(control);\n      if (Array.isArray(validators) && validators.length > 0) {\n        // Filter out directive validator function.\n        const updatedValidators = validators.filter(validator => validator !== dir.validator);\n        if (updatedValidators.length !== validators.length) {\n          isControlUpdated = true;\n          control.setValidators(updatedValidators);\n        }\n      }\n    }\n    if (dir.asyncValidator !== null) {\n      const asyncValidators = getControlAsyncValidators(control);\n      if (Array.isArray(asyncValidators) && asyncValidators.length > 0) {\n        // Filter out directive async validator function.\n        const updatedAsyncValidators = asyncValidators.filter(asyncValidator => asyncValidator !== dir.asyncValidator);\n        if (updatedAsyncValidators.length !== asyncValidators.length) {\n          isControlUpdated = true;\n          control.setAsyncValidators(updatedAsyncValidators);\n        }\n      }\n    }\n  }\n  // Clear onValidatorChange callbacks by providing a noop function.\n  const noop = () => {};\n  registerOnValidatorChange(dir._rawValidators, noop);\n  registerOnValidatorChange(dir._rawAsyncValidators, noop);\n  return isControlUpdated;\n}\nfunction setUpViewChangePipeline(control, dir) {\n  dir.valueAccessor.registerOnChange(newValue => {\n    control._pendingValue = newValue;\n    control._pendingChange = true;\n    control._pendingDirty = true;\n    if (control.updateOn === 'change') updateControl(control, dir);\n  });\n}\nfunction setUpBlurPipeline(control, dir) {\n  dir.valueAccessor.registerOnTouched(() => {\n    control._pendingTouched = true;\n    if (control.updateOn === 'blur' && control._pendingChange) updateControl(control, dir);\n    if (control.updateOn !== 'submit') control.markAsTouched();\n  });\n}\nfunction updateControl(control, dir) {\n  if (control._pendingDirty) control.markAsDirty();\n  control.setValue(control._pendingValue, {\n    emitModelToViewChange: false\n  });\n  dir.viewToModelUpdate(control._pendingValue);\n  control._pendingChange = false;\n}\nfunction setUpModelChangePipeline(control, dir) {\n  const onChange = (newValue, emitModelEvent) => {\n    // control -> view\n    dir.valueAccessor.writeValue(newValue);\n    // control -> ngModel\n    if (emitModelEvent) dir.viewToModelUpdate(newValue);\n  };\n  control.registerOnChange(onChange);\n  // Register a callback function to cleanup onChange handler\n  // from a control instance when a directive is destroyed.\n  dir._registerOnDestroy(() => {\n    control._unregisterOnChange(onChange);\n  });\n}\n/**\n * Links a FormGroup or FormArray instance and corresponding Form directive by setting up validators\n * present in the view.\n *\n * @param control FormGroup or FormArray instance that should be linked.\n * @param dir Directive that provides view validators.\n */\nfunction setUpFormContainer(control, dir) {\n  if (control == null && (typeof ngDevMode === 'undefined' || ngDevMode)) _throwError(dir, 'Cannot find control with');\n  setUpValidators(control, dir);\n}\n/**\n * Reverts the setup performed by the `setUpFormContainer` function.\n *\n * @param control FormGroup or FormArray instance that should be cleaned up.\n * @param dir Directive that provided view validators.\n * @returns true if a control was updated as a result of this action.\n */\nfunction cleanUpFormContainer(control, dir) {\n  return cleanUpValidators(control, dir);\n}\nfunction _noControlError(dir) {\n  return _throwError(dir, 'There is no FormControl instance attached to form control element with');\n}\nfunction _throwError(dir, message) {\n  const messageEnd = _describeControlLocation(dir);\n  throw new Error(`${message} ${messageEnd}`);\n}\nfunction _describeControlLocation(dir) {\n  const path = dir.path;\n  if (path && path.length > 1) return `path: '${path.join(' -> ')}'`;\n  if (path?.[0]) return `name: '${path}'`;\n  return 'unspecified name attribute';\n}\nfunction _throwMissingValueAccessorError(dir) {\n  const loc = _describeControlLocation(dir);\n  throw new _RuntimeError(-1203 /* RuntimeErrorCode.NG_MISSING_VALUE_ACCESSOR */, `No value accessor for form control ${loc}.`);\n}\nfunction _throwInvalidValueAccessorError(dir) {\n  const loc = _describeControlLocation(dir);\n  throw new _RuntimeError(1200 /* RuntimeErrorCode.NG_VALUE_ACCESSOR_NOT_PROVIDED */, `Value accessor was not provided as an array for form control with ${loc}. ` + `Check that the \\`NG_VALUE_ACCESSOR\\` token is configured as a \\`multi: true\\` provider.`);\n}\nfunction isPropertyUpdated(changes, viewModel) {\n  if (!changes.hasOwnProperty('model')) return false;\n  const change = changes['model'];\n  if (change.isFirstChange()) return true;\n  return !Object.is(viewModel, change.currentValue);\n}\nfunction isBuiltInAccessor(valueAccessor) {\n  // Check if a given value accessor is an instance of a class that directly extends\n  // `BuiltInControlValueAccessor` one.\n  return Object.getPrototypeOf(valueAccessor.constructor) === BuiltInControlValueAccessor;\n}\nfunction syncPendingControls(form, directives) {\n  form._syncPendingControls();\n  directives.forEach(dir => {\n    const control = dir.control;\n    if (control.updateOn === 'submit' && control._pendingChange) {\n      dir.viewToModelUpdate(control._pendingValue);\n      control._pendingChange = false;\n    }\n  });\n}\n// TODO: vsavkin remove it once https://github.com/angular/angular/issues/3011 is implemented\nfunction selectValueAccessor(dir, valueAccessors) {\n  if (!valueAccessors) return null;\n  if (!Array.isArray(valueAccessors) && (typeof ngDevMode === 'undefined' || ngDevMode)) _throwInvalidValueAccessorError(dir);\n  let defaultAccessor = undefined;\n  let builtinAccessor = undefined;\n  let customAccessor = undefined;\n  valueAccessors.forEach(v => {\n    if (v.constructor === DefaultValueAccessor) {\n      defaultAccessor = v;\n    } else if (isBuiltInAccessor(v)) {\n      if (builtinAccessor && (typeof ngDevMode === 'undefined' || ngDevMode)) _throwError(dir, 'More than one built-in value accessor matches form control with');\n      builtinAccessor = v;\n    } else {\n      if (customAccessor && (typeof ngDevMode === 'undefined' || ngDevMode)) _throwError(dir, 'More than one custom value accessor matches form control with');\n      customAccessor = v;\n    }\n  });\n  if (customAccessor) return customAccessor;\n  if (builtinAccessor) return builtinAccessor;\n  if (defaultAccessor) return defaultAccessor;\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    _throwError(dir, 'No valid value accessor for form control with');\n  }\n  return null;\n}\nfunction removeListItem$1(list, el) {\n  const index = list.indexOf(el);\n  if (index > -1) list.splice(index, 1);\n}\n// TODO(kara): remove after deprecation period\nfunction _ngModelWarning(name, type, instance, warningConfig) {\n  if (warningConfig === 'never') return;\n  if ((warningConfig === null || warningConfig === 'once') && !type._ngModelWarningSentOnce || warningConfig === 'always' && !instance._ngModelWarningSent) {\n    console.warn(ngModelWarning(name));\n    type._ngModelWarningSentOnce = true;\n    instance._ngModelWarningSent = true;\n  }\n}\nconst formDirectiveProvider$1 = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => NgForm)\n};\nconst resolvedPromise$1 = (() => Promise.resolve())();\n/**\n * @description\n * Creates a top-level `FormGroup` instance and binds it to a form\n * to track aggregate form value and validation status.\n *\n * As soon as you import the `FormsModule`, this directive becomes active by default on\n * all `<form>` tags.  You don't need to add a special selector.\n *\n * You optionally export the directive into a local template variable using `ngForm` as the key\n * (ex: `#myForm=\"ngForm\"`). This is optional, but useful.  Many properties from the underlying\n * `FormGroup` instance are duplicated on the directive itself, so a reference to it\n * gives you access to the aggregate value and validity status of the form, as well as\n * user interaction properties like `dirty` and `touched`.\n *\n * To register child controls with the form, use `NgModel` with a `name`\n * attribute. You may use `NgModelGroup` to create sub-groups within the form.\n *\n * If necessary, listen to the directive's `ngSubmit` event to be notified when the user has\n * triggered a form submission. The `ngSubmit` event emits the original form\n * submission event.\n *\n * In template driven forms, all `<form>` tags are automatically tagged as `NgForm`.\n * To import the `FormsModule` but skip its usage in some forms,\n * for example, to use native HTML5 validation, add the `ngNoForm` and the `<form>`\n * tags won't create an `NgForm` directive. In reactive forms, using `ngNoForm` is\n * unnecessary because the `<form>` tags are inert. In that case, you would\n * refrain from using the `formGroup` directive.\n *\n * @usageNotes\n *\n * ### Listening for form submission\n *\n * The following example shows how to capture the form values from the \"ngSubmit\" event.\n *\n * {@example forms/ts/simpleForm/simple_form_example.ts region='Component'}\n *\n * ### Setting the update options\n *\n * The following example shows you how to change the \"updateOn\" option from its default using\n * ngFormOptions.\n *\n * ```html\n * <form [ngFormOptions]=\"{updateOn: 'blur'}\">\n *    <input name=\"one\" ngModel>  <!-- this ngModel will update on blur -->\n * </form>\n * ```\n *\n * ### Native DOM validation UI\n *\n * In order to prevent the native DOM form validation UI from interfering with Angular's form\n * validation, Angular automatically adds the `novalidate` attribute on any `<form>` whenever\n * `FormModule` or `ReactiveFormModule` are imported into the application.\n * If you want to explicitly enable native DOM validation UI with Angular forms, you can add the\n * `ngNativeValidate` attribute to the `<form>` element:\n *\n * ```html\n * <form ngNativeValidate>\n *   ...\n * </form>\n * ```\n *\n * @ngModule FormsModule\n * @publicApi\n */\nclass NgForm extends ControlContainer {\n  callSetDisabledState;\n  /**\n   * @description\n   * Returns whether the form submission has been triggered.\n   */\n  get submitted() {\n    return untracked(this.submittedReactive);\n  }\n  /** @internal */\n  _submitted = computed(() => this.submittedReactive(), ...(ngDevMode ? [{\n    debugName: \"_submitted\"\n  }] : []));\n  submittedReactive = signal(false, ...(ngDevMode ? [{\n    debugName: \"submittedReactive\"\n  }] : []));\n  _directives = new Set();\n  /**\n   * @description\n   * The `FormGroup` instance created for this form.\n   */\n  form;\n  /**\n   * @description\n   * Event emitter for the \"ngSubmit\" event\n   */\n  ngSubmit = new EventEmitter();\n  /**\n   * @description\n   * Tracks options for the `NgForm` instance.\n   *\n   * **updateOn**: Sets the default `updateOn` value for all child `NgModels` below it\n   * unless explicitly set by a child `NgModel` using `ngModelOptions`). Defaults to 'change'.\n   * Possible values: `'change'` | `'blur'` | `'submit'`.\n   *\n   */\n  options;\n  constructor(validators, asyncValidators, callSetDisabledState) {\n    super();\n    this.callSetDisabledState = callSetDisabledState;\n    this.form = new FormGroup({}, composeValidators(validators), composeAsyncValidators(asyncValidators));\n  }\n  /** @docs-private */\n  ngAfterViewInit() {\n    this._setUpdateStrategy();\n  }\n  /**\n   * @description\n   * The directive instance.\n   */\n  get formDirective() {\n    return this;\n  }\n  /**\n   * @description\n   * The internal `FormGroup` instance.\n   */\n  get control() {\n    return this.form;\n  }\n  /**\n   * @description\n   * Returns an array representing the path to this group. Because this directive\n   * always lives at the top level of a form, it is always an empty array.\n   */\n  get path() {\n    return [];\n  }\n  /**\n   * @description\n   * Returns a map of the controls in this group.\n   */\n  get controls() {\n    return this.form.controls;\n  }\n  /**\n   * @description\n   * Method that sets up the control directive in this group, re-calculates its value\n   * and validity, and adds the instance to the internal list of directives.\n   *\n   * @param dir The `NgModel` directive instance.\n   */\n  addControl(dir) {\n    resolvedPromise$1.then(() => {\n      const container = this._findContainer(dir.path);\n      dir.control = container.registerControl(dir.name, dir.control);\n      setUpControl(dir.control, dir, this.callSetDisabledState);\n      dir.control.updateValueAndValidity({\n        emitEvent: false\n      });\n      this._directives.add(dir);\n    });\n  }\n  /**\n   * @description\n   * Retrieves the `FormControl` instance from the provided `NgModel` directive.\n   *\n   * @param dir The `NgModel` directive instance.\n   */\n  getControl(dir) {\n    return this.form.get(dir.path);\n  }\n  /**\n   * @description\n   * Removes the `NgModel` instance from the internal list of directives\n   *\n   * @param dir The `NgModel` directive instance.\n   */\n  removeControl(dir) {\n    resolvedPromise$1.then(() => {\n      const container = this._findContainer(dir.path);\n      if (container) {\n        container.removeControl(dir.name);\n      }\n      this._directives.delete(dir);\n    });\n  }\n  /**\n   * @description\n   * Adds a new `NgModelGroup` directive instance to the form.\n   *\n   * @param dir The `NgModelGroup` directive instance.\n   */\n  addFormGroup(dir) {\n    resolvedPromise$1.then(() => {\n      const container = this._findContainer(dir.path);\n      const group = new FormGroup({});\n      setUpFormContainer(group, dir);\n      container.registerControl(dir.name, group);\n      group.updateValueAndValidity({\n        emitEvent: false\n      });\n    });\n  }\n  /**\n   * @description\n   * Removes the `NgModelGroup` directive instance from the form.\n   *\n   * @param dir The `NgModelGroup` directive instance.\n   */\n  removeFormGroup(dir) {\n    resolvedPromise$1.then(() => {\n      const container = this._findContainer(dir.path);\n      if (container) {\n        container.removeControl(dir.name);\n      }\n    });\n  }\n  /**\n   * @description\n   * Retrieves the `FormGroup` for a provided `NgModelGroup` directive instance\n   *\n   * @param dir The `NgModelGroup` directive instance.\n   */\n  getFormGroup(dir) {\n    return this.form.get(dir.path);\n  }\n  /**\n   * Sets the new value for the provided `NgControl` directive.\n   *\n   * @param dir The `NgControl` directive instance.\n   * @param value The new value for the directive's control.\n   */\n  updateModel(dir, value) {\n    resolvedPromise$1.then(() => {\n      const ctrl = this.form.get(dir.path);\n      ctrl.setValue(value);\n    });\n  }\n  /**\n   * @description\n   * Sets the value for this `FormGroup`.\n   *\n   * @param value The new value\n   */\n  setValue(value) {\n    this.control.setValue(value);\n  }\n  /**\n   * @description\n   * Method called when the \"submit\" event is triggered on the form.\n   * Triggers the `ngSubmit` emitter to emit the \"submit\" event as its payload.\n   *\n   * @param $event The \"submit\" event object\n   */\n  onSubmit($event) {\n    this.submittedReactive.set(true);\n    syncPendingControls(this.form, this._directives);\n    this.ngSubmit.emit($event);\n    this.form._events.next(new FormSubmittedEvent(this.control));\n    // Forms with `method=\"dialog\"` have some special behavior\n    // that won't reload the page and that shouldn't be prevented.\n    return $event?.target?.method === 'dialog';\n  }\n  /**\n   * @description\n   * Method called when the \"reset\" event is triggered on the form.\n   */\n  onReset() {\n    this.resetForm();\n  }\n  /**\n   * @description\n   * Resets the form to an initial value and resets its submitted status.\n   *\n   * @param value The new value for the form.\n   */\n  resetForm(value = undefined) {\n    this.form.reset(value);\n    this.submittedReactive.set(false);\n    this.form._events.next(new FormResetEvent(this.form));\n  }\n  _setUpdateStrategy() {\n    if (this.options && this.options.updateOn != null) {\n      this.form._updateOn = this.options.updateOn;\n    }\n  }\n  _findContainer(path) {\n    path.pop();\n    return path.length ? this.form.get(path) : this.form;\n  }\n  static ɵfac = function NgForm_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgForm)(i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10), i0.ɵɵdirectiveInject(CALL_SET_DISABLED_STATE, 8));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgForm,\n    selectors: [[\"form\", 3, \"ngNoForm\", \"\", 3, \"formGroup\", \"\"], [\"ng-form\"], [\"\", \"ngForm\", \"\"]],\n    hostBindings: function NgForm_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"submit\", function NgForm_submit_HostBindingHandler($event) {\n          return ctx.onSubmit($event);\n        })(\"reset\", function NgForm_reset_HostBindingHandler() {\n          return ctx.onReset();\n        });\n      }\n    },\n    inputs: {\n      options: [0, \"ngFormOptions\", \"options\"]\n    },\n    outputs: {\n      ngSubmit: \"ngSubmit\"\n    },\n    exportAs: [\"ngForm\"],\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([formDirectiveProvider$1]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgForm, [{\n    type: Directive,\n    args: [{\n      selector: 'form:not([ngNoForm]):not([formGroup]),ng-form,[ngForm]',\n      providers: [formDirectiveProvider$1],\n      host: {\n        '(submit)': 'onSubmit($event)',\n        '(reset)': 'onReset()'\n      },\n      outputs: ['ngSubmit'],\n      exportAs: 'ngForm',\n      standalone: false\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_ASYNC_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CALL_SET_DISABLED_STATE]\n    }]\n  }], {\n    options: [{\n      type: Input,\n      args: ['ngFormOptions']\n    }]\n  });\n})();\nfunction removeListItem(list, el) {\n  const index = list.indexOf(el);\n  if (index > -1) list.splice(index, 1);\n}\nfunction isFormControlState(formState) {\n  return typeof formState === 'object' && formState !== null && Object.keys(formState).length === 2 && 'value' in formState && 'disabled' in formState;\n}\nconst FormControl = class FormControl extends AbstractControl {\n  /** @publicApi */\n  defaultValue = null;\n  /** @internal */\n  _onChange = [];\n  /** @internal */\n  _pendingValue;\n  /** @internal */\n  _pendingChange = false;\n  constructor(\n  // formState and defaultValue will only be null if T is nullable\n  formState = null, validatorOrOpts, asyncValidator) {\n    super(pickValidators(validatorOrOpts), pickAsyncValidators(asyncValidator, validatorOrOpts));\n    this._applyFormState(formState);\n    this._setUpdateStrategy(validatorOrOpts);\n    this._initObservables();\n    this.updateValueAndValidity({\n      onlySelf: true,\n      // If `asyncValidator` is present, it will trigger control status change from `PENDING` to\n      // `VALID` or `INVALID`.\n      // The status should be broadcasted via the `statusChanges` observable, so we set\n      // `emitEvent` to `true` to allow that during the control creation process.\n      emitEvent: !!this.asyncValidator\n    });\n    if (isOptionsObj(validatorOrOpts) && (validatorOrOpts.nonNullable || validatorOrOpts.initialValueIsDefault)) {\n      if (isFormControlState(formState)) {\n        this.defaultValue = formState.value;\n      } else {\n        this.defaultValue = formState;\n      }\n    }\n  }\n  setValue(value, options = {}) {\n    this.value = this._pendingValue = value;\n    if (this._onChange.length && options.emitModelToViewChange !== false) {\n      this._onChange.forEach(changeFn => changeFn(this.value, options.emitViewToModelChange !== false));\n    }\n    this.updateValueAndValidity(options);\n  }\n  patchValue(value, options = {}) {\n    this.setValue(value, options);\n  }\n  reset(formState = this.defaultValue, options = {}) {\n    this._applyFormState(formState);\n    this.markAsPristine(options);\n    this.markAsUntouched(options);\n    this.setValue(this.value, options);\n    this._pendingChange = false;\n  }\n  /**  @internal */\n  _updateValue() {}\n  /**  @internal */\n  _anyControls(condition) {\n    return false;\n  }\n  /**  @internal */\n  _allControlsDisabled() {\n    return this.disabled;\n  }\n  registerOnChange(fn) {\n    this._onChange.push(fn);\n  }\n  /** @internal */\n  _unregisterOnChange(fn) {\n    removeListItem(this._onChange, fn);\n  }\n  registerOnDisabledChange(fn) {\n    this._onDisabledChange.push(fn);\n  }\n  /** @internal */\n  _unregisterOnDisabledChange(fn) {\n    removeListItem(this._onDisabledChange, fn);\n  }\n  /** @internal */\n  _forEachChild(cb) {}\n  /** @internal */\n  _syncPendingControls() {\n    if (this.updateOn === 'submit') {\n      if (this._pendingDirty) this.markAsDirty();\n      if (this._pendingTouched) this.markAsTouched();\n      if (this._pendingChange) {\n        this.setValue(this._pendingValue, {\n          onlySelf: true,\n          emitModelToViewChange: false\n        });\n        return true;\n      }\n    }\n    return false;\n  }\n  _applyFormState(formState) {\n    if (isFormControlState(formState)) {\n      this.value = this._pendingValue = formState.value;\n      formState.disabled ? this.disable({\n        onlySelf: true,\n        emitEvent: false\n      }) : this.enable({\n        onlySelf: true,\n        emitEvent: false\n      });\n    } else {\n      this.value = this._pendingValue = formState;\n    }\n  }\n};\nconst UntypedFormControl = FormControl;\n/**\n * @description\n * Asserts that the given control is an instance of `FormControl`\n *\n * @publicApi\n */\nconst isFormControl = control => control instanceof FormControl;\n\n/**\n * @description\n * A base class for code shared between the `NgModelGroup` and `FormGroupName` directives.\n *\n * @publicApi\n */\nclass AbstractFormGroupDirective extends ControlContainer {\n  /**\n   * @description\n   * The parent control for the group\n   *\n   * @internal\n   */\n  _parent;\n  /** @docs-private */\n  ngOnInit() {\n    this._checkParentType();\n    // Register the group with its parent group.\n    this.formDirective.addFormGroup(this);\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    if (this.formDirective) {\n      // Remove the group from its parent group.\n      this.formDirective.removeFormGroup(this);\n    }\n  }\n  /**\n   * @description\n   * The `FormGroup` bound to this directive.\n   */\n  get control() {\n    return this.formDirective.getFormGroup(this);\n  }\n  /**\n   * @description\n   * The path to this group from the top-level directive.\n   */\n  get path() {\n    return controlPath(this.name == null ? this.name : this.name.toString(), this._parent);\n  }\n  /**\n   * @description\n   * The top-level directive for this group if present, otherwise null.\n   */\n  get formDirective() {\n    return this._parent ? this._parent.formDirective : null;\n  }\n  /** @internal */\n  _checkParentType() {}\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵAbstractFormGroupDirective_BaseFactory;\n    return function AbstractFormGroupDirective_Factory(__ngFactoryType__) {\n      return (ɵAbstractFormGroupDirective_BaseFactory || (ɵAbstractFormGroupDirective_BaseFactory = i0.ɵɵgetInheritedFactory(AbstractFormGroupDirective)))(__ngFactoryType__ || AbstractFormGroupDirective);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: AbstractFormGroupDirective,\n    standalone: false,\n    features: [i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbstractFormGroupDirective, [{\n    type: Directive,\n    args: [{\n      standalone: false\n    }]\n  }], null, null);\n})();\nfunction modelParentException() {\n  return new _RuntimeError(1350 /* RuntimeErrorCode.NGMODEL_IN_FORM_GROUP */, `\n    ngModel cannot be used to register form controls with a parent formGroup directive.  Try using\n    formGroup's partner directive \"formControlName\" instead.  Example:\n\n    ${formControlNameExample}\n\n    Or, if you'd like to avoid registering this form control, indicate that it's standalone in ngModelOptions:\n\n    Example:\n\n    ${ngModelWithFormGroupExample}`);\n}\nfunction formGroupNameException() {\n  return new _RuntimeError(1351 /* RuntimeErrorCode.NGMODEL_IN_FORM_GROUP_NAME */, `\n    ngModel cannot be used to register form controls with a parent formGroupName or formArrayName directive.\n\n    Option 1: Use formControlName instead of ngModel (reactive strategy):\n\n    ${formGroupNameExample}\n\n    Option 2:  Update ngModel's parent be ngModelGroup (template-driven strategy):\n\n    ${ngModelGroupExample}`);\n}\nfunction missingNameException() {\n  return new _RuntimeError(1352 /* RuntimeErrorCode.NGMODEL_WITHOUT_NAME */, `If ngModel is used within a form tag, either the name attribute must be set or the form\n    control must be defined as 'standalone' in ngModelOptions.\n\n    Example 1: <input [(ngModel)]=\"person.firstName\" name=\"first\">\n    Example 2: <input [(ngModel)]=\"person.firstName\" [ngModelOptions]=\"{standalone: true}\">`);\n}\nfunction modelGroupParentException() {\n  return new _RuntimeError(1353 /* RuntimeErrorCode.NGMODELGROUP_IN_FORM_GROUP */, `\n    ngModelGroup cannot be used with a parent formGroup directive.\n\n    Option 1: Use formGroupName instead of ngModelGroup (reactive strategy):\n\n    ${formGroupNameExample}\n\n    Option 2:  Use a regular form tag instead of the formGroup directive (template-driven strategy):\n\n    ${ngModelGroupExample}`);\n}\nconst modelGroupProvider = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => NgModelGroup)\n};\n/**\n * @description\n * Creates and binds a `FormGroup` instance to a DOM element.\n *\n * This directive can only be used as a child of `NgForm` (within `<form>` tags).\n *\n * Use this directive to validate a sub-group of your form separately from the\n * rest of your form, or if some values in your domain model make more sense\n * to consume together in a nested object.\n *\n * Provide a name for the sub-group and it will become the key\n * for the sub-group in the form's full value. If you need direct access, export the directive into\n * a local template variable using `ngModelGroup` (ex: `#myGroup=\"ngModelGroup\"`).\n *\n * @usageNotes\n *\n * ### Consuming controls in a grouping\n *\n * The following example shows you how to combine controls together in a sub-group\n * of the form.\n *\n * {@example forms/ts/ngModelGroup/ng_model_group_example.ts region='Component'}\n *\n * @ngModule FormsModule\n * @publicApi\n */\nclass NgModelGroup extends AbstractFormGroupDirective {\n  /**\n   * @description\n   * Tracks the name of the `NgModelGroup` bound to the directive. The name corresponds\n   * to a key in the parent `NgForm`.\n   */\n  name = '';\n  constructor(parent, validators, asyncValidators) {\n    super();\n    this._parent = parent;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n  }\n  /** @internal */\n  _checkParentType() {\n    if (!(this._parent instanceof NgModelGroup) && !(this._parent instanceof NgForm) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw modelGroupParentException();\n    }\n  }\n  static ɵfac = function NgModelGroup_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgModelGroup)(i0.ɵɵdirectiveInject(ControlContainer, 5), i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgModelGroup,\n    selectors: [[\"\", \"ngModelGroup\", \"\"]],\n    inputs: {\n      name: [0, \"ngModelGroup\", \"name\"]\n    },\n    exportAs: [\"ngModelGroup\"],\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([modelGroupProvider]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgModelGroup, [{\n    type: Directive,\n    args: [{\n      selector: '[ngModelGroup]',\n      providers: [modelGroupProvider],\n      exportAs: 'ngModelGroup',\n      standalone: false\n    }]\n  }], () => [{\n    type: ControlContainer,\n    decorators: [{\n      type: Host\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_ASYNC_VALIDATORS]\n    }]\n  }], {\n    name: [{\n      type: Input,\n      args: ['ngModelGroup']\n    }]\n  });\n})();\nconst formControlBinding$1 = {\n  provide: NgControl,\n  useExisting: forwardRef(() => NgModel)\n};\n/**\n * `ngModel` forces an additional change detection run when its inputs change:\n * E.g.:\n * ```html\n * <div>{{myModel.valid}}</div>\n * <input [(ngModel)]=\"myValue\" #myModel=\"ngModel\">\n * ```\n * I.e. `ngModel` can export itself on the element and then be used in the template.\n * Normally, this would result in expressions before the `input` that use the exported directive\n * to have an old value as they have been\n * dirty checked before. As this is a very common case for `ngModel`, we added this second change\n * detection run.\n *\n * Notes:\n * - this is just one extra run no matter how many `ngModel`s have been changed.\n * - this is a general problem when using `exportAs` for directives!\n */\nconst resolvedPromise = (() => Promise.resolve())();\n/**\n * @description\n * Creates a `FormControl` instance from a [domain\n * model](https://en.wikipedia.org/wiki/Domain_model) and binds it to a form control element.\n *\n * The `FormControl` instance tracks the value, user interaction, and\n * validation status of the control and keeps the view synced with the model. If used\n * within a parent form, the directive also registers itself with the form as a child\n * control.\n *\n * This directive is used by itself or as part of a larger form. Use the\n * `ngModel` selector to activate it.\n *\n * It accepts a domain model as an optional `Input`. If you have a one-way binding\n * to `ngModel` with `[]` syntax, changing the domain model's value in the component\n * class sets the value in the view. If you have a two-way binding with `[()]` syntax\n * (also known as 'banana-in-a-box syntax'), the value in the UI always syncs back to\n * the domain model in your class.\n *\n * To inspect the properties of the associated `FormControl` (like the validity state),\n * export the directive into a local template variable using `ngModel` as the key (ex:\n * `#myVar=\"ngModel\"`). You can then access the control using the directive's `control` property.\n * However, the most commonly used properties (like `valid` and `dirty`) also exist on the control\n * for direct access. See a full list of properties directly available in\n * `AbstractControlDirective`.\n *\n * @see {@link RadioControlValueAccessor}\n * @see {@link SelectControlValueAccessor}\n *\n * @usageNotes\n *\n * ### Using ngModel on a standalone control\n *\n * The following examples show a simple standalone control using `ngModel`:\n *\n * {@example forms/ts/simpleNgModel/simple_ng_model_example.ts region='Component'}\n *\n * When using the `ngModel` within `<form>` tags, you'll also need to supply a `name` attribute\n * so that the control can be registered with the parent form under that name.\n *\n * In the context of a parent form, it's often unnecessary to include one-way or two-way binding,\n * as the parent form syncs the value for you. You access its properties by exporting it into a\n * local template variable using `ngForm` such as (`#f=\"ngForm\"`). Use the variable where\n * needed on form submission.\n *\n * If you do need to populate initial values into your form, using a one-way binding for\n * `ngModel` tends to be sufficient as long as you use the exported form's value rather\n * than the domain model's value on submit.\n *\n * ### Using ngModel within a form\n *\n * The following example shows controls using `ngModel` within a form:\n *\n * {@example forms/ts/simpleForm/simple_form_example.ts region='Component'}\n *\n * ### Using a standalone ngModel within a group\n *\n * The following example shows you how to use a standalone ngModel control\n * within a form. This controls the display of the form, but doesn't contain form data.\n *\n * ```html\n * <form>\n *   <input name=\"login\" ngModel placeholder=\"Login\">\n *   <input type=\"checkbox\" ngModel [ngModelOptions]=\"{standalone: true}\"> Show more options?\n * </form>\n * <!-- form value: {login: ''} -->\n * ```\n *\n * ### Setting the ngModel `name` attribute through options\n *\n * The following example shows you an alternate way to set the name attribute. Here,\n * an attribute identified as name is used within a custom form control component. To still be able\n * to specify the NgModel's name, you must specify it using the `ngModelOptions` input instead.\n *\n * ```html\n * <form>\n *   <my-custom-form-control name=\"Nancy\" ngModel [ngModelOptions]=\"{name: 'user'}\">\n *   </my-custom-form-control>\n * </form>\n * <!-- form value: {user: ''} -->\n * ```\n *\n * @ngModule FormsModule\n * @publicApi\n */\nclass NgModel extends NgControl {\n  _changeDetectorRef;\n  callSetDisabledState;\n  control = new FormControl();\n  // At runtime we coerce arbitrary values assigned to the \"disabled\" input to a \"boolean\".\n  // This is not reflected in the type of the property because outside of templates, consumers\n  // should only deal with booleans. In templates, a string is allowed for convenience and to\n  // match the native \"disabled attribute\" semantics which can be observed on input elements.\n  // This static member tells the compiler that values of type \"string\" can also be assigned\n  // to the input in a template.\n  /** @docs-private */\n  static ngAcceptInputType_isDisabled;\n  /** @internal */\n  _registered = false;\n  /**\n   * Internal reference to the view model value.\n   * @docs-private\n   */\n  viewModel;\n  /**\n   * @description\n   * Tracks the name bound to the directive. If a parent form exists, it\n   * uses this name as a key to retrieve this control's value.\n   */\n  name = '';\n  /**\n   * @description\n   * Tracks whether the control is disabled.\n   */\n  isDisabled;\n  /**\n   * @description\n   * Tracks the value bound to this directive.\n   */\n  model;\n  /**\n   * @description\n   * Tracks the configuration options for this `ngModel` instance.\n   *\n   * **name**: An alternative to setting the name attribute on the form control element. See\n   * the [example](api/forms/NgModel#using-ngmodel-on-a-standalone-control) for using `NgModel`\n   * as a standalone control.\n   *\n   * **standalone**: When set to true, the `ngModel` will not register itself with its parent form,\n   * and acts as if it's not in the form. Defaults to false. If no parent form exists, this option\n   * has no effect.\n   *\n   * **updateOn**: Defines the event upon which the form control value and validity update.\n   * Defaults to 'change'. Possible values: `'change'` | `'blur'` | `'submit'`.\n   *\n   */\n  options;\n  /**\n   * @description\n   * Event emitter for producing the `ngModelChange` event after\n   * the view model updates.\n   */\n  update = new EventEmitter();\n  constructor(parent, validators, asyncValidators, valueAccessors, _changeDetectorRef, callSetDisabledState) {\n    super();\n    this._changeDetectorRef = _changeDetectorRef;\n    this.callSetDisabledState = callSetDisabledState;\n    this._parent = parent;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n    this.valueAccessor = selectValueAccessor(this, valueAccessors);\n  }\n  /** @docs-private */\n  ngOnChanges(changes) {\n    this._checkForErrors();\n    if (!this._registered || 'name' in changes) {\n      if (this._registered) {\n        this._checkName();\n        if (this.formDirective) {\n          // We can't call `formDirective.removeControl(this)`, because the `name` has already been\n          // changed. We also can't reset the name temporarily since the logic in `removeControl`\n          // is inside a promise and it won't run immediately. We work around it by giving it an\n          // object with the same shape instead.\n          const oldName = changes['name'].previousValue;\n          this.formDirective.removeControl({\n            name: oldName,\n            path: this._getPath(oldName)\n          });\n        }\n      }\n      this._setUpControl();\n    }\n    if ('isDisabled' in changes) {\n      this._updateDisabled(changes);\n    }\n    if (isPropertyUpdated(changes, this.viewModel)) {\n      this._updateValue(this.model);\n      this.viewModel = this.model;\n    }\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    this.formDirective && this.formDirective.removeControl(this);\n  }\n  /**\n   * @description\n   * Returns an array that represents the path from the top-level form to this control.\n   * Each index is the string name of the control on that level.\n   */\n  get path() {\n    return this._getPath(this.name);\n  }\n  /**\n   * @description\n   * The top-level directive for this control if present, otherwise null.\n   */\n  get formDirective() {\n    return this._parent ? this._parent.formDirective : null;\n  }\n  /**\n   * @description\n   * Sets the new value for the view model and emits an `ngModelChange` event.\n   *\n   * @param newValue The new value emitted by `ngModelChange`.\n   */\n  viewToModelUpdate(newValue) {\n    this.viewModel = newValue;\n    this.update.emit(newValue);\n  }\n  _setUpControl() {\n    this._setUpdateStrategy();\n    this._isStandalone() ? this._setUpStandalone() : this.formDirective.addControl(this);\n    this._registered = true;\n  }\n  _setUpdateStrategy() {\n    if (this.options && this.options.updateOn != null) {\n      this.control._updateOn = this.options.updateOn;\n    }\n  }\n  _isStandalone() {\n    return !this._parent || !!(this.options && this.options.standalone);\n  }\n  _setUpStandalone() {\n    setUpControl(this.control, this, this.callSetDisabledState);\n    this.control.updateValueAndValidity({\n      emitEvent: false\n    });\n  }\n  _checkForErrors() {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this._isStandalone()) {\n      checkParentType$1(this._parent);\n    }\n    this._checkName();\n  }\n  _checkName() {\n    if (this.options && this.options.name) this.name = this.options.name;\n    if (!this._isStandalone() && !this.name && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw missingNameException();\n    }\n  }\n  _updateValue(value) {\n    resolvedPromise.then(() => {\n      this.control.setValue(value, {\n        emitViewToModelChange: false\n      });\n      this._changeDetectorRef?.markForCheck();\n    });\n  }\n  _updateDisabled(changes) {\n    const disabledValue = changes['isDisabled'].currentValue;\n    // checking for 0 to avoid breaking change\n    const isDisabled = disabledValue !== 0 && booleanAttribute(disabledValue);\n    resolvedPromise.then(() => {\n      if (isDisabled && !this.control.disabled) {\n        this.control.disable();\n      } else if (!isDisabled && this.control.disabled) {\n        this.control.enable();\n      }\n      this._changeDetectorRef?.markForCheck();\n    });\n  }\n  _getPath(controlName) {\n    return this._parent ? controlPath(controlName, this._parent) : [controlName];\n  }\n  static ɵfac = function NgModel_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgModel)(i0.ɵɵdirectiveInject(ControlContainer, 9), i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_VALUE_ACCESSOR, 10), i0.ɵɵdirectiveInject(ChangeDetectorRef, 8), i0.ɵɵdirectiveInject(CALL_SET_DISABLED_STATE, 8));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgModel,\n    selectors: [[\"\", \"ngModel\", \"\", 3, \"formControlName\", \"\", 3, \"formControl\", \"\"]],\n    inputs: {\n      name: \"name\",\n      isDisabled: [0, \"disabled\", \"isDisabled\"],\n      model: [0, \"ngModel\", \"model\"],\n      options: [0, \"ngModelOptions\", \"options\"]\n    },\n    outputs: {\n      update: \"ngModelChange\"\n    },\n    exportAs: [\"ngModel\"],\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([formControlBinding$1]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgModel, [{\n    type: Directive,\n    args: [{\n      selector: '[ngModel]:not([formControlName]):not([formControl])',\n      providers: [formControlBinding$1],\n      exportAs: 'ngModel',\n      standalone: false\n    }]\n  }], () => [{\n    type: ControlContainer,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Host\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_ASYNC_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_VALUE_ACCESSOR]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ChangeDetectorRef]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CALL_SET_DISABLED_STATE]\n    }]\n  }], {\n    name: [{\n      type: Input\n    }],\n    isDisabled: [{\n      type: Input,\n      args: ['disabled']\n    }],\n    model: [{\n      type: Input,\n      args: ['ngModel']\n    }],\n    options: [{\n      type: Input,\n      args: ['ngModelOptions']\n    }],\n    update: [{\n      type: Output,\n      args: ['ngModelChange']\n    }]\n  });\n})();\nfunction checkParentType$1(parent) {\n  if (!(parent instanceof NgModelGroup) && parent instanceof AbstractFormGroupDirective) {\n    throw formGroupNameException();\n  } else if (!(parent instanceof NgModelGroup) && !(parent instanceof NgForm)) {\n    throw modelParentException();\n  }\n}\n\n/**\n * @description\n *\n * Adds `novalidate` attribute to all forms by default.\n *\n * `novalidate` is used to disable browser's native form validation.\n *\n * If you want to use native validation with Angular forms, just add `ngNativeValidate` attribute:\n *\n * ```html\n * <form ngNativeValidate></form>\n * ```\n *\n * @publicApi\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n */\nclass ɵNgNoValidate {\n  static ɵfac = function ɵNgNoValidate_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ɵNgNoValidate)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ɵNgNoValidate,\n    selectors: [[\"form\", 3, \"ngNoForm\", \"\", 3, \"ngNativeValidate\", \"\"]],\n    hostAttrs: [\"novalidate\", \"\"],\n    standalone: false\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ɵNgNoValidate, [{\n    type: Directive,\n    args: [{\n      selector: 'form:not([ngNoForm]):not([ngNativeValidate])',\n      host: {\n        'novalidate': ''\n      },\n      standalone: false\n    }]\n  }], null, null);\n})();\nconst NUMBER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => NumberValueAccessor),\n  multi: true\n};\n/**\n * @description\n * The `ControlValueAccessor` for writing a number value and listening to number input changes.\n * The value accessor is used by the `FormControlDirective`, `FormControlName`, and `NgModel`\n * directives.\n *\n * @usageNotes\n *\n * ### Using a number input with a reactive form.\n *\n * The following example shows how to use a number input with a reactive form.\n *\n * ```ts\n * const totalCountControl = new FormControl();\n * ```\n *\n * ```html\n * <input type=\"number\" [formControl]=\"totalCountControl\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass NumberValueAccessor extends BuiltInControlValueAccessor {\n  /**\n   * Sets the \"value\" property on the input element.\n   * @docs-private\n   */\n  writeValue(value) {\n    // The value needs to be normalized for IE9, otherwise it is set to 'null' when null\n    const normalizedValue = value == null ? '' : value;\n    this.setProperty('value', normalizedValue);\n  }\n  /**\n   * Registers a function called when the control value changes.\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this.onChange = value => {\n      fn(value == '' ? null : parseFloat(value));\n    };\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵNumberValueAccessor_BaseFactory;\n    return function NumberValueAccessor_Factory(__ngFactoryType__) {\n      return (ɵNumberValueAccessor_BaseFactory || (ɵNumberValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(NumberValueAccessor)))(__ngFactoryType__ || NumberValueAccessor);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NumberValueAccessor,\n    selectors: [[\"input\", \"type\", \"number\", \"formControlName\", \"\"], [\"input\", \"type\", \"number\", \"formControl\", \"\"], [\"input\", \"type\", \"number\", \"ngModel\", \"\"]],\n    hostBindings: function NumberValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function NumberValueAccessor_input_HostBindingHandler($event) {\n          return ctx.onChange($event.target.value);\n        })(\"blur\", function NumberValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([NUMBER_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NumberValueAccessor, [{\n    type: Directive,\n    args: [{\n      selector: 'input[type=number][formControlName],input[type=number][formControl],input[type=number][ngModel]',\n      host: {\n        '(input)': 'onChange($any($event.target).value)',\n        '(blur)': 'onTouched()'\n      },\n      providers: [NUMBER_VALUE_ACCESSOR],\n      standalone: false\n    }]\n  }], null, null);\n})();\nconst RADIO_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => RadioControlValueAccessor),\n  multi: true\n};\nfunction throwNameError() {\n  throw new _RuntimeError(1202 /* RuntimeErrorCode.NAME_AND_FORM_CONTROL_NAME_MUST_MATCH */, `\n      If you define both a name and a formControlName attribute on your radio button, their values\n      must match. Ex: <input type=\"radio\" formControlName=\"food\" name=\"food\">\n    `);\n}\n/**\n * @description\n * Class used by Angular to track radio buttons. For internal use only.\n */\nclass RadioControlRegistry {\n  _accessors = [];\n  /**\n   * @description\n   * Adds a control to the internal registry. For internal use only.\n   */\n  add(control, accessor) {\n    this._accessors.push([control, accessor]);\n  }\n  /**\n   * @description\n   * Removes a control from the internal registry. For internal use only.\n   */\n  remove(accessor) {\n    for (let i = this._accessors.length - 1; i >= 0; --i) {\n      if (this._accessors[i][1] === accessor) {\n        this._accessors.splice(i, 1);\n        return;\n      }\n    }\n  }\n  /**\n   * @description\n   * Selects a radio button. For internal use only.\n   */\n  select(accessor) {\n    this._accessors.forEach(c => {\n      if (this._isSameGroup(c, accessor) && c[1] !== accessor) {\n        c[1].fireUncheck(accessor.value);\n      }\n    });\n  }\n  _isSameGroup(controlPair, accessor) {\n    if (!controlPair[0].control) return false;\n    return controlPair[0]._parent === accessor._control._parent && controlPair[1].name === accessor.name;\n  }\n  static ɵfac = function RadioControlRegistry_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RadioControlRegistry)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RadioControlRegistry,\n    factory: RadioControlRegistry.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RadioControlRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * @description\n * The `ControlValueAccessor` for writing radio control values and listening to radio control\n * changes. The value accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n * @usageNotes\n *\n * ### Using radio buttons with reactive form directives\n *\n * The follow example shows how to use radio buttons in a reactive form. When using radio buttons in\n * a reactive form, radio buttons in the same group should have the same `formControlName`.\n * Providing a `name` attribute is optional.\n *\n * {@example forms/ts/reactiveRadioButtons/reactive_radio_button_example.ts region='Reactive'}\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass RadioControlValueAccessor extends BuiltInControlValueAccessor {\n  _registry;\n  _injector;\n  /** @internal */\n  _state;\n  /** @internal */\n  _control;\n  /** @internal */\n  _fn;\n  setDisabledStateFired = false;\n  /**\n   * The registered callback function called when a change event occurs on the input element.\n   * Note: we declare `onChange` here (also used as host listener) as a function with no arguments\n   * to override the `onChange` function (which expects 1 argument) in the parent\n   * `BaseControlValueAccessor` class.\n   * @docs-private\n   */\n  onChange = () => {};\n  /**\n   * @description\n   * Tracks the name of the radio input element.\n   */\n  name;\n  /**\n   * @description\n   * Tracks the name of the `FormControl` bound to the directive. The name corresponds\n   * to a key in the parent `FormGroup` or `FormArray`.\n   */\n  formControlName;\n  /**\n   * @description\n   * Tracks the value of the radio input element\n   */\n  value;\n  callSetDisabledState = inject(CALL_SET_DISABLED_STATE, {\n    optional: true\n  }) ?? setDisabledStateDefault;\n  constructor(renderer, elementRef, _registry, _injector) {\n    super(renderer, elementRef);\n    this._registry = _registry;\n    this._injector = _injector;\n  }\n  /** @docs-private */\n  ngOnInit() {\n    this._control = this._injector.get(NgControl);\n    this._checkName();\n    this._registry.add(this._control, this);\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    this._registry.remove(this);\n  }\n  /**\n   * Sets the \"checked\" property value on the radio input element.\n   * @docs-private\n   */\n  writeValue(value) {\n    this._state = value === this.value;\n    this.setProperty('checked', this._state);\n  }\n  /**\n   * Registers a function called when the control value changes.\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this._fn = fn;\n    this.onChange = () => {\n      fn(this.value);\n      this._registry.select(this);\n    };\n  }\n  /** @docs-private */\n  setDisabledState(isDisabled) {\n    /**\n     * `setDisabledState` is supposed to be called whenever the disabled state of a control changes,\n     * including upon control creation. However, a longstanding bug caused the method to not fire\n     * when an *enabled* control was attached. This bug was fixed in v15 in #47576.\n     *\n     * This had a side effect: previously, it was possible to instantiate a reactive form control\n     * with `[attr.disabled]=true`, even though the corresponding control was enabled in the\n     * model. This resulted in a mismatch between the model and the DOM. Now, because\n     * `setDisabledState` is always called, the value in the DOM will be immediately overwritten\n     * with the \"correct\" enabled value.\n     *\n     * However, the fix also created an exceptional case: radio buttons. Because Reactive Forms\n     * models the entire group of radio buttons as a single `FormControl`, there is no way to\n     * control the disabled state for individual radios, so they can no longer be configured as\n     * disabled. Thus, we keep the old behavior for radio buttons, so that `[attr.disabled]`\n     * continues to work. Specifically, we drop the first call to `setDisabledState` if `disabled`\n     * is `false`, and we are not in legacy mode.\n     */\n    if (this.setDisabledStateFired || isDisabled || this.callSetDisabledState === 'whenDisabledForLegacyCode') {\n      this.setProperty('disabled', isDisabled);\n    }\n    this.setDisabledStateFired = true;\n  }\n  /**\n   * Sets the \"value\" on the radio input element and unchecks it.\n   *\n   * @param value\n   */\n  fireUncheck(value) {\n    this.writeValue(value);\n  }\n  _checkName() {\n    if (this.name && this.formControlName && this.name !== this.formControlName && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwNameError();\n    }\n    if (!this.name && this.formControlName) this.name = this.formControlName;\n  }\n  static ɵfac = function RadioControlValueAccessor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || RadioControlValueAccessor)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(RadioControlRegistry), i0.ɵɵdirectiveInject(i0.Injector));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RadioControlValueAccessor,\n    selectors: [[\"input\", \"type\", \"radio\", \"formControlName\", \"\"], [\"input\", \"type\", \"radio\", \"formControl\", \"\"], [\"input\", \"type\", \"radio\", \"ngModel\", \"\"]],\n    hostBindings: function RadioControlValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function RadioControlValueAccessor_change_HostBindingHandler() {\n          return ctx.onChange();\n        })(\"blur\", function RadioControlValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    inputs: {\n      name: \"name\",\n      formControlName: \"formControlName\",\n      value: \"value\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([RADIO_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RadioControlValueAccessor, [{\n    type: Directive,\n    args: [{\n      selector: 'input[type=radio][formControlName],input[type=radio][formControl],input[type=radio][ngModel]',\n      host: {\n        '(change)': 'onChange()',\n        '(blur)': 'onTouched()'\n      },\n      providers: [RADIO_VALUE_ACCESSOR],\n      standalone: false\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: RadioControlRegistry\n  }, {\n    type: i0.Injector\n  }], {\n    name: [{\n      type: Input\n    }],\n    formControlName: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }]\n  });\n})();\nconst RANGE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => RangeValueAccessor),\n  multi: true\n};\n/**\n * @description\n * The `ControlValueAccessor` for writing a range value and listening to range input changes.\n * The value accessor is used by the `FormControlDirective`, `FormControlName`, and  `NgModel`\n * directives.\n *\n * @usageNotes\n *\n * ### Using a range input with a reactive form\n *\n * The following example shows how to use a range input with a reactive form.\n *\n * ```ts\n * const ageControl = new FormControl();\n * ```\n *\n * ```html\n * <input type=\"range\" [formControl]=\"ageControl\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass RangeValueAccessor extends BuiltInControlValueAccessor {\n  /**\n   * Sets the \"value\" property on the input element.\n   * @docs-private\n   */\n  writeValue(value) {\n    this.setProperty('value', parseFloat(value));\n  }\n  /**\n   * Registers a function called when the control value changes.\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this.onChange = value => {\n      fn(value == '' ? null : parseFloat(value));\n    };\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵRangeValueAccessor_BaseFactory;\n    return function RangeValueAccessor_Factory(__ngFactoryType__) {\n      return (ɵRangeValueAccessor_BaseFactory || (ɵRangeValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(RangeValueAccessor)))(__ngFactoryType__ || RangeValueAccessor);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RangeValueAccessor,\n    selectors: [[\"input\", \"type\", \"range\", \"formControlName\", \"\"], [\"input\", \"type\", \"range\", \"formControl\", \"\"], [\"input\", \"type\", \"range\", \"ngModel\", \"\"]],\n    hostBindings: function RangeValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function RangeValueAccessor_change_HostBindingHandler($event) {\n          return ctx.onChange($event.target.value);\n        })(\"input\", function RangeValueAccessor_input_HostBindingHandler($event) {\n          return ctx.onChange($event.target.value);\n        })(\"blur\", function RangeValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([RANGE_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RangeValueAccessor, [{\n    type: Directive,\n    args: [{\n      selector: 'input[type=range][formControlName],input[type=range][formControl],input[type=range][ngModel]',\n      host: {\n        '(change)': 'onChange($any($event.target).value)',\n        '(input)': 'onChange($any($event.target).value)',\n        '(blur)': 'onTouched()'\n      },\n      providers: [RANGE_VALUE_ACCESSOR],\n      standalone: false\n    }]\n  }], null, null);\n})();\n\n/**\n * Token to provide to turn off the ngModel warning on formControl and formControlName.\n */\nconst NG_MODEL_WITH_FORM_CONTROL_WARNING = new InjectionToken(ngDevMode ? 'NgModelWithFormControlWarning' : '');\nconst formControlBinding = {\n  provide: NgControl,\n  useExisting: forwardRef(() => FormControlDirective)\n};\n/**\n * @description\n * Synchronizes a standalone `FormControl` instance to a form control element.\n *\n * Note that support for using the `ngModel` input property and `ngModelChange` event with reactive\n * form directives was deprecated in Angular v6 and is scheduled for removal in\n * a future version of Angular.\n *\n * @see [Reactive Forms Guide](guide/forms/reactive-forms)\n * @see {@link FormControl}\n * @see {@link AbstractControl}\n *\n * @usageNotes\n *\n * The following example shows how to register a standalone control and set its value.\n *\n * {@example forms/ts/simpleFormControl/simple_form_control_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\nclass FormControlDirective extends NgControl {\n  _ngModelWarningConfig;\n  callSetDisabledState;\n  /**\n   * Internal reference to the view model value.\n   * @docs-private\n   */\n  viewModel;\n  /**\n   * @description\n   * Tracks the `FormControl` instance bound to the directive.\n   */\n  form;\n  /**\n   * @description\n   * Triggers a warning in dev mode that this input should not be used with reactive forms.\n   */\n  set isDisabled(isDisabled) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      console.warn(disabledAttrWarning);\n    }\n  }\n  // TODO(kara): remove next 4 properties once deprecation period is over\n  /** @deprecated as of v6 */\n  model;\n  /** @deprecated as of v6 */\n  update = new EventEmitter();\n  /**\n   * @description\n   * Static property used to track whether any ngModel warnings have been sent across\n   * all instances of FormControlDirective. Used to support warning config of \"once\".\n   *\n   * @internal\n   */\n  static _ngModelWarningSentOnce = false;\n  /**\n   * @description\n   * Instance property used to track whether an ngModel warning has been sent out for this\n   * particular `FormControlDirective` instance. Used to support warning config of \"always\".\n   *\n   * @internal\n   */\n  _ngModelWarningSent = false;\n  constructor(validators, asyncValidators, valueAccessors, _ngModelWarningConfig, callSetDisabledState) {\n    super();\n    this._ngModelWarningConfig = _ngModelWarningConfig;\n    this.callSetDisabledState = callSetDisabledState;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n    this.valueAccessor = selectValueAccessor(this, valueAccessors);\n  }\n  /** @docs-private */\n  ngOnChanges(changes) {\n    if (this._isControlChanged(changes)) {\n      const previousForm = changes['form'].previousValue;\n      if (previousForm) {\n        cleanUpControl(previousForm, this, /* validateControlPresenceOnChange */false);\n      }\n      setUpControl(this.form, this, this.callSetDisabledState);\n      this.form.updateValueAndValidity({\n        emitEvent: false\n      });\n    }\n    if (isPropertyUpdated(changes, this.viewModel)) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        _ngModelWarning('formControl', FormControlDirective, this, this._ngModelWarningConfig);\n      }\n      this.form.setValue(this.model);\n      this.viewModel = this.model;\n    }\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    if (this.form) {\n      cleanUpControl(this.form, this, /* validateControlPresenceOnChange */false);\n    }\n  }\n  /**\n   * @description\n   * Returns an array that represents the path from the top-level form to this control.\n   * Each index is the string name of the control on that level.\n   */\n  get path() {\n    return [];\n  }\n  /**\n   * @description\n   * The `FormControl` bound to this directive.\n   */\n  get control() {\n    return this.form;\n  }\n  /**\n   * @description\n   * Sets the new value for the view model and emits an `ngModelChange` event.\n   *\n   * @param newValue The new value for the view model.\n   */\n  viewToModelUpdate(newValue) {\n    this.viewModel = newValue;\n    this.update.emit(newValue);\n  }\n  _isControlChanged(changes) {\n    return changes.hasOwnProperty('form');\n  }\n  static ɵfac = function FormControlDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FormControlDirective)(i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_VALUE_ACCESSOR, 10), i0.ɵɵdirectiveInject(NG_MODEL_WITH_FORM_CONTROL_WARNING, 8), i0.ɵɵdirectiveInject(CALL_SET_DISABLED_STATE, 8));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FormControlDirective,\n    selectors: [[\"\", \"formControl\", \"\"]],\n    inputs: {\n      form: [0, \"formControl\", \"form\"],\n      isDisabled: [0, \"disabled\", \"isDisabled\"],\n      model: [0, \"ngModel\", \"model\"]\n    },\n    outputs: {\n      update: \"ngModelChange\"\n    },\n    exportAs: [\"ngForm\"],\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([formControlBinding]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormControlDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[formControl]',\n      providers: [formControlBinding],\n      exportAs: 'ngForm',\n      standalone: false\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_ASYNC_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_VALUE_ACCESSOR]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [NG_MODEL_WITH_FORM_CONTROL_WARNING]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CALL_SET_DISABLED_STATE]\n    }]\n  }], {\n    form: [{\n      type: Input,\n      args: ['formControl']\n    }],\n    isDisabled: [{\n      type: Input,\n      args: ['disabled']\n    }],\n    model: [{\n      type: Input,\n      args: ['ngModel']\n    }],\n    update: [{\n      type: Output,\n      args: ['ngModelChange']\n    }]\n  });\n})();\nconst formDirectiveProvider = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormGroupDirective)\n};\n/**\n * @description\n *\n * Binds an existing `FormGroup` or `FormRecord` to a DOM element.\n *\n * This directive accepts an existing `FormGroup` instance. It will then use this\n * `FormGroup` instance to match any child `FormControl`, `FormGroup`/`FormRecord`,\n * and `FormArray` instances to child `FormControlName`, `FormGroupName`,\n * and `FormArrayName` directives.\n *\n * @see [Reactive Forms Guide](guide/forms/reactive-forms)\n * @see {@link AbstractControl}\n *\n * @usageNotes\n * ### Register Form Group\n *\n * The following example registers a `FormGroup` with first name and last name controls,\n * and listens for the *ngSubmit* event when the button is clicked.\n *\n * {@example forms/ts/simpleFormGroup/simple_form_group_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\nclass FormGroupDirective extends ControlContainer {\n  callSetDisabledState;\n  /**\n   * @description\n   * Reports whether the form submission has been triggered.\n   */\n  get submitted() {\n    return untracked(this._submittedReactive);\n  }\n  // TODO(atscott): Remove once invalid API usage is cleaned up internally\n  set submitted(value) {\n    this._submittedReactive.set(value);\n  }\n  /** @internal */\n  _submitted = computed(() => this._submittedReactive(), ...(ngDevMode ? [{\n    debugName: \"_submitted\"\n  }] : []));\n  _submittedReactive = signal(false, ...(ngDevMode ? [{\n    debugName: \"_submittedReactive\"\n  }] : []));\n  /**\n   * Reference to an old form group input value, which is needed to cleanup\n   * old instance in case it was replaced with a new one.\n   */\n  _oldForm;\n  /**\n   * Callback that should be invoked when controls in FormGroup or FormArray collection change\n   * (added or removed). This callback triggers corresponding DOM updates.\n   */\n  _onCollectionChange = () => this._updateDomValue();\n  /**\n   * @description\n   * Tracks the list of added `FormControlName` instances\n   */\n  directives = [];\n  /**\n   * @description\n   * Tracks the `FormGroup` bound to this directive.\n   */\n  form = null;\n  /**\n   * @description\n   * Emits an event when the form submission has been triggered.\n   */\n  ngSubmit = new EventEmitter();\n  constructor(validators, asyncValidators, callSetDisabledState) {\n    super();\n    this.callSetDisabledState = callSetDisabledState;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n  }\n  /** @docs-private */\n  ngOnChanges(changes) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !this.form) {\n      throw missingFormException();\n    }\n    if (changes.hasOwnProperty('form')) {\n      this._updateValidators();\n      this._updateDomValue();\n      this._updateRegistrations();\n      this._oldForm = this.form;\n    }\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    if (this.form) {\n      cleanUpValidators(this.form, this);\n      // Currently the `onCollectionChange` callback is rewritten each time the\n      // `_registerOnCollectionChange` function is invoked. The implication is that cleanup should\n      // happen *only* when the `onCollectionChange` callback was set by this directive instance.\n      // Otherwise it might cause overriding a callback of some other directive instances. We should\n      // consider updating this logic later to make it similar to how `onChange` callbacks are\n      // handled, see https://github.com/angular/angular/issues/39732 for additional info.\n      if (this.form._onCollectionChange === this._onCollectionChange) {\n        this.form._registerOnCollectionChange(() => {});\n      }\n    }\n  }\n  /**\n   * @description\n   * Returns this directive's instance.\n   */\n  get formDirective() {\n    return this;\n  }\n  /**\n   * @description\n   * Returns the `FormGroup` bound to this directive.\n   */\n  get control() {\n    return this.form;\n  }\n  /**\n   * @description\n   * Returns an array representing the path to this group. Because this directive\n   * always lives at the top level of a form, it always an empty array.\n   */\n  get path() {\n    return [];\n  }\n  /**\n   * @description\n   * Method that sets up the control directive in this group, re-calculates its value\n   * and validity, and adds the instance to the internal list of directives.\n   *\n   * @param dir The `FormControlName` directive instance.\n   */\n  addControl(dir) {\n    const ctrl = this.form.get(dir.path);\n    setUpControl(ctrl, dir, this.callSetDisabledState);\n    ctrl.updateValueAndValidity({\n      emitEvent: false\n    });\n    this.directives.push(dir);\n    return ctrl;\n  }\n  /**\n   * @description\n   * Retrieves the `FormControl` instance from the provided `FormControlName` directive\n   *\n   * @param dir The `FormControlName` directive instance.\n   */\n  getControl(dir) {\n    return this.form.get(dir.path);\n  }\n  /**\n   * @description\n   * Removes the `FormControlName` instance from the internal list of directives\n   *\n   * @param dir The `FormControlName` directive instance.\n   */\n  removeControl(dir) {\n    cleanUpControl(dir.control || null, dir, /* validateControlPresenceOnChange */false);\n    removeListItem$1(this.directives, dir);\n  }\n  /**\n   * Adds a new `FormGroupName` directive instance to the form.\n   *\n   * @param dir The `FormGroupName` directive instance.\n   */\n  addFormGroup(dir) {\n    this._setUpFormContainer(dir);\n  }\n  /**\n   * Performs the necessary cleanup when a `FormGroupName` directive instance is removed from the\n   * view.\n   *\n   * @param dir The `FormGroupName` directive instance.\n   */\n  removeFormGroup(dir) {\n    this._cleanUpFormContainer(dir);\n  }\n  /**\n   * @description\n   * Retrieves the `FormGroup` for a provided `FormGroupName` directive instance\n   *\n   * @param dir The `FormGroupName` directive instance.\n   */\n  getFormGroup(dir) {\n    return this.form.get(dir.path);\n  }\n  /**\n   * Performs the necessary setup when a `FormArrayName` directive instance is added to the view.\n   *\n   * @param dir The `FormArrayName` directive instance.\n   */\n  addFormArray(dir) {\n    this._setUpFormContainer(dir);\n  }\n  /**\n   * Performs the necessary cleanup when a `FormArrayName` directive instance is removed from the\n   * view.\n   *\n   * @param dir The `FormArrayName` directive instance.\n   */\n  removeFormArray(dir) {\n    this._cleanUpFormContainer(dir);\n  }\n  /**\n   * @description\n   * Retrieves the `FormArray` for a provided `FormArrayName` directive instance.\n   *\n   * @param dir The `FormArrayName` directive instance.\n   */\n  getFormArray(dir) {\n    return this.form.get(dir.path);\n  }\n  /**\n   * Sets the new value for the provided `FormControlName` directive.\n   *\n   * @param dir The `FormControlName` directive instance.\n   * @param value The new value for the directive's control.\n   */\n  updateModel(dir, value) {\n    const ctrl = this.form.get(dir.path);\n    ctrl.setValue(value);\n  }\n  /**\n   * @description\n   * Method called with the \"submit\" event is triggered on the form.\n   * Triggers the `ngSubmit` emitter to emit the \"submit\" event as its payload.\n   *\n   * @param $event The \"submit\" event object\n   */\n  onSubmit($event) {\n    this._submittedReactive.set(true);\n    syncPendingControls(this.form, this.directives);\n    this.ngSubmit.emit($event);\n    this.form._events.next(new FormSubmittedEvent(this.control));\n    // Forms with `method=\"dialog\"` have some special behavior that won't reload the page and that\n    // shouldn't be prevented. Note that we need to null check the `event` and the `target`, because\n    // some internal apps call this method directly with the wrong arguments.\n    return $event?.target?.method === 'dialog';\n  }\n  /**\n   * @description\n   * Method called when the \"reset\" event is triggered on the form.\n   */\n  onReset() {\n    this.resetForm();\n  }\n  /**\n   * @description\n   * Resets the form to an initial value and resets its submitted status.\n   *\n   * @param value The new value for the form, `undefined` by default\n   */\n  resetForm(value = undefined, options = {}) {\n    this.form.reset(value, options);\n    this._submittedReactive.set(false);\n    if (options?.emitEvent !== false) {\n      this.form._events.next(new FormResetEvent(this.form));\n    }\n  }\n  /** @internal */\n  _updateDomValue() {\n    this.directives.forEach(dir => {\n      const oldCtrl = dir.control;\n      const newCtrl = this.form.get(dir.path);\n      if (oldCtrl !== newCtrl) {\n        // Note: the value of the `dir.control` may not be defined, for example when it's a first\n        // `FormControl` that is added to a `FormGroup` instance (via `addControl` call).\n        cleanUpControl(oldCtrl || null, dir);\n        // Check whether new control at the same location inside the corresponding `FormGroup` is an\n        // instance of `FormControl` and perform control setup only if that's the case.\n        // Note: we don't need to clear the list of directives (`this.directives`) here, it would be\n        // taken care of in the `removeControl` method invoked when corresponding `formControlName`\n        // directive instance is being removed (invoked from `FormControlName.ngOnDestroy`).\n        if (isFormControl(newCtrl)) {\n          setUpControl(newCtrl, dir, this.callSetDisabledState);\n          dir.control = newCtrl;\n        }\n      }\n    });\n    this.form._updateTreeValidity({\n      emitEvent: false\n    });\n  }\n  _setUpFormContainer(dir) {\n    const ctrl = this.form.get(dir.path);\n    setUpFormContainer(ctrl, dir);\n    // NOTE: this operation looks unnecessary in case no new validators were added in\n    // `setUpFormContainer` call. Consider updating this code to match the logic in\n    // `_cleanUpFormContainer` function.\n    ctrl.updateValueAndValidity({\n      emitEvent: false\n    });\n  }\n  _cleanUpFormContainer(dir) {\n    if (this.form) {\n      const ctrl = this.form.get(dir.path);\n      if (ctrl) {\n        const isControlUpdated = cleanUpFormContainer(ctrl, dir);\n        if (isControlUpdated) {\n          // Run validity check only in case a control was updated (i.e. view validators were\n          // removed) as removing view validators might cause validity to change.\n          ctrl.updateValueAndValidity({\n            emitEvent: false\n          });\n        }\n      }\n    }\n  }\n  _updateRegistrations() {\n    this.form._registerOnCollectionChange(this._onCollectionChange);\n    if (this._oldForm) {\n      this._oldForm._registerOnCollectionChange(() => {});\n    }\n  }\n  _updateValidators() {\n    setUpValidators(this.form, this);\n    if (this._oldForm) {\n      cleanUpValidators(this._oldForm, this);\n    }\n  }\n  static ɵfac = function FormGroupDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FormGroupDirective)(i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10), i0.ɵɵdirectiveInject(CALL_SET_DISABLED_STATE, 8));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FormGroupDirective,\n    selectors: [[\"\", \"formGroup\", \"\"]],\n    hostBindings: function FormGroupDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"submit\", function FormGroupDirective_submit_HostBindingHandler($event) {\n          return ctx.onSubmit($event);\n        })(\"reset\", function FormGroupDirective_reset_HostBindingHandler() {\n          return ctx.onReset();\n        });\n      }\n    },\n    inputs: {\n      form: [0, \"formGroup\", \"form\"]\n    },\n    outputs: {\n      ngSubmit: \"ngSubmit\"\n    },\n    exportAs: [\"ngForm\"],\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([formDirectiveProvider]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormGroupDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[formGroup]',\n      providers: [formDirectiveProvider],\n      host: {\n        '(submit)': 'onSubmit($event)',\n        '(reset)': 'onReset()'\n      },\n      exportAs: 'ngForm',\n      standalone: false\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_ASYNC_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CALL_SET_DISABLED_STATE]\n    }]\n  }], {\n    form: [{\n      type: Input,\n      args: ['formGroup']\n    }],\n    ngSubmit: [{\n      type: Output\n    }]\n  });\n})();\nconst formGroupNameProvider = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormGroupName)\n};\n/**\n * @description\n *\n * Syncs a nested `FormGroup` or `FormRecord` to a DOM element.\n *\n * This directive can only be used with a parent `FormGroupDirective`.\n *\n * It accepts the string name of the nested `FormGroup` or `FormRecord` to link, and\n * looks for a `FormGroup` or `FormRecord` registered with that name in the parent\n * `FormGroup` instance you passed into `FormGroupDirective`.\n *\n * Use nested form groups to validate a sub-group of a\n * form separately from the rest or to group the values of certain\n * controls into their own nested object.\n *\n * @see [Reactive Forms Guide](guide/forms/reactive-forms)\n *\n * @usageNotes\n *\n * ### Access the group by name\n *\n * The following example uses the `AbstractControl.get` method to access the\n * associated `FormGroup`\n *\n * ```ts\n *   this.form.get('name');\n * ```\n *\n * ### Access individual controls in the group\n *\n * The following example uses the `AbstractControl.get` method to access\n * individual controls within the group using dot syntax.\n *\n * ```ts\n *   this.form.get('name.first');\n * ```\n *\n * ### Register a nested `FormGroup`.\n *\n * The following example registers a nested *name* `FormGroup` within an existing `FormGroup`,\n * and provides methods to retrieve the nested `FormGroup` and individual controls.\n *\n * {@example forms/ts/nestedFormGroup/nested_form_group_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\nclass FormGroupName extends AbstractFormGroupDirective {\n  /**\n   * @description\n   * Tracks the name of the `FormGroup` bound to the directive. The name corresponds\n   * to a key in the parent `FormGroup` or `FormArray`.\n   * Accepts a name as a string or a number.\n   * The name in the form of a string is useful for individual forms,\n   * while the numerical form allows for form groups to be bound\n   * to indices when iterating over groups in a `FormArray`.\n   */\n  name = null;\n  constructor(parent, validators, asyncValidators) {\n    super();\n    this._parent = parent;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n  }\n  /** @internal */\n  _checkParentType() {\n    if (hasInvalidParent(this._parent) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw groupParentException();\n    }\n  }\n  static ɵfac = function FormGroupName_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FormGroupName)(i0.ɵɵdirectiveInject(ControlContainer, 13), i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FormGroupName,\n    selectors: [[\"\", \"formGroupName\", \"\"]],\n    inputs: {\n      name: [0, \"formGroupName\", \"name\"]\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([formGroupNameProvider]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormGroupName, [{\n    type: Directive,\n    args: [{\n      selector: '[formGroupName]',\n      providers: [formGroupNameProvider],\n      standalone: false\n    }]\n  }], () => [{\n    type: ControlContainer,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Host\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_ASYNC_VALIDATORS]\n    }]\n  }], {\n    name: [{\n      type: Input,\n      args: ['formGroupName']\n    }]\n  });\n})();\nconst formArrayNameProvider = {\n  provide: ControlContainer,\n  useExisting: forwardRef(() => FormArrayName)\n};\n/**\n * @description\n *\n * Syncs a nested `FormArray` to a DOM element.\n *\n * This directive is designed to be used with a parent `FormGroupDirective` (selector:\n * `[formGroup]`).\n *\n * It accepts the string name of the nested `FormArray` you want to link, and\n * will look for a `FormArray` registered with that name in the parent\n * `FormGroup` instance you passed into `FormGroupDirective`.\n *\n * @see [Reactive Forms Guide](guide/forms/reactive-forms)\n * @see {@link AbstractControl}\n *\n * @usageNotes\n *\n * ### Example\n *\n * {@example forms/ts/nestedFormArray/nested_form_array_example.ts region='Component'}\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\nclass FormArrayName extends ControlContainer {\n  /** @internal */\n  _parent;\n  /**\n   * @description\n   * Tracks the name of the `FormArray` bound to the directive. The name corresponds\n   * to a key in the parent `FormGroup` or `FormArray`.\n   * Accepts a name as a string or a number.\n   * The name in the form of a string is useful for individual forms,\n   * while the numerical form allows for form arrays to be bound\n   * to indices when iterating over arrays in a `FormArray`.\n   */\n  name = null;\n  constructor(parent, validators, asyncValidators) {\n    super();\n    this._parent = parent;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n  }\n  /**\n   * A lifecycle method called when the directive's inputs are initialized. For internal use only.\n   * @throws If the directive does not have a valid parent.\n   * @docs-private\n   */\n  ngOnInit() {\n    if (hasInvalidParent(this._parent) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw arrayParentException();\n    }\n    this.formDirective.addFormArray(this);\n  }\n  /**\n   * A lifecycle method called before the directive's instance is destroyed. For internal use only.\n   * @docs-private\n   */\n  ngOnDestroy() {\n    this.formDirective?.removeFormArray(this);\n  }\n  /**\n   * @description\n   * The `FormArray` bound to this directive.\n   */\n  get control() {\n    return this.formDirective.getFormArray(this);\n  }\n  /**\n   * @description\n   * The top-level directive for this group if present, otherwise null.\n   */\n  get formDirective() {\n    return this._parent ? this._parent.formDirective : null;\n  }\n  /**\n   * @description\n   * Returns an array that represents the path from the top-level form to this control.\n   * Each index is the string name of the control on that level.\n   */\n  get path() {\n    return controlPath(this.name == null ? this.name : this.name.toString(), this._parent);\n  }\n  static ɵfac = function FormArrayName_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FormArrayName)(i0.ɵɵdirectiveInject(ControlContainer, 13), i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FormArrayName,\n    selectors: [[\"\", \"formArrayName\", \"\"]],\n    inputs: {\n      name: [0, \"formArrayName\", \"name\"]\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([formArrayNameProvider]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormArrayName, [{\n    type: Directive,\n    args: [{\n      selector: '[formArrayName]',\n      providers: [formArrayNameProvider],\n      standalone: false\n    }]\n  }], () => [{\n    type: ControlContainer,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Host\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_ASYNC_VALIDATORS]\n    }]\n  }], {\n    name: [{\n      type: Input,\n      args: ['formArrayName']\n    }]\n  });\n})();\nfunction hasInvalidParent(parent) {\n  return !(parent instanceof FormGroupName) && !(parent instanceof FormGroupDirective) && !(parent instanceof FormArrayName);\n}\nconst controlNameBinding = {\n  provide: NgControl,\n  useExisting: forwardRef(() => FormControlName)\n};\n/**\n * @description\n * Syncs a `FormControl` in an existing `FormGroup` to a form control\n * element by name.\n *\n * @see [Reactive Forms Guide](guide/forms/reactive-forms)\n * @see {@link FormControl}\n * @see {@link AbstractControl}\n *\n * @usageNotes\n *\n * ### Register `FormControl` within a group\n *\n * The following example shows how to register multiple form controls within a form group\n * and set their value.\n *\n * {@example forms/ts/simpleFormGroup/simple_form_group_example.ts region='Component'}\n *\n * To see `formControlName` examples with different form control types, see:\n *\n * * Radio buttons: `RadioControlValueAccessor`\n * * Selects: `SelectControlValueAccessor`\n *\n * ### Use with ngModel is deprecated\n *\n * Support for using the `ngModel` input property and `ngModelChange` event with reactive\n * form directives has been deprecated in Angular v6 and is scheduled for removal in\n * a future version of Angular.\n *\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\nclass FormControlName extends NgControl {\n  _ngModelWarningConfig;\n  _added = false;\n  /**\n   * Internal reference to the view model value.\n   * @internal\n   */\n  viewModel;\n  /**\n   * @description\n   * Tracks the `FormControl` instance bound to the directive.\n   */\n  control;\n  /**\n   * @description\n   * Tracks the name of the `FormControl` bound to the directive. The name corresponds\n   * to a key in the parent `FormGroup` or `FormArray`.\n   * Accepts a name as a string or a number.\n   * The name in the form of a string is useful for individual forms,\n   * while the numerical form allows for form controls to be bound\n   * to indices when iterating over controls in a `FormArray`.\n   */\n  name = null;\n  /**\n   * @description\n   * Triggers a warning in dev mode that this input should not be used with reactive forms.\n   */\n  set isDisabled(isDisabled) {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      console.warn(disabledAttrWarning);\n    }\n  }\n  // TODO(kara): remove next 4 properties once deprecation period is over\n  /** @deprecated as of v6 */\n  model;\n  /** @deprecated as of v6 */\n  update = new EventEmitter();\n  /**\n   * @description\n   * Static property used to track whether any ngModel warnings have been sent across\n   * all instances of FormControlName. Used to support warning config of \"once\".\n   *\n   * @internal\n   */\n  static _ngModelWarningSentOnce = false;\n  /**\n   * @description\n   * Instance property used to track whether an ngModel warning has been sent out for this\n   * particular FormControlName instance. Used to support warning config of \"always\".\n   *\n   * @internal\n   */\n  _ngModelWarningSent = false;\n  constructor(parent, validators, asyncValidators, valueAccessors, _ngModelWarningConfig) {\n    super();\n    this._ngModelWarningConfig = _ngModelWarningConfig;\n    this._parent = parent;\n    this._setValidators(validators);\n    this._setAsyncValidators(asyncValidators);\n    this.valueAccessor = selectValueAccessor(this, valueAccessors);\n  }\n  /** @docs-private */\n  ngOnChanges(changes) {\n    if (!this._added) this._setUpControl();\n    if (isPropertyUpdated(changes, this.viewModel)) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        _ngModelWarning('formControlName', FormControlName, this, this._ngModelWarningConfig);\n      }\n      this.viewModel = this.model;\n      this.formDirective.updateModel(this, this.model);\n    }\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    if (this.formDirective) {\n      this.formDirective.removeControl(this);\n    }\n  }\n  /**\n   * @description\n   * Sets the new value for the view model and emits an `ngModelChange` event.\n   *\n   * @param newValue The new value for the view model.\n   */\n  viewToModelUpdate(newValue) {\n    this.viewModel = newValue;\n    this.update.emit(newValue);\n  }\n  /**\n   * @description\n   * Returns an array that represents the path from the top-level form to this control.\n   * Each index is the string name of the control on that level.\n   */\n  get path() {\n    return controlPath(this.name == null ? this.name : this.name.toString(), this._parent);\n  }\n  /**\n   * @description\n   * The top-level directive for this group if present, otherwise null.\n   */\n  get formDirective() {\n    return this._parent ? this._parent.formDirective : null;\n  }\n  _setUpControl() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      checkParentType(this._parent, this.name);\n    }\n    this.control = this.formDirective.addControl(this);\n    this._added = true;\n  }\n  static ɵfac = function FormControlName_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FormControlName)(i0.ɵɵdirectiveInject(ControlContainer, 13), i0.ɵɵdirectiveInject(NG_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_ASYNC_VALIDATORS, 10), i0.ɵɵdirectiveInject(NG_VALUE_ACCESSOR, 10), i0.ɵɵdirectiveInject(NG_MODEL_WITH_FORM_CONTROL_WARNING, 8));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: FormControlName,\n    selectors: [[\"\", \"formControlName\", \"\"]],\n    inputs: {\n      name: [0, \"formControlName\", \"name\"],\n      isDisabled: [0, \"disabled\", \"isDisabled\"],\n      model: [0, \"ngModel\", \"model\"]\n    },\n    outputs: {\n      update: \"ngModelChange\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([controlNameBinding]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormControlName, [{\n    type: Directive,\n    args: [{\n      selector: '[formControlName]',\n      providers: [controlNameBinding],\n      standalone: false\n    }]\n  }], () => [{\n    type: ControlContainer,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Host\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_ASYNC_VALIDATORS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [NG_VALUE_ACCESSOR]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [NG_MODEL_WITH_FORM_CONTROL_WARNING]\n    }]\n  }], {\n    name: [{\n      type: Input,\n      args: ['formControlName']\n    }],\n    isDisabled: [{\n      type: Input,\n      args: ['disabled']\n    }],\n    model: [{\n      type: Input,\n      args: ['ngModel']\n    }],\n    update: [{\n      type: Output,\n      args: ['ngModelChange']\n    }]\n  });\n})();\nfunction checkParentType(parent, name) {\n  if (!(parent instanceof FormGroupName) && parent instanceof AbstractFormGroupDirective) {\n    throw ngModelGroupException();\n  } else if (!(parent instanceof FormGroupName) && !(parent instanceof FormGroupDirective) && !(parent instanceof FormArrayName)) {\n    throw controlParentException(name);\n  }\n}\nconst SELECT_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => SelectControlValueAccessor),\n  multi: true\n};\nfunction _buildValueString$1(id, value) {\n  if (id == null) return `${value}`;\n  if (value && typeof value === 'object') value = 'Object';\n  return `${id}: ${value}`.slice(0, 50);\n}\nfunction _extractId$1(valueString) {\n  return valueString.split(':')[0];\n}\n/**\n * @description\n * The `ControlValueAccessor` for writing select control values and listening to select control\n * changes. The value accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n * @usageNotes\n *\n * ### Using select controls in a reactive form\n *\n * The following examples show how to use a select control in a reactive form.\n *\n * {@example forms/ts/reactiveSelectControl/reactive_select_control_example.ts region='Component'}\n *\n * ### Using select controls in a template-driven form\n *\n * To use a select in a template-driven form, simply add an `ngModel` and a `name`\n * attribute to the main `<select>` tag.\n *\n * {@example forms/ts/selectControl/select_control_example.ts region='Component'}\n *\n * ### Customizing option selection\n *\n * Angular uses object identity to select option. It's possible for the identities of items\n * to change while the data does not. This can happen, for example, if the items are produced\n * from an RPC to the server, and that RPC is re-run. Even if the data hasn't changed, the\n * second response will produce objects with different identities.\n *\n * To customize the default option comparison algorithm, `<select>` supports `compareWith` input.\n * `compareWith` takes a **function** which has two arguments: `option1` and `option2`.\n * If `compareWith` is given, Angular selects option by the return value of the function.\n *\n * ```ts\n * const selectedCountriesControl = new FormControl();\n * ```\n *\n * ```html\n * <select [compareWith]=\"compareFn\"  [formControl]=\"selectedCountriesControl\">\n *    @for(country of countries; track $index) {\n *        <option[ngValue]=\"country\">{{country.name}}</option>\n *    }\n * </select>\n *\n * compareFn(c1: Country, c2: Country): boolean {\n *     return c1 && c2 ? c1.id === c2.id : c1 === c2;\n * }\n * ```\n *\n * **Note:** We listen to the 'change' event because 'input' events aren't fired\n * for selects in IE, see:\n * https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/input_event#browser_compatibility\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass SelectControlValueAccessor extends BuiltInControlValueAccessor {\n  /** @docs-private */\n  value;\n  /** @internal */\n  _optionMap = new Map();\n  /** @internal */\n  _idCounter = 0;\n  /**\n   * @description\n   * Tracks the option comparison algorithm for tracking identities when\n   * checking for changes.\n   */\n  set compareWith(fn) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new _RuntimeError(1201 /* RuntimeErrorCode.COMPAREWITH_NOT_A_FN */, `compareWith must be a function, but received ${JSON.stringify(fn)}`);\n    }\n    this._compareWith = fn;\n  }\n  _compareWith = Object.is;\n  // We need this because we might be in the process of destroying the root\n  // injector, which is marked as destroyed before running destroy hooks.\n  // Attempting to use afterNextRender with the node injector would evntually\n  // run into that already destroyed injector.\n  appRefInjector = inject(ApplicationRef).injector;\n  destroyRef = inject(DestroyRef);\n  cdr = inject(ChangeDetectorRef);\n  _queuedWrite = false;\n  /**\n   * This is needed to efficiently set the select value when adding/removing options. If\n   * writeValue is instead called for every added/removed option, this results in exponentially\n   * more _compareValue calls than the number of option elements (issue #41330).\n   *\n   * Secondly, calling writeValue when rendering individual option elements instead of after they\n   * are all rendered caused an issue in Safari and IE 11 where the first option element failed\n   * to be deselected when no option matched the select ngModel. This was because Angular would\n   * set the select element's value property before appending the option's child text node to the\n   * DOM (issue #14505).\n   *\n   * Finally, this approach is necessary to avoid an issue with delayed element removal when\n   * using the animations module (in all browsers). Otherwise when a selected option is removed\n   * (so no option matches the ngModel anymore), Angular would change the select element value\n   * before actually removing the option from the DOM. Then when the option is finally removed\n   * from the DOM, the browser would change the select value to that of the first option, even\n   * though it doesn't match the ngModel (issue #18430).\n   *\n   * @internal\n   */\n  _writeValueAfterRender() {\n    if (this._queuedWrite || this.appRefInjector.destroyed) {\n      return;\n    }\n    this._queuedWrite = true;\n    afterNextRender({\n      write: () => {\n        if (this.destroyRef.destroyed) {\n          return;\n        }\n        this._queuedWrite = false;\n        this.writeValue(this.value);\n      }\n    }, {\n      injector: this.appRefInjector\n    });\n  }\n  /**\n   * Sets the \"value\" property on the select element.\n   * @docs-private\n   */\n  writeValue(value) {\n    // TODO(atscott): This could likely be optimized more by only marking for check if the value is changed\n    // note that this needs to include both the internal value and the value in the DOM.\n    this.cdr.markForCheck();\n    this.value = value;\n    const id = this._getOptionId(value);\n    const valueString = _buildValueString$1(id, value);\n    this.setProperty('value', valueString);\n  }\n  /**\n   * Registers a function called when the control value changes.\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this.onChange = valueString => {\n      this.value = this._getOptionValue(valueString);\n      fn(this.value);\n    };\n  }\n  /** @internal */\n  _registerOption() {\n    return (this._idCounter++).toString();\n  }\n  /** @internal */\n  _getOptionId(value) {\n    for (const id of this._optionMap.keys()) {\n      if (this._compareWith(this._optionMap.get(id), value)) return id;\n    }\n    return null;\n  }\n  /** @internal */\n  _getOptionValue(valueString) {\n    const id = _extractId$1(valueString);\n    return this._optionMap.has(id) ? this._optionMap.get(id) : valueString;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵSelectControlValueAccessor_BaseFactory;\n    return function SelectControlValueAccessor_Factory(__ngFactoryType__) {\n      return (ɵSelectControlValueAccessor_BaseFactory || (ɵSelectControlValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(SelectControlValueAccessor)))(__ngFactoryType__ || SelectControlValueAccessor);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: SelectControlValueAccessor,\n    selectors: [[\"select\", \"formControlName\", \"\", 3, \"multiple\", \"\"], [\"select\", \"formControl\", \"\", 3, \"multiple\", \"\"], [\"select\", \"ngModel\", \"\", 3, \"multiple\", \"\"]],\n    hostBindings: function SelectControlValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function SelectControlValueAccessor_change_HostBindingHandler($event) {\n          return ctx.onChange($event.target.value);\n        })(\"blur\", function SelectControlValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    inputs: {\n      compareWith: \"compareWith\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([SELECT_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SelectControlValueAccessor, [{\n    type: Directive,\n    args: [{\n      selector: 'select:not([multiple])[formControlName],select:not([multiple])[formControl],select:not([multiple])[ngModel]',\n      host: {\n        '(change)': 'onChange($any($event.target).value)',\n        '(blur)': 'onTouched()'\n      },\n      providers: [SELECT_VALUE_ACCESSOR],\n      standalone: false\n    }]\n  }], null, {\n    compareWith: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @description\n * Marks `<option>` as dynamic, so Angular can be notified when options change.\n *\n * @see {@link SelectControlValueAccessor}\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass NgSelectOption {\n  _element;\n  _renderer;\n  _select;\n  /**\n   * @description\n   * ID of the option element\n   */\n  id;\n  constructor(_element, _renderer, _select) {\n    this._element = _element;\n    this._renderer = _renderer;\n    this._select = _select;\n    if (this._select) this.id = this._select._registerOption();\n  }\n  /**\n   * @description\n   * Tracks the value bound to the option element. Unlike the value binding,\n   * ngValue supports binding to objects.\n   */\n  set ngValue(value) {\n    if (this._select == null) return;\n    this._select._optionMap.set(this.id, value);\n    this._setElementValue(_buildValueString$1(this.id, value));\n    this._select._writeValueAfterRender();\n  }\n  /**\n   * @description\n   * Tracks simple string values bound to the option element.\n   * For objects, use the `ngValue` input binding.\n   */\n  set value(value) {\n    this._setElementValue(value);\n    if (this._select) this._select._writeValueAfterRender();\n  }\n  /** @internal */\n  _setElementValue(value) {\n    this._renderer.setProperty(this._element.nativeElement, 'value', value);\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    if (this._select) {\n      this._select._optionMap.delete(this.id);\n      this._select._writeValueAfterRender();\n    }\n  }\n  static ɵfac = function NgSelectOption_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgSelectOption)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(SelectControlValueAccessor, 9));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgSelectOption,\n    selectors: [[\"option\"]],\n    inputs: {\n      ngValue: \"ngValue\",\n      value: \"value\"\n    },\n    standalone: false\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgSelectOption, [{\n    type: Directive,\n    args: [{\n      selector: 'option',\n      standalone: false\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: SelectControlValueAccessor,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Host\n    }]\n  }], {\n    ngValue: [{\n      type: Input,\n      args: ['ngValue']\n    }],\n    value: [{\n      type: Input,\n      args: ['value']\n    }]\n  });\n})();\nconst SELECT_MULTIPLE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => SelectMultipleControlValueAccessor),\n  multi: true\n};\nfunction _buildValueString(id, value) {\n  if (id == null) return `${value}`;\n  if (typeof value === 'string') value = `'${value}'`;\n  if (value && typeof value === 'object') value = 'Object';\n  return `${id}: ${value}`.slice(0, 50);\n}\nfunction _extractId(valueString) {\n  return valueString.split(':')[0];\n}\n/**\n * @description\n * The `ControlValueAccessor` for writing multi-select control values and listening to multi-select\n * control changes. The value accessor is used by the `FormControlDirective`, `FormControlName`, and\n * `NgModel` directives.\n *\n * @see {@link SelectControlValueAccessor}\n *\n * @usageNotes\n *\n * ### Using a multi-select control\n *\n * The follow example shows you how to use a multi-select control with a reactive form.\n *\n * ```ts\n * const countryControl = new FormControl();\n * ```\n *\n * ```html\n * <select multiple name=\"countries\" [formControl]=\"countryControl\">\n *   @for(country of countries; track $index) {\n *      <option [ngValue]=\"country\">{{ country.name }}</option>\n *   }\n * </select>\n * ```\n *\n * ### Customizing option selection\n *\n * To customize the default option comparison algorithm, `<select>` supports `compareWith` input.\n * See the `SelectControlValueAccessor` for usage.\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass SelectMultipleControlValueAccessor extends BuiltInControlValueAccessor {\n  /**\n   * The current value.\n   * @docs-private\n   */\n  value;\n  /** @internal */\n  _optionMap = new Map();\n  /** @internal */\n  _idCounter = 0;\n  /**\n   * @description\n   * Tracks the option comparison algorithm for tracking identities when\n   * checking for changes.\n   */\n  set compareWith(fn) {\n    if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw new _RuntimeError(1201 /* RuntimeErrorCode.COMPAREWITH_NOT_A_FN */, `compareWith must be a function, but received ${JSON.stringify(fn)}`);\n    }\n    this._compareWith = fn;\n  }\n  _compareWith = Object.is;\n  /**\n   * Sets the \"value\" property on one or of more of the select's options.\n   * @docs-private\n   */\n  writeValue(value) {\n    this.value = value;\n    let optionSelectedStateSetter;\n    if (Array.isArray(value)) {\n      // convert values to ids\n      const ids = value.map(v => this._getOptionId(v));\n      optionSelectedStateSetter = (opt, o) => {\n        opt._setSelected(ids.indexOf(o.toString()) > -1);\n      };\n    } else {\n      optionSelectedStateSetter = (opt, o) => {\n        opt._setSelected(false);\n      };\n    }\n    this._optionMap.forEach(optionSelectedStateSetter);\n  }\n  /**\n   * Registers a function called when the control value changes\n   * and writes an array of the selected options.\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this.onChange = element => {\n      const selected = [];\n      const selectedOptions = element.selectedOptions;\n      if (selectedOptions !== undefined) {\n        const options = selectedOptions;\n        for (let i = 0; i < options.length; i++) {\n          const opt = options[i];\n          const val = this._getOptionValue(opt.value);\n          selected.push(val);\n        }\n      }\n      // Degrade to use `options` when `selectedOptions` property is not available.\n      // Note: the `selectedOptions` is available in all supported browsers, but the Domino lib\n      // doesn't have it currently, see https://github.com/fgnass/domino/issues/177.\n      else {\n        const options = element.options;\n        for (let i = 0; i < options.length; i++) {\n          const opt = options[i];\n          if (opt.selected) {\n            const val = this._getOptionValue(opt.value);\n            selected.push(val);\n          }\n        }\n      }\n      this.value = selected;\n      fn(selected);\n    };\n  }\n  /** @internal */\n  _registerOption(value) {\n    const id = (this._idCounter++).toString();\n    this._optionMap.set(id, value);\n    return id;\n  }\n  /** @internal */\n  _getOptionId(value) {\n    for (const id of this._optionMap.keys()) {\n      if (this._compareWith(this._optionMap.get(id)._value, value)) return id;\n    }\n    return null;\n  }\n  /** @internal */\n  _getOptionValue(valueString) {\n    const id = _extractId(valueString);\n    return this._optionMap.has(id) ? this._optionMap.get(id)._value : valueString;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵSelectMultipleControlValueAccessor_BaseFactory;\n    return function SelectMultipleControlValueAccessor_Factory(__ngFactoryType__) {\n      return (ɵSelectMultipleControlValueAccessor_BaseFactory || (ɵSelectMultipleControlValueAccessor_BaseFactory = i0.ɵɵgetInheritedFactory(SelectMultipleControlValueAccessor)))(__ngFactoryType__ || SelectMultipleControlValueAccessor);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: SelectMultipleControlValueAccessor,\n    selectors: [[\"select\", \"multiple\", \"\", \"formControlName\", \"\"], [\"select\", \"multiple\", \"\", \"formControl\", \"\"], [\"select\", \"multiple\", \"\", \"ngModel\", \"\"]],\n    hostBindings: function SelectMultipleControlValueAccessor_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"change\", function SelectMultipleControlValueAccessor_change_HostBindingHandler($event) {\n          return ctx.onChange($event.target);\n        })(\"blur\", function SelectMultipleControlValueAccessor_blur_HostBindingHandler() {\n          return ctx.onTouched();\n        });\n      }\n    },\n    inputs: {\n      compareWith: \"compareWith\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([SELECT_MULTIPLE_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SelectMultipleControlValueAccessor, [{\n    type: Directive,\n    args: [{\n      selector: 'select[multiple][formControlName],select[multiple][formControl],select[multiple][ngModel]',\n      host: {\n        '(change)': 'onChange($event.target)',\n        '(blur)': 'onTouched()'\n      },\n      providers: [SELECT_MULTIPLE_VALUE_ACCESSOR],\n      standalone: false\n    }]\n  }], null, {\n    compareWith: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @description\n * Marks `<option>` as dynamic, so Angular can be notified when options change.\n *\n * @see {@link SelectMultipleControlValueAccessor}\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass ɵNgSelectMultipleOption {\n  _element;\n  _renderer;\n  _select;\n  id;\n  /** @internal */\n  _value;\n  constructor(_element, _renderer, _select) {\n    this._element = _element;\n    this._renderer = _renderer;\n    this._select = _select;\n    if (this._select) {\n      this.id = this._select._registerOption(this);\n    }\n  }\n  /**\n   * @description\n   * Tracks the value bound to the option element. Unlike the value binding,\n   * ngValue supports binding to objects.\n   */\n  set ngValue(value) {\n    if (this._select == null) return;\n    this._value = value;\n    this._setElementValue(_buildValueString(this.id, value));\n    this._select.writeValue(this._select.value);\n  }\n  /**\n   * @description\n   * Tracks simple string values bound to the option element.\n   * For objects, use the `ngValue` input binding.\n   */\n  set value(value) {\n    if (this._select) {\n      this._value = value;\n      this._setElementValue(_buildValueString(this.id, value));\n      this._select.writeValue(this._select.value);\n    } else {\n      this._setElementValue(value);\n    }\n  }\n  /** @internal */\n  _setElementValue(value) {\n    this._renderer.setProperty(this._element.nativeElement, 'value', value);\n  }\n  /** @internal */\n  _setSelected(selected) {\n    this._renderer.setProperty(this._element.nativeElement, 'selected', selected);\n  }\n  /** @docs-private */\n  ngOnDestroy() {\n    if (this._select) {\n      this._select._optionMap.delete(this.id);\n      this._select.writeValue(this._select.value);\n    }\n  }\n  static ɵfac = function ɵNgSelectMultipleOption_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ɵNgSelectMultipleOption)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(SelectMultipleControlValueAccessor, 9));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: ɵNgSelectMultipleOption,\n    selectors: [[\"option\"]],\n    inputs: {\n      ngValue: \"ngValue\",\n      value: \"value\"\n    },\n    standalone: false\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ɵNgSelectMultipleOption, [{\n    type: Directive,\n    args: [{\n      selector: 'option',\n      standalone: false\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: SelectMultipleControlValueAccessor,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Host\n    }]\n  }], {\n    ngValue: [{\n      type: Input,\n      args: ['ngValue']\n    }],\n    value: [{\n      type: Input,\n      args: ['value']\n    }]\n  });\n})();\n\n/**\n * Method that updates string to integer if not already a number\n *\n * @param value The value to convert to integer.\n * @returns value of parameter converted to number or integer.\n */\nfunction toInteger(value) {\n  return typeof value === 'number' ? value : parseInt(value, 10);\n}\n/**\n * Method that ensures that provided value is a float (and converts it to float if needed).\n *\n * @param value The value to convert to float.\n * @returns value of parameter converted to number or float.\n */\nfunction toFloat(value) {\n  return typeof value === 'number' ? value : parseFloat(value);\n}\n/**\n * A base class for Validator-based Directives. The class contains common logic shared across such\n * Directives.\n *\n * For internal use only, this class is not intended for use outside of the Forms package.\n */\nclass AbstractValidatorDirective {\n  _validator = nullValidator;\n  _onChange;\n  /**\n   * A flag that tracks whether this validator is enabled.\n   *\n   * Marking it `internal` (vs `protected`), so that this flag can be used in host bindings of\n   * directive classes that extend this base class.\n   * @internal\n   */\n  _enabled;\n  /** @docs-private */\n  ngOnChanges(changes) {\n    if (this.inputName in changes) {\n      const input = this.normalizeInput(changes[this.inputName].currentValue);\n      this._enabled = this.enabled(input);\n      this._validator = this._enabled ? this.createValidator(input) : nullValidator;\n      if (this._onChange) {\n        this._onChange();\n      }\n    }\n  }\n  /** @docs-private */\n  validate(control) {\n    return this._validator(control);\n  }\n  /** @docs-private */\n  registerOnValidatorChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * @description\n   * Determines whether this validator should be active or not based on an input.\n   * Base class implementation checks whether an input is defined (if the value is different from\n   * `null` and `undefined`). Validator classes that extend this base class can override this\n   * function with the logic specific to a particular validator directive.\n   */\n  enabled(input) {\n    return input != null /* both `null` and `undefined` */;\n  }\n  static ɵfac = function AbstractValidatorDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AbstractValidatorDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: AbstractValidatorDirective,\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AbstractValidatorDirective, [{\n    type: Directive\n  }], null, null);\n})();\n/**\n * @description\n * Provider which adds `MaxValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nconst MAX_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MaxValidator),\n  multi: true\n};\n/**\n * A directive which installs the {@link MaxValidator} for any `formControlName`,\n * `formControl`, or control with `ngModel` that also has a `max` attribute.\n *\n * @see [Form Validation](guide/forms/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a max validator\n *\n * The following example shows how to add a max validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input type=\"number\" ngModel max=\"4\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass MaxValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the max bound to this directive.\n   */\n  max;\n  /** @internal */\n  inputName = 'max';\n  /** @internal */\n  normalizeInput = input => toFloat(input);\n  /** @internal */\n  createValidator = max => maxValidator(max);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMaxValidator_BaseFactory;\n    return function MaxValidator_Factory(__ngFactoryType__) {\n      return (ɵMaxValidator_BaseFactory || (ɵMaxValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MaxValidator)))(__ngFactoryType__ || MaxValidator);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MaxValidator,\n    selectors: [[\"input\", \"type\", \"number\", \"max\", \"\", \"formControlName\", \"\"], [\"input\", \"type\", \"number\", \"max\", \"\", \"formControl\", \"\"], [\"input\", \"type\", \"number\", \"max\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function MaxValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"max\", ctx._enabled ? ctx.max : null);\n      }\n    },\n    inputs: {\n      max: \"max\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([MAX_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MaxValidator, [{\n    type: Directive,\n    args: [{\n      selector: 'input[type=number][max][formControlName],input[type=number][max][formControl],input[type=number][max][ngModel]',\n      providers: [MAX_VALIDATOR],\n      host: {\n        '[attr.max]': '_enabled ? max : null'\n      },\n      standalone: false\n    }]\n  }], null, {\n    max: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @description\n * Provider which adds `MinValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nconst MIN_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MinValidator),\n  multi: true\n};\n/**\n * A directive which installs the {@link MinValidator} for any `formControlName`,\n * `formControl`, or control with `ngModel` that also has a `min` attribute.\n *\n * @see [Form Validation](guide/forms/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a min validator\n *\n * The following example shows how to add a min validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input type=\"number\" ngModel min=\"4\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass MinValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the min bound to this directive.\n   */\n  min;\n  /** @internal */\n  inputName = 'min';\n  /** @internal */\n  normalizeInput = input => toFloat(input);\n  /** @internal */\n  createValidator = min => minValidator(min);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMinValidator_BaseFactory;\n    return function MinValidator_Factory(__ngFactoryType__) {\n      return (ɵMinValidator_BaseFactory || (ɵMinValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MinValidator)))(__ngFactoryType__ || MinValidator);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MinValidator,\n    selectors: [[\"input\", \"type\", \"number\", \"min\", \"\", \"formControlName\", \"\"], [\"input\", \"type\", \"number\", \"min\", \"\", \"formControl\", \"\"], [\"input\", \"type\", \"number\", \"min\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function MinValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"min\", ctx._enabled ? ctx.min : null);\n      }\n    },\n    inputs: {\n      min: \"min\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([MIN_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MinValidator, [{\n    type: Directive,\n    args: [{\n      selector: 'input[type=number][min][formControlName],input[type=number][min][formControl],input[type=number][min][ngModel]',\n      providers: [MIN_VALIDATOR],\n      host: {\n        '[attr.min]': '_enabled ? min : null'\n      },\n      standalone: false\n    }]\n  }], null, {\n    min: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @description\n * Provider which adds `RequiredValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nconst REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => RequiredValidator),\n  multi: true\n};\n/**\n * @description\n * Provider which adds `CheckboxRequiredValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nconst CHECKBOX_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => CheckboxRequiredValidator),\n  multi: true\n};\n/**\n * @description\n * A directive that adds the `required` validator to any controls marked with the\n * `required` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/forms/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a required validator using template-driven forms\n *\n * ```html\n * <input name=\"fullName\" ngModel required>\n * ```\n *\n * @ngModule FormsModule\n * @ngModule ReactiveFormsModule\n * @publicApi\n */\nclass RequiredValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the required attribute bound to this directive.\n   */\n  required;\n  /** @internal */\n  inputName = 'required';\n  /** @internal */\n  normalizeInput = booleanAttribute;\n  /** @internal */\n  createValidator = input => requiredValidator;\n  /** @docs-private */\n  enabled(input) {\n    return input;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵRequiredValidator_BaseFactory;\n    return function RequiredValidator_Factory(__ngFactoryType__) {\n      return (ɵRequiredValidator_BaseFactory || (ɵRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(RequiredValidator)))(__ngFactoryType__ || RequiredValidator);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RequiredValidator,\n    selectors: [[\"\", \"required\", \"\", \"formControlName\", \"\", 3, \"type\", \"checkbox\"], [\"\", \"required\", \"\", \"formControl\", \"\", 3, \"type\", \"checkbox\"], [\"\", \"required\", \"\", \"ngModel\", \"\", 3, \"type\", \"checkbox\"]],\n    hostVars: 1,\n    hostBindings: function RequiredValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"required\", ctx._enabled ? \"\" : null);\n      }\n    },\n    inputs: {\n      required: \"required\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RequiredValidator, [{\n    type: Directive,\n    args: [{\n      selector: ':not([type=checkbox])[required][formControlName],:not([type=checkbox])[required][formControl],:not([type=checkbox])[required][ngModel]',\n      providers: [REQUIRED_VALIDATOR],\n      host: {\n        '[attr.required]': '_enabled ? \"\" : null'\n      },\n      standalone: false\n    }]\n  }], null, {\n    required: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * A Directive that adds the `required` validator to checkbox controls marked with the\n * `required` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/forms/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a required checkbox validator using template-driven forms\n *\n * The following example shows how to add a checkbox required validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input type=\"checkbox\" name=\"active\" ngModel required>\n * ```\n *\n * @publicApi\n * @ngModule FormsModule\n * @ngModule ReactiveFormsModule\n */\nclass CheckboxRequiredValidator extends RequiredValidator {\n  /** @internal */\n  createValidator = input => requiredTrueValidator;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵCheckboxRequiredValidator_BaseFactory;\n    return function CheckboxRequiredValidator_Factory(__ngFactoryType__) {\n      return (ɵCheckboxRequiredValidator_BaseFactory || (ɵCheckboxRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(CheckboxRequiredValidator)))(__ngFactoryType__ || CheckboxRequiredValidator);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CheckboxRequiredValidator,\n    selectors: [[\"input\", \"type\", \"checkbox\", \"required\", \"\", \"formControlName\", \"\"], [\"input\", \"type\", \"checkbox\", \"required\", \"\", \"formControl\", \"\"], [\"input\", \"type\", \"checkbox\", \"required\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function CheckboxRequiredValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"required\", ctx._enabled ? \"\" : null);\n      }\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([CHECKBOX_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CheckboxRequiredValidator, [{\n    type: Directive,\n    args: [{\n      selector: 'input[type=checkbox][required][formControlName],input[type=checkbox][required][formControl],input[type=checkbox][required][ngModel]',\n      providers: [CHECKBOX_REQUIRED_VALIDATOR],\n      host: {\n        '[attr.required]': '_enabled ? \"\" : null'\n      },\n      standalone: false\n    }]\n  }], null, null);\n})();\n/**\n * @description\n * Provider which adds `EmailValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nconst EMAIL_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => EmailValidator),\n  multi: true\n};\n/**\n * A directive that adds the `email` validator to controls marked with the\n * `email` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * The email validation is based on the WHATWG HTML specification with some enhancements to\n * incorporate more RFC rules. More information can be found on the [Validators.email\n * page](api/forms/Validators#email).\n *\n * @see [Form Validation](guide/forms/form-validation)\n *\n * @usageNotes\n *\n * ### Adding an email validator\n *\n * The following example shows how to add an email validator to an input attached to an ngModel\n * binding.\n *\n * ```html\n * <input type=\"email\" name=\"email\" ngModel email>\n * <input type=\"email\" name=\"email\" ngModel email=\"true\">\n * <input type=\"email\" name=\"email\" ngModel [email]=\"true\">\n * ```\n *\n * @publicApi\n * @ngModule FormsModule\n * @ngModule ReactiveFormsModule\n */\nclass EmailValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the email attribute bound to this directive.\n   */\n  email;\n  /** @internal */\n  inputName = 'email';\n  /** @internal */\n  normalizeInput = booleanAttribute;\n  /** @internal */\n  createValidator = input => emailValidator;\n  /** @docs-private */\n  enabled(input) {\n    return input;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵEmailValidator_BaseFactory;\n    return function EmailValidator_Factory(__ngFactoryType__) {\n      return (ɵEmailValidator_BaseFactory || (ɵEmailValidator_BaseFactory = i0.ɵɵgetInheritedFactory(EmailValidator)))(__ngFactoryType__ || EmailValidator);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: EmailValidator,\n    selectors: [[\"\", \"email\", \"\", \"formControlName\", \"\"], [\"\", \"email\", \"\", \"formControl\", \"\"], [\"\", \"email\", \"\", \"ngModel\", \"\"]],\n    inputs: {\n      email: \"email\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([EMAIL_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EmailValidator, [{\n    type: Directive,\n    args: [{\n      selector: '[email][formControlName],[email][formControl],[email][ngModel]',\n      providers: [EMAIL_VALIDATOR],\n      standalone: false\n    }]\n  }], null, {\n    email: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @description\n * Provider which adds `MinLengthValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nconst MIN_LENGTH_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MinLengthValidator),\n  multi: true\n};\n/**\n * A directive that adds minimum length validation to controls marked with the\n * `minlength` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/forms/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a minimum length validator\n *\n * The following example shows how to add a minimum length validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input name=\"firstName\" ngModel minlength=\"4\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass MinLengthValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the minimum length bound to this directive.\n   */\n  minlength;\n  /** @internal */\n  inputName = 'minlength';\n  /** @internal */\n  normalizeInput = input => toInteger(input);\n  /** @internal */\n  createValidator = minlength => minLengthValidator(minlength);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMinLengthValidator_BaseFactory;\n    return function MinLengthValidator_Factory(__ngFactoryType__) {\n      return (ɵMinLengthValidator_BaseFactory || (ɵMinLengthValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MinLengthValidator)))(__ngFactoryType__ || MinLengthValidator);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MinLengthValidator,\n    selectors: [[\"\", \"minlength\", \"\", \"formControlName\", \"\"], [\"\", \"minlength\", \"\", \"formControl\", \"\"], [\"\", \"minlength\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function MinLengthValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"minlength\", ctx._enabled ? ctx.minlength : null);\n      }\n    },\n    inputs: {\n      minlength: \"minlength\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([MIN_LENGTH_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MinLengthValidator, [{\n    type: Directive,\n    args: [{\n      selector: '[minlength][formControlName],[minlength][formControl],[minlength][ngModel]',\n      providers: [MIN_LENGTH_VALIDATOR],\n      host: {\n        '[attr.minlength]': '_enabled ? minlength : null'\n      },\n      standalone: false\n    }]\n  }], null, {\n    minlength: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @description\n * Provider which adds `MaxLengthValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nconst MAX_LENGTH_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MaxLengthValidator),\n  multi: true\n};\n/**\n * A directive that adds maximum length validation to controls marked with the\n * `maxlength` attribute. The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/forms/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a maximum length validator\n *\n * The following example shows how to add a maximum length validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input name=\"firstName\" ngModel maxlength=\"25\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass MaxLengthValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the maximum length bound to this directive.\n   */\n  maxlength;\n  /** @internal */\n  inputName = 'maxlength';\n  /** @internal */\n  normalizeInput = input => toInteger(input);\n  /** @internal */\n  createValidator = maxlength => maxLengthValidator(maxlength);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMaxLengthValidator_BaseFactory;\n    return function MaxLengthValidator_Factory(__ngFactoryType__) {\n      return (ɵMaxLengthValidator_BaseFactory || (ɵMaxLengthValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MaxLengthValidator)))(__ngFactoryType__ || MaxLengthValidator);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MaxLengthValidator,\n    selectors: [[\"\", \"maxlength\", \"\", \"formControlName\", \"\"], [\"\", \"maxlength\", \"\", \"formControl\", \"\"], [\"\", \"maxlength\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function MaxLengthValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"maxlength\", ctx._enabled ? ctx.maxlength : null);\n      }\n    },\n    inputs: {\n      maxlength: \"maxlength\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([MAX_LENGTH_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MaxLengthValidator, [{\n    type: Directive,\n    args: [{\n      selector: '[maxlength][formControlName],[maxlength][formControl],[maxlength][ngModel]',\n      providers: [MAX_LENGTH_VALIDATOR],\n      host: {\n        '[attr.maxlength]': '_enabled ? maxlength : null'\n      },\n      standalone: false\n    }]\n  }], null, {\n    maxlength: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @description\n * Provider which adds `PatternValidator` to the `NG_VALIDATORS` multi-provider list.\n */\nconst PATTERN_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => PatternValidator),\n  multi: true\n};\n/**\n * @description\n * A directive that adds regex pattern validation to controls marked with the\n * `pattern` attribute. The regex must match the entire control value.\n * The directive is provided with the `NG_VALIDATORS` multi-provider list.\n *\n * @see [Form Validation](guide/forms/form-validation)\n *\n * @usageNotes\n *\n * ### Adding a pattern validator\n *\n * The following example shows how to add a pattern validator to an input attached to an\n * ngModel binding.\n *\n * ```html\n * <input name=\"firstName\" ngModel pattern=\"[a-zA-Z ]*\">\n * ```\n *\n * @ngModule ReactiveFormsModule\n * @ngModule FormsModule\n * @publicApi\n */\nclass PatternValidator extends AbstractValidatorDirective {\n  /**\n   * @description\n   * Tracks changes to the pattern bound to this directive.\n   */\n  pattern; // This input is always defined, since the name matches selector.\n  /** @internal */\n  inputName = 'pattern';\n  /** @internal */\n  normalizeInput = input => input;\n  /** @internal */\n  createValidator = input => patternValidator(input);\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵPatternValidator_BaseFactory;\n    return function PatternValidator_Factory(__ngFactoryType__) {\n      return (ɵPatternValidator_BaseFactory || (ɵPatternValidator_BaseFactory = i0.ɵɵgetInheritedFactory(PatternValidator)))(__ngFactoryType__ || PatternValidator);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: PatternValidator,\n    selectors: [[\"\", \"pattern\", \"\", \"formControlName\", \"\"], [\"\", \"pattern\", \"\", \"formControl\", \"\"], [\"\", \"pattern\", \"\", \"ngModel\", \"\"]],\n    hostVars: 1,\n    hostBindings: function PatternValidator_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"pattern\", ctx._enabled ? ctx.pattern : null);\n      }\n    },\n    inputs: {\n      pattern: \"pattern\"\n    },\n    standalone: false,\n    features: [i0.ɵɵProvidersFeature([PATTERN_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PatternValidator, [{\n    type: Directive,\n    args: [{\n      selector: '[pattern][formControlName],[pattern][formControl],[pattern][ngModel]',\n      providers: [PATTERN_VALIDATOR],\n      host: {\n        '[attr.pattern]': '_enabled ? pattern : null'\n      },\n      standalone: false\n    }]\n  }], null, {\n    pattern: [{\n      type: Input\n    }]\n  });\n})();\nconst SHARED_FORM_DIRECTIVES = [ɵNgNoValidate, NgSelectOption, ɵNgSelectMultipleOption, DefaultValueAccessor, NumberValueAccessor, RangeValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, SelectMultipleControlValueAccessor, RadioControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, MinLengthValidator, MaxLengthValidator, PatternValidator, CheckboxRequiredValidator, EmailValidator, MinValidator, MaxValidator];\nconst TEMPLATE_DRIVEN_DIRECTIVES = [NgModel, NgModelGroup, NgForm];\nconst REACTIVE_DRIVEN_DIRECTIVES = [FormControlDirective, FormGroupDirective, FormControlName, FormGroupName, FormArrayName];\n/**\n * Internal module used for sharing directives between FormsModule and ReactiveFormsModule\n */\nclass ɵInternalFormsSharedModule {\n  static ɵfac = function ɵInternalFormsSharedModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ɵInternalFormsSharedModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ɵInternalFormsSharedModule,\n    declarations: [ɵNgNoValidate, NgSelectOption, ɵNgSelectMultipleOption, DefaultValueAccessor, NumberValueAccessor, RangeValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, SelectMultipleControlValueAccessor, RadioControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, MinLengthValidator, MaxLengthValidator, PatternValidator, CheckboxRequiredValidator, EmailValidator, MinValidator, MaxValidator],\n    exports: [ɵNgNoValidate, NgSelectOption, ɵNgSelectMultipleOption, DefaultValueAccessor, NumberValueAccessor, RangeValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, SelectMultipleControlValueAccessor, RadioControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, MinLengthValidator, MaxLengthValidator, PatternValidator, CheckboxRequiredValidator, EmailValidator, MinValidator, MaxValidator]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ɵInternalFormsSharedModule, [{\n    type: NgModule,\n    args: [{\n      declarations: SHARED_FORM_DIRECTIVES,\n      exports: SHARED_FORM_DIRECTIVES\n    }]\n  }], null, null);\n})();\n\n/**\n * Tracks the value and validity state of an array of `FormControl`,\n * `FormGroup` or `FormArray` instances.\n *\n * A `FormArray` aggregates the values of each child `FormControl` into an array.\n * It calculates its status by reducing the status values of its children. For example, if one of\n * the controls in a `FormArray` is invalid, the entire array becomes invalid.\n *\n * `FormArray` accepts one generic argument, which is the type of the controls inside.\n * If you need a heterogenous array, use {@link UntypedFormArray}.\n *\n * `FormArray` is one of the four fundamental building blocks used to define forms in Angular,\n * along with `FormControl`, `FormGroup`, and `FormRecord`.\n *\n * @usageNotes\n *\n * ### Create an array of form controls\n *\n * ```ts\n * const arr = new FormArray([\n *   new FormControl('Nancy', Validators.minLength(2)),\n *   new FormControl('Drew'),\n * ]);\n *\n * console.log(arr.value);   // ['Nancy', 'Drew']\n * console.log(arr.status);  // 'VALID'\n * ```\n *\n * ### Create a form array with array-level validators\n *\n * You include array-level validators and async validators. These come in handy\n * when you want to perform validation that considers the value of more than one child\n * control.\n *\n * The two types of validators are passed in separately as the second and third arg\n * respectively, or together as part of an options object.\n *\n * ```ts\n * const arr = new FormArray([\n *   new FormControl('Nancy'),\n *   new FormControl('Drew')\n * ], {validators: myValidator, asyncValidators: myAsyncValidator});\n * ```\n *\n * ### Set the updateOn property for all controls in a form array\n *\n * The options object is used to set a default value for each child\n * control's `updateOn` property. If you set `updateOn` to `'blur'` at the\n * array level, all child controls default to 'blur', unless the child\n * has explicitly specified a different `updateOn` value.\n *\n * ```ts\n * const arr = new FormArray([\n *    new FormControl()\n * ], {updateOn: 'blur'});\n * ```\n *\n * ### Adding or removing controls from a form array\n *\n * To change the controls in the array, use the `push`, `insert`, `removeAt` or `clear` methods\n * in `FormArray` itself. These methods ensure the controls are properly tracked in the\n * form's hierarchy. Do not modify the array of `AbstractControl`s used to instantiate\n * the `FormArray` directly, as that result in strange and unexpected behavior such\n * as broken change detection.\n *\n * @publicApi\n */\nclass FormArray extends AbstractControl {\n  /**\n   * Creates a new `FormArray` instance.\n   *\n   * @param controls An array of child controls. Each child control is given an index\n   * where it is registered.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of\n   * such functions, or an `AbstractControlOptions` object that contains validation functions\n   * and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator functions\n   *\n   */\n  constructor(controls, validatorOrOpts, asyncValidator) {\n    super(pickValidators(validatorOrOpts), pickAsyncValidators(asyncValidator, validatorOrOpts));\n    this.controls = controls;\n    this._initObservables();\n    this._setUpdateStrategy(validatorOrOpts);\n    this._setUpControls();\n    this.updateValueAndValidity({\n      onlySelf: true,\n      // If `asyncValidator` is present, it will trigger control status change from `PENDING` to\n      // `VALID` or `INVALID`.\n      // The status should be broadcasted via the `statusChanges` observable, so we set `emitEvent`\n      // to `true` to allow that during the control creation process.\n      emitEvent: !!this.asyncValidator\n    });\n  }\n  controls;\n  /**\n   * Get the `AbstractControl` at the given `index` in the array.\n   *\n   * @param index Index in the array to retrieve the control. If `index` is negative, it will wrap\n   *     around from the back, and if index is greatly negative (less than `-length`), the result is\n   * undefined. This behavior is the same as `Array.at(index)`.\n   */\n  at(index) {\n    return this.controls[this._adjustIndex(index)];\n  }\n  /**\n   * Insert a new `AbstractControl` at the end of the array.\n   *\n   * @param control Form control to be inserted\n   * @param options Specifies whether this FormArray instance should emit events after a new\n   *     control is added.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * inserted. When false, no events are emitted.\n   */\n  push(control, options = {}) {\n    if (Array.isArray(control)) {\n      control.forEach(ctrl => {\n        this.controls.push(ctrl);\n        this._registerControl(ctrl);\n      });\n    } else {\n      this.controls.push(control);\n      this._registerControl(control);\n    }\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n    this._onCollectionChange();\n  }\n  /**\n   * Insert a new `AbstractControl` at the given `index` in the array.\n   *\n   * @param index Index in the array to insert the control. If `index` is negative, wraps around\n   *     from the back. If `index` is greatly negative (less than `-length`), prepends to the array.\n   * This behavior is the same as `Array.splice(index, 0, control)`.\n   * @param control Form control to be inserted\n   * @param options Specifies whether this FormArray instance should emit events after a new\n   *     control is inserted.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * inserted. When false, no events are emitted.\n   */\n  insert(index, control, options = {}) {\n    this.controls.splice(index, 0, control);\n    this._registerControl(control);\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n  }\n  /**\n   * Remove the control at the given `index` in the array.\n   *\n   * @param index Index in the array to remove the control.  If `index` is negative, wraps around\n   *     from the back. If `index` is greatly negative (less than `-length`), removes the first\n   *     element. This behavior is the same as `Array.splice(index, 1)`.\n   * @param options Specifies whether this FormArray instance should emit events after a\n   *     control is removed.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * removed. When false, no events are emitted.\n   */\n  removeAt(index, options = {}) {\n    // Adjust the index, then clamp it at no less than 0 to prevent undesired underflows.\n    let adjustedIndex = this._adjustIndex(index);\n    if (adjustedIndex < 0) adjustedIndex = 0;\n    if (this.controls[adjustedIndex]) this.controls[adjustedIndex]._registerOnCollectionChange(() => {});\n    this.controls.splice(adjustedIndex, 1);\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n  }\n  /**\n   * Replace an existing control.\n   *\n   * @param index Index in the array to replace the control. If `index` is negative, wraps around\n   *     from the back. If `index` is greatly negative (less than `-length`), replaces the first\n   *     element. This behavior is the same as `Array.splice(index, 1, control)`.\n   * @param control The `AbstractControl` control to replace the existing control\n   * @param options Specifies whether this FormArray instance should emit events after an\n   *     existing control is replaced with a new one.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control is\n   * replaced with a new one. When false, no events are emitted.\n   */\n  setControl(index, control, options = {}) {\n    // Adjust the index, then clamp it at no less than 0 to prevent undesired underflows.\n    let adjustedIndex = this._adjustIndex(index);\n    if (adjustedIndex < 0) adjustedIndex = 0;\n    if (this.controls[adjustedIndex]) this.controls[adjustedIndex]._registerOnCollectionChange(() => {});\n    this.controls.splice(adjustedIndex, 1);\n    if (control) {\n      this.controls.splice(adjustedIndex, 0, control);\n      this._registerControl(control);\n    }\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n    this._onCollectionChange();\n  }\n  /**\n   * Length of the control array.\n   */\n  get length() {\n    return this.controls.length;\n  }\n  /**\n   * Sets the value of the `FormArray`. It accepts an array that matches\n   * the structure of the control.\n   *\n   * This method performs strict checks, and throws an error if you try\n   * to set the value of a control that doesn't exist or if you exclude the\n   * value of a control.\n   *\n   * @usageNotes\n   * ### Set the values for the controls in the form array\n   *\n   * ```ts\n   * const arr = new FormArray([\n   *   new FormControl(),\n   *   new FormControl()\n   * ]);\n   * console.log(arr.value);   // [null, null]\n   *\n   * arr.setValue(['Nancy', 'Drew']);\n   * console.log(arr.value);   // ['Nancy', 'Drew']\n   * ```\n   *\n   * @param value Array of values for the controls\n   * @param options Configure options that determine how the control propagates changes and\n   * emits events after the value changes\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default\n   * is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control value is updated.\n   * When false, no events are emitted.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   */\n  setValue(value, options = {}) {\n    assertAllValuesPresent(this, false, value);\n    value.forEach((newValue, index) => {\n      assertControlPresent(this, false, index);\n      this.at(index).setValue(newValue, {\n        onlySelf: true,\n        emitEvent: options.emitEvent\n      });\n    });\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * Patches the value of the `FormArray`. It accepts an array that matches the\n   * structure of the control, and does its best to match the values to the correct\n   * controls in the group.\n   *\n   * It accepts both super-sets and sub-sets of the array without throwing an error.\n   *\n   * @usageNotes\n   * ### Patch the values for controls in a form array\n   *\n   * ```ts\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   * console.log(arr.value);   // [null, null]\n   *\n   * arr.patchValue(['Nancy']);\n   * console.log(arr.value);   // ['Nancy', null]\n   * ```\n   *\n   * @param value Array of latest values for the controls\n   * @param options Configure options that determine how the control propagates changes and\n   * emits events after the value changes\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default\n   * is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when the control\n   * value is updated. When false, no events are emitted. The configuration options are passed to\n   * the {@link AbstractControl#updateValueAndValidity updateValueAndValidity} method.\n   */\n  patchValue(value, options = {}) {\n    // Even though the `value` argument type doesn't allow `null` and `undefined` values, the\n    // `patchValue` can be called recursively and inner data structures might have these values,\n    // so we just ignore such cases when a field containing FormArray instance receives `null` or\n    // `undefined` as a value.\n    if (value == null /* both `null` and `undefined` */) return;\n    value.forEach((newValue, index) => {\n      if (this.at(index)) {\n        this.at(index).patchValue(newValue, {\n          onlySelf: true,\n          emitEvent: options.emitEvent\n        });\n      }\n    });\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * Resets the `FormArray` and all descendants are marked `pristine` and `untouched`, and the\n   * value of all descendants to null or null maps.\n   *\n   * You reset to a specific form state by passing in an array of states\n   * that matches the structure of the control. The state is a standalone value\n   * or a form state object with both a value and a disabled status.\n   *\n   * @usageNotes\n   * ### Reset the values in a form array\n   *\n   * ```ts\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   * arr.reset(['name', 'last name']);\n   *\n   * console.log(arr.value);  // ['name', 'last name']\n   * ```\n   *\n   * ### Reset the values in a form array and the disabled status for the first control\n   *\n   * ```ts\n   * arr.reset([\n   *   {value: 'name', disabled: true},\n   *   'last'\n   * ]);\n   *\n   * console.log(arr.value);  // ['last']\n   * console.log(arr.at(0).status);  // 'DISABLED'\n   * ```\n   *\n   * @param value Array of values for the controls\n   * @param options Configure options that determine how the control propagates changes and\n   * emits events after the value changes\n   *\n   * * `onlySelf`: When true, each change only affects this control, and not its parent. Default\n   * is false.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges`\n   * observables emit events with the latest status and value when the control is reset.\n   * When false, no events are emitted.\n   * The configuration options are passed to the {@link AbstractControl#updateValueAndValidity\n   * updateValueAndValidity} method.\n   */\n  reset(value = [], options = {}) {\n    this._forEachChild((control, index) => {\n      control.reset(value[index], {\n        onlySelf: true,\n        emitEvent: options.emitEvent\n      });\n    });\n    this._updatePristine(options, this);\n    this._updateTouched(options, this);\n    this.updateValueAndValidity(options);\n  }\n  /**\n   * The aggregate value of the array, including any disabled controls.\n   *\n   * Reports all values regardless of disabled status.\n   */\n  getRawValue() {\n    return this.controls.map(control => control.getRawValue());\n  }\n  /**\n   * Remove all controls in the `FormArray`.\n   *\n   * @param options Specifies whether this FormArray instance should emit events after all\n   *     controls are removed.\n   * * `emitEvent`: When true or not supplied (the default), both the `statusChanges` and\n   * `valueChanges` observables emit events with the latest status and value when all controls\n   * in this FormArray instance are removed. When false, no events are emitted.\n   *\n   * @usageNotes\n   * ### Remove all elements from a FormArray\n   *\n   * ```ts\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   * console.log(arr.length);  // 2\n   *\n   * arr.clear();\n   * console.log(arr.length);  // 0\n   * ```\n   *\n   * It's a simpler and more efficient alternative to removing all elements one by one:\n   *\n   * ```ts\n   * const arr = new FormArray([\n   *    new FormControl(),\n   *    new FormControl()\n   * ]);\n   *\n   * while (arr.length) {\n   *    arr.removeAt(0);\n   * }\n   * ```\n   */\n  clear(options = {}) {\n    if (this.controls.length < 1) return;\n    this._forEachChild(control => control._registerOnCollectionChange(() => {}));\n    this.controls.splice(0);\n    this.updateValueAndValidity({\n      emitEvent: options.emitEvent\n    });\n  }\n  /**\n   * Adjusts a negative index by summing it with the length of the array. For very negative\n   * indices, the result may remain negative.\n   * @internal\n   */\n  _adjustIndex(index) {\n    return index < 0 ? index + this.length : index;\n  }\n  /** @internal */\n  _syncPendingControls() {\n    let subtreeUpdated = this.controls.reduce((updated, child) => {\n      return child._syncPendingControls() ? true : updated;\n    }, false);\n    if (subtreeUpdated) this.updateValueAndValidity({\n      onlySelf: true\n    });\n    return subtreeUpdated;\n  }\n  /** @internal */\n  _forEachChild(cb) {\n    this.controls.forEach((control, index) => {\n      cb(control, index);\n    });\n  }\n  /** @internal */\n  _updateValue() {\n    this.value = this.controls.filter(control => control.enabled || this.disabled).map(control => control.value);\n  }\n  /** @internal */\n  _anyControls(condition) {\n    return this.controls.some(control => control.enabled && condition(control));\n  }\n  /** @internal */\n  _setUpControls() {\n    this._forEachChild(control => this._registerControl(control));\n  }\n  /** @internal */\n  _allControlsDisabled() {\n    for (const control of this.controls) {\n      if (control.enabled) return false;\n    }\n    return this.controls.length > 0 || this.disabled;\n  }\n  _registerControl(control) {\n    control.setParent(this);\n    control._registerOnCollectionChange(this._onCollectionChange);\n  }\n  /** @internal */\n  _find(name) {\n    return this.at(name) ?? null;\n  }\n}\nconst UntypedFormArray = FormArray;\n/**\n * @description\n * Asserts that the given control is an instance of `FormArray`\n *\n * @publicApi\n */\nconst isFormArray = control => control instanceof FormArray;\nfunction isAbstractControlOptions(options) {\n  return !!options && (options.asyncValidators !== undefined || options.validators !== undefined || options.updateOn !== undefined);\n}\n/**\n * @description\n * Creates an `AbstractControl` from a user-specified configuration.\n *\n * The `FormBuilder` provides syntactic sugar that shortens creating instances of a\n * `FormControl`, `FormGroup`, or `FormArray`. It reduces the amount of boilerplate needed to\n * build complex forms.\n *\n * @see [Reactive Forms Guide](guide/forms/reactive-forms)\n *\n * @publicApi\n */\nclass FormBuilder {\n  useNonNullable = false;\n  /**\n   * @description\n   * Returns a FormBuilder in which automatically constructed `FormControl` elements\n   * have `{nonNullable: true}` and are non-nullable.\n   *\n   * **Constructing non-nullable controls**\n   *\n   * When constructing a control, it will be non-nullable, and will reset to its initial value.\n   *\n   * ```ts\n   * let nnfb = new FormBuilder().nonNullable;\n   * let name = nnfb.control('Alex'); // FormControl<string>\n   * name.reset();\n   * console.log(name); // 'Alex'\n   * ```\n   *\n   * **Constructing non-nullable groups or arrays**\n   *\n   * When constructing a group or array, all automatically created inner controls will be\n   * non-nullable, and will reset to their initial values.\n   *\n   * ```ts\n   * let nnfb = new FormBuilder().nonNullable;\n   * let name = nnfb.group({who: 'Alex'}); // FormGroup<{who: FormControl<string>}>\n   * name.reset();\n   * console.log(name); // {who: 'Alex'}\n   * ```\n   * **Constructing *nullable* fields on groups or arrays**\n   *\n   * It is still possible to have a nullable field. In particular, any `FormControl` which is\n   * *already* constructed will not be altered. For example:\n   *\n   * ```ts\n   * let nnfb = new FormBuilder().nonNullable;\n   * // FormGroup<{who: FormControl<string|null>}>\n   * let name = nnfb.group({who: new FormControl('Alex')});\n   * name.reset(); console.log(name); // {who: null}\n   * ```\n   *\n   * Because the inner control is constructed explicitly by the caller, the builder has\n   * no control over how it is created, and cannot exclude the `null`.\n   */\n  get nonNullable() {\n    const nnfb = new FormBuilder();\n    nnfb.useNonNullable = true;\n    return nnfb;\n  }\n  group(controls, options = null) {\n    const reducedControls = this._reduceControls(controls);\n    let newOptions = {};\n    if (isAbstractControlOptions(options)) {\n      // `options` are `AbstractControlOptions`\n      newOptions = options;\n    } else if (options !== null) {\n      // `options` are legacy form group options\n      newOptions.validators = options.validator;\n      newOptions.asyncValidators = options.asyncValidator;\n    }\n    return new FormGroup(reducedControls, newOptions);\n  }\n  /**\n   * @description\n   * Constructs a new `FormRecord` instance. Accepts a single generic argument, which is an object\n   * containing all the keys and corresponding inner control types.\n   *\n   * @param controls A collection of child controls. The key for each child is the name\n   * under which it is registered.\n   *\n   * @param options Configuration options object for the `FormRecord`. The object should have the\n   * `AbstractControlOptions` type and might contain the following fields:\n   * * `validators`: A synchronous validator function, or an array of validator functions.\n   * * `asyncValidators`: A single async validator or array of async validator functions.\n   * * `updateOn`: The event upon which the control should be updated (options: 'change' | 'blur'\n   * | submit').\n   */\n  record(controls, options = null) {\n    const reducedControls = this._reduceControls(controls);\n    // Cast to `any` because the inferred types are not as specific as Element.\n    return new FormRecord(reducedControls, options);\n  }\n  /**\n   * @description\n   * Constructs a new `FormControl` with the given state, validators and options. Sets\n   * `{nonNullable: true}` in the options to get a non-nullable control. Otherwise, the\n   * control will be nullable. Accepts a single generic argument, which is the type  of the\n   * control's value.\n   *\n   * @param formState Initializes the control with an initial state value, or\n   * with an object that contains both a value and a disabled status.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of\n   * such functions, or a `FormControlOptions` object that contains\n   * validation functions and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator\n   * functions.\n   *\n   * @usageNotes\n   *\n   * ### Initialize a control as disabled\n   *\n   * The following example returns a control with an initial value in a disabled state.\n   *\n   * {@example forms/ts/formBuilder/form_builder_example.ts region='disabled-control'}\n   */\n  control(formState, validatorOrOpts, asyncValidator) {\n    let newOptions = {};\n    if (!this.useNonNullable) {\n      return new FormControl(formState, validatorOrOpts, asyncValidator);\n    }\n    if (isAbstractControlOptions(validatorOrOpts)) {\n      // If the second argument is options, then they are copied.\n      newOptions = validatorOrOpts;\n    } else {\n      // If the other arguments are validators, they are copied into an options object.\n      newOptions.validators = validatorOrOpts;\n      newOptions.asyncValidators = asyncValidator;\n    }\n    return new FormControl(formState, {\n      ...newOptions,\n      nonNullable: true\n    });\n  }\n  /**\n   * Constructs a new `FormArray` from the given array of configurations,\n   * validators and options. Accepts a single generic argument, which is the type of each control\n   * inside the array.\n   *\n   * @param controls An array of child controls or control configs. Each child control is given an\n   *     index when it is registered.\n   *\n   * @param validatorOrOpts A synchronous validator function, or an array of such functions, or an\n   *     `AbstractControlOptions` object that contains\n   * validation functions and a validation trigger.\n   *\n   * @param asyncValidator A single async validator or array of async validator functions.\n   */\n  array(controls, validatorOrOpts, asyncValidator) {\n    const createdControls = controls.map(c => this._createControl(c));\n    // Cast to `any` because the inferred types are not as specific as Element.\n    return new FormArray(createdControls, validatorOrOpts, asyncValidator);\n  }\n  /** @internal */\n  _reduceControls(controls) {\n    const createdControls = {};\n    Object.keys(controls).forEach(controlName => {\n      createdControls[controlName] = this._createControl(controls[controlName]);\n    });\n    return createdControls;\n  }\n  /** @internal */\n  _createControl(controls) {\n    if (controls instanceof FormControl) {\n      return controls;\n    } else if (controls instanceof AbstractControl) {\n      // A control; just return it\n      return controls;\n    } else if (Array.isArray(controls)) {\n      // ControlConfig Tuple\n      const value = controls[0];\n      const validator = controls.length > 1 ? controls[1] : null;\n      const asyncValidator = controls.length > 2 ? controls[2] : null;\n      return this.control(value, validator, asyncValidator);\n    } else {\n      // T or FormControlState<T>\n      return this.control(controls);\n    }\n  }\n  static ɵfac = function FormBuilder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FormBuilder)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FormBuilder,\n    factory: FormBuilder.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * @description\n * `NonNullableFormBuilder` is similar to {@link FormBuilder}, but automatically constructed\n * {@link FormControl} elements have `{nonNullable: true}` and are non-nullable.\n *\n * @publicApi\n */\nclass NonNullableFormBuilder {\n  static ɵfac = function NonNullableFormBuilder_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NonNullableFormBuilder)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: NonNullableFormBuilder,\n    factory: () => (() => inject(FormBuilder).nonNullable)(),\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NonNullableFormBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: () => inject(FormBuilder).nonNullable\n    }]\n  }], null, null);\n})();\n/**\n * UntypedFormBuilder is the same as `FormBuilder`, but it provides untyped controls.\n */\nclass UntypedFormBuilder extends FormBuilder {\n  group(controlsConfig, options = null) {\n    return super.group(controlsConfig, options);\n  }\n  /**\n   * Like `FormBuilder#control`, except the resulting control is untyped.\n   */\n  control(formState, validatorOrOpts, asyncValidator) {\n    return super.control(formState, validatorOrOpts, asyncValidator);\n  }\n  /**\n   * Like `FormBuilder#array`, except the resulting array is untyped.\n   */\n  array(controlsConfig, validatorOrOpts, asyncValidator) {\n    return super.array(controlsConfig, validatorOrOpts, asyncValidator);\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵUntypedFormBuilder_BaseFactory;\n    return function UntypedFormBuilder_Factory(__ngFactoryType__) {\n      return (ɵUntypedFormBuilder_BaseFactory || (ɵUntypedFormBuilder_BaseFactory = i0.ɵɵgetInheritedFactory(UntypedFormBuilder)))(__ngFactoryType__ || UntypedFormBuilder);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: UntypedFormBuilder,\n    factory: UntypedFormBuilder.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UntypedFormBuilder, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the forms package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('20.3.1');\n\n/**\n * Exports the required providers and directives for template-driven forms,\n * making them available for import by NgModules that import this module.\n *\n * @see [Forms Overview](guide/forms)\n * @see [Template-driven Forms Guide](guide/forms)\n *\n * @publicApi\n */\nclass FormsModule {\n  /**\n   * @description\n   * Provides options for configuring the forms module.\n   *\n   * @param opts An object of configuration options\n   * * `callSetDisabledState` Configures whether to `always` call `setDisabledState`, which is more\n   * correct, or to only call it `whenDisabled`, which is the legacy behavior.\n   */\n  static withConfig(opts) {\n    return {\n      ngModule: FormsModule,\n      providers: [{\n        provide: CALL_SET_DISABLED_STATE,\n        useValue: opts.callSetDisabledState ?? setDisabledStateDefault\n      }]\n    };\n  }\n  static ɵfac = function FormsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FormsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FormsModule,\n    declarations: [NgModel, NgModelGroup, NgForm],\n    exports: [ɵInternalFormsSharedModule, NgModel, NgModelGroup, NgForm]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ɵInternalFormsSharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FormsModule, [{\n    type: NgModule,\n    args: [{\n      declarations: TEMPLATE_DRIVEN_DIRECTIVES,\n      exports: [ɵInternalFormsSharedModule, TEMPLATE_DRIVEN_DIRECTIVES]\n    }]\n  }], null, null);\n})();\n/**\n * Exports the required infrastructure and directives for reactive forms,\n * making them available for import by NgModules that import this module.\n *\n * @see [Forms Overview](guide/forms)\n * @see [Reactive Forms Guide](guide/forms/reactive-forms)\n *\n * @publicApi\n */\nclass ReactiveFormsModule {\n  /**\n   * @description\n   * Provides options for configuring the reactive forms module.\n   *\n   * @param opts An object of configuration options\n   * * `warnOnNgModelWithFormControl` Configures when to emit a warning when an `ngModel`\n   * binding is used with reactive form directives.\n   * * `callSetDisabledState` Configures whether to `always` call `setDisabledState`, which is more\n   * correct, or to only call it `whenDisabled`, which is the legacy behavior.\n   */\n  static withConfig(opts) {\n    return {\n      ngModule: ReactiveFormsModule,\n      providers: [{\n        provide: NG_MODEL_WITH_FORM_CONTROL_WARNING,\n        useValue: opts.warnOnNgModelWithFormControl ?? 'always'\n      }, {\n        provide: CALL_SET_DISABLED_STATE,\n        useValue: opts.callSetDisabledState ?? setDisabledStateDefault\n      }]\n    };\n  }\n  static ɵfac = function ReactiveFormsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ReactiveFormsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ReactiveFormsModule,\n    declarations: [FormControlDirective, FormGroupDirective, FormControlName, FormGroupName, FormArrayName],\n    exports: [ɵInternalFormsSharedModule, FormControlDirective, FormGroupDirective, FormControlName, FormGroupName, FormArrayName]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [ɵInternalFormsSharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ReactiveFormsModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [REACTIVE_DRIVEN_DIRECTIVES],\n      exports: [ɵInternalFormsSharedModule, REACTIVE_DRIVEN_DIRECTIVES]\n    }]\n  }], null, null);\n})();\nexport { AbstractControl, AbstractControlDirective, AbstractFormGroupDirective, COMPOSITION_BUFFER_MODE, CheckboxControlValueAccessor, CheckboxRequiredValidator, ControlContainer, ControlEvent, DefaultValueAccessor, EmailValidator, FormArray, FormArrayName, FormBuilder, FormControl, FormControlDirective, FormControlName, FormGroup, FormGroupDirective, FormGroupName, FormRecord, FormResetEvent, FormSubmittedEvent, FormsModule, MaxLengthValidator, MaxValidator, MinLengthValidator, MinValidator, NG_ASYNC_VALIDATORS, NG_VALIDATORS, NG_VALUE_ACCESSOR, NgControl, NgControlStatus, NgControlStatusGroup, NgForm, NgModel, NgModelGroup, NgSelectOption, NonNullableFormBuilder, NumberValueAccessor, PatternValidator, PristineChangeEvent, RadioControlValueAccessor, RangeValueAccessor, ReactiveFormsModule, RequiredValidator, SelectControlValueAccessor, SelectMultipleControlValueAccessor, StatusChangeEvent, TouchedChangeEvent, UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, VERSION, Validators, ValueChangeEvent, isFormArray, isFormControl, isFormGroup, isFormRecord, ɵInternalFormsSharedModule, ɵNgNoValidate, ɵNgSelectMultipleOption };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,OAAK;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,YAAY,MAAM;AAAA,EAAC;AAAA,EACnB,YAAY,WAAW,aAAa;AAClC,SAAK,YAAY;AACjB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,KAAK,OAAO;AACtB,SAAK,UAAU,YAAY,KAAK,YAAY,eAAe,KAAK,KAAK;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,YAAY;AAC3B,SAAK,YAAY,YAAY,UAAU;AAAA,EACzC;AAAA,EACA,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA6B,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,CAAC;AAAA,EACpI;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,EACR,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAUH,IAAM,8BAAN,MAAM,qCAAoC,yBAAyB;AAAA,EACjE,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,oCAAoC,mBAAmB;AACrE,cAAQ,6CAA6C,2CAA8C,sBAAsB,4BAA2B,IAAI,qBAAqB,4BAA2B;AAAA,IAC1M;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,oBAAoB,IAAI,eAAe,YAAY,oBAAoB,EAAE;AAC/E,IAAM,0BAA0B;AAAA,EAC9B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,4BAA4B;AAAA,EAC1D,OAAO;AACT;AAwBA,IAAM,+BAAN,MAAM,sCAAqC,4BAA4B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrE,WAAW,OAAO;AAChB,SAAK,YAAY,WAAW,KAAK;AAAA,EACnC;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qCAAqC,mBAAmB;AACtE,cAAQ,8CAA8C,4CAA+C,sBAAsB,6BAA4B,IAAI,qBAAqB,6BAA4B;AAAA,IAC9M;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,QAAQ,YAAY,mBAAmB,EAAE,GAAG,CAAC,SAAS,QAAQ,YAAY,eAAe,EAAE,GAAG,CAAC,SAAS,QAAQ,YAAY,WAAW,EAAE,CAAC;AAAA,IAChK,cAAc,SAAS,0CAA0C,IAAI,KAAK;AACxE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,UAAU,SAAS,uDAAuD,QAAQ;AAC9F,iBAAO,IAAI,SAAS,OAAO,OAAO,OAAO;AAAA,QAC3C,CAAC,EAAE,QAAQ,SAAS,uDAAuD;AACzE,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,uBAAuB,CAAC,GAAM,0BAA0B;AAAA,EAC5F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ;AAAA,MACA,WAAW,CAAC,uBAAuB;AAAA,MACnC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,yBAAyB;AAAA,EAC7B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,oBAAoB;AAAA,EAClD,OAAO;AACT;AAKA,SAAS,aAAa;AACpB,QAAM,YAAY,OAAQ,IAAI,OAAQ,EAAE,aAAa,IAAI;AACzD,SAAO,gBAAgB,KAAK,UAAU,YAAY,CAAC;AACrD;AAOA,IAAM,0BAA0B,IAAI,eAAe,YAAY,yBAAyB,EAAE;AAmC1F,IAAM,uBAAN,MAAM,8BAA6B,yBAAyB;AAAA,EAC1D;AAAA;AAAA,EAEA,aAAa;AAAA,EACb,YAAY,UAAU,YAAY,kBAAkB;AAClD,UAAM,UAAU,UAAU;AAC1B,SAAK,mBAAmB;AACxB,QAAI,KAAK,oBAAoB,MAAM;AACjC,WAAK,mBAAmB,CAAC,WAAW;AAAA,IACtC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,UAAM,kBAAkB,SAAS,OAAO,KAAK;AAC7C,SAAK,YAAY,SAAS,eAAe;AAAA,EAC3C;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,QAAI,CAAC,KAAK,oBAAoB,KAAK,oBAAoB,CAAC,KAAK,YAAY;AACvE,WAAK,SAAS,KAAK;AAAA,IACrB;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,SAAK,aAAa;AAClB,SAAK,oBAAoB,KAAK,SAAS,KAAK;AAAA,EAC9C;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,yBAAyB,CAAC,CAAC;AAAA,EAClL;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,mBAAmB,IAAI,GAAG,QAAQ,UAAU,GAAG,CAAC,YAAY,mBAAmB,EAAE,GAAG,CAAC,SAAS,eAAe,IAAI,GAAG,QAAQ,UAAU,GAAG,CAAC,YAAY,eAAe,EAAE,GAAG,CAAC,SAAS,WAAW,IAAI,GAAG,QAAQ,UAAU,GAAG,CAAC,YAAY,WAAW,EAAE,GAAG,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,IAC1S,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,8CAA8C,QAAQ;AACpF,iBAAO,IAAI,aAAa,OAAO,OAAO,KAAK;AAAA,QAC7C,CAAC,EAAE,QAAQ,SAAS,+CAA+C;AACjE,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC,EAAE,oBAAoB,SAAS,2DAA2D;AACzF,iBAAO,IAAI,kBAAkB;AAAA,QAC/B,CAAC,EAAE,kBAAkB,SAAS,uDAAuD,QAAQ;AAC3F,iBAAO,IAAI,gBAAgB,OAAO,OAAO,KAAK;AAAA,QAChD,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,sBAAsB,CAAC,GAAM,0BAA0B;AAAA,EAC3F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA;AAAA;AAAA;AAAA,MAIV,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,MACtB;AAAA,MACA,WAAW,CAAC,sBAAsB;AAAA,MAClC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,kBAAkB,OAAO;AAChC,SAAO,SAAS,QAAQ,aAAa,KAAK,MAAM;AAClD;AAOA,SAAS,aAAa,OAAO;AAE3B,MAAI,SAAS,MAAM;AACjB,WAAO;AAAA,EACT,WAAW,MAAM,QAAQ,KAAK,KAAK,OAAO,UAAU,UAAU;AAC5D,WAAO,MAAM;AAAA,EACf,WAAW,iBAAiB,KAAK;AAC/B,WAAO,MAAM;AAAA,EACf;AACA,SAAO;AACT;AA6BA,IAAM,gBAAgB,IAAI,eAAe,YAAY,iBAAiB,EAAE;AA8BxE,IAAM,sBAAsB,IAAI,eAAe,YAAY,sBAAsB,EAAE;AA+BnF,IAAM,eAAe;AAYrB,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBf,OAAO,IAAI,KAAK;AACd,WAAO,aAAa,GAAG;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,OAAO,IAAI,KAAK;AACd,WAAO,aAAa,GAAG;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBA,OAAO,SAAS,SAAS;AACvB,WAAO,kBAAkB,OAAO;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,OAAO,aAAa,SAAS;AAC3B,WAAO,sBAAsB,OAAO;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqCA,OAAO,MAAM,SAAS;AACpB,WAAO,eAAe,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgCA,OAAO,UAAU,WAAW;AAC1B,WAAO,mBAAmB,SAAS;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6BA,OAAO,UAAU,WAAW;AAC1B,WAAO,mBAAmB,SAAS;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkDA,OAAO,QAAQ,SAAS;AACtB,WAAO,iBAAiB,OAAO;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,cAAc,SAAS;AAC5B,WAAO,cAAc;AAAA,EACvB;AAAA,EACA,OAAO,QAAQ,YAAY;AACzB,WAAO,QAAQ,UAAU;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,aAAa,YAAY;AAC9B,WAAO,aAAa,UAAU;AAAA,EAChC;AACF;AAKA,SAAS,aAAa,KAAK;AACzB,SAAO,aAAW;AAChB,QAAI,QAAQ,SAAS,QAAQ,OAAO,MAAM;AACxC,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,WAAW,QAAQ,KAAK;AAGtC,WAAO,CAAC,MAAM,KAAK,KAAK,QAAQ,MAAM;AAAA,MACpC,OAAO;AAAA,QACL,OAAO;AAAA,QACP,UAAU,QAAQ;AAAA,MACpB;AAAA,IACF,IAAI;AAAA,EACN;AACF;AAKA,SAAS,aAAa,KAAK;AACzB,SAAO,aAAW;AAChB,QAAI,QAAQ,SAAS,QAAQ,OAAO,MAAM;AACxC,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,WAAW,QAAQ,KAAK;AAGtC,WAAO,CAAC,MAAM,KAAK,KAAK,QAAQ,MAAM;AAAA,MACpC,OAAO;AAAA,QACL,OAAO;AAAA,QACP,UAAU,QAAQ;AAAA,MACpB;AAAA,IACF,IAAI;AAAA,EACN;AACF;AAKA,SAAS,kBAAkB,SAAS;AAClC,SAAO,kBAAkB,QAAQ,KAAK,IAAI;AAAA,IACxC,YAAY;AAAA,EACd,IAAI;AACN;AAMA,SAAS,sBAAsB,SAAS;AACtC,SAAO,QAAQ,UAAU,OAAO,OAAO;AAAA,IACrC,YAAY;AAAA,EACd;AACF;AAKA,SAAS,eAAe,SAAS;AAC/B,MAAI,kBAAkB,QAAQ,KAAK,GAAG;AACpC,WAAO;AAAA,EACT;AACA,SAAO,aAAa,KAAK,QAAQ,KAAK,IAAI,OAAO;AAAA,IAC/C,SAAS;AAAA,EACX;AACF;AAQA,SAAS,mBAAmB,WAAW;AACrC,SAAO,aAAW;AAChB,UAAM,SAAS,QAAQ,OAAO,UAAU,aAAa,QAAQ,KAAK;AAClE,QAAI,WAAW,QAAQ,WAAW,GAAG;AAGnC,aAAO;AAAA,IACT;AACA,WAAO,SAAS,YAAY;AAAA,MAC1B,aAAa;AAAA,QACX,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,MAClB;AAAA,IACF,IAAI;AAAA,EACN;AACF;AAQA,SAAS,mBAAmB,WAAW;AACrC,SAAO,aAAW;AAChB,UAAM,SAAS,QAAQ,OAAO,UAAU,aAAa,QAAQ,KAAK;AAClE,QAAI,WAAW,QAAQ,SAAS,WAAW;AACzC,aAAO;AAAA,QACL,aAAa;AAAA,UACX,kBAAkB;AAAA,UAClB,gBAAgB;AAAA,QAClB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAKA,SAAS,iBAAiB,SAAS;AACjC,MAAI,CAAC,QAAS,QAAO;AACrB,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,YAAY,UAAU;AAC/B,eAAW;AACX,QAAI,QAAQ,OAAO,CAAC,MAAM,IAAK,aAAY;AAC3C,gBAAY;AACZ,QAAI,QAAQ,OAAO,QAAQ,SAAS,CAAC,MAAM,IAAK,aAAY;AAC5D,YAAQ,IAAI,OAAO,QAAQ;AAAA,EAC7B,OAAO;AACL,eAAW,QAAQ,SAAS;AAC5B,YAAQ;AAAA,EACV;AACA,SAAO,aAAW;AAChB,QAAI,kBAAkB,QAAQ,KAAK,GAAG;AACpC,aAAO;AAAA,IACT;AACA,UAAM,QAAQ,QAAQ;AACtB,WAAO,MAAM,KAAK,KAAK,IAAI,OAAO;AAAA,MAChC,WAAW;AAAA,QACT,mBAAmB;AAAA,QACnB,eAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AACF;AAIA,SAAS,cAAc,SAAS;AAC9B,SAAO;AACT;AACA,SAAS,UAAU,GAAG;AACpB,SAAO,KAAK;AACd;AACA,SAAS,aAAa,OAAO;AAC3B,QAAM,MAAM,UAAW,KAAK,IAAI,KAAK,KAAK,IAAI;AAC9C,OAAK,OAAO,cAAc,eAAe,cAAc,CAAC,eAAgB,GAAG,GAAG;AAC5E,QAAI,eAAe;AAEnB,QAAI,OAAO,UAAU,UAAU;AAC7B,sBAAgB;AAAA,IAClB;AACA,UAAM,IAAI,aAAc,OAA0D,YAAY;AAAA,EAChG;AACA,SAAO;AACT;AACA,SAAS,YAAY,eAAe;AAClC,MAAI,MAAM,CAAC;AACX,gBAAc,QAAQ,YAAU;AAC9B,UAAM,UAAU,OAAO,kCAClB,MACA,UACD;AAAA,EACN,CAAC;AACD,SAAO,OAAO,KAAK,GAAG,EAAE,WAAW,IAAI,OAAO;AAChD;AACA,SAAS,kBAAkB,SAAS,YAAY;AAC9C,SAAO,WAAW,IAAI,eAAa,UAAU,OAAO,CAAC;AACvD;AACA,SAAS,cAAc,WAAW;AAChC,SAAO,CAAC,UAAU;AACpB;AASA,SAAS,oBAAoB,YAAY;AACvC,SAAO,WAAW,IAAI,eAAa;AACjC,WAAO,cAAc,SAAS,IAAI,YAAY,OAAK,UAAU,SAAS,CAAC;AAAA,EACzE,CAAC;AACH;AAKA,SAAS,QAAQ,YAAY;AAC3B,MAAI,CAAC,WAAY,QAAO;AACxB,QAAM,oBAAoB,WAAW,OAAO,SAAS;AACrD,MAAI,kBAAkB,UAAU,EAAG,QAAO;AAC1C,SAAO,SAAU,SAAS;AACxB,WAAO,YAAY,kBAAkB,SAAS,iBAAiB,CAAC;AAAA,EAClE;AACF;AAMA,SAAS,kBAAkB,YAAY;AACrC,SAAO,cAAc,OAAO,QAAQ,oBAAoB,UAAU,CAAC,IAAI;AACzE;AAKA,SAAS,aAAa,YAAY;AAChC,MAAI,CAAC,WAAY,QAAO;AACxB,QAAM,oBAAoB,WAAW,OAAO,SAAS;AACrD,MAAI,kBAAkB,UAAU,EAAG,QAAO;AAC1C,SAAO,SAAU,SAAS;AACxB,UAAM,cAAc,kBAAkB,SAAS,iBAAiB,EAAE,IAAI,YAAY;AAClF,WAAO,SAAS,WAAW,EAAE,KAAK,IAAI,WAAW,CAAC;AAAA,EACpD;AACF;AAMA,SAAS,uBAAuB,YAAY;AAC1C,SAAO,cAAc,OAAO,aAAa,oBAAoB,UAAU,CAAC,IAAI;AAC9E;AAKA,SAAS,gBAAgB,mBAAmB,cAAc;AACxD,MAAI,sBAAsB,KAAM,QAAO,CAAC,YAAY;AACpD,SAAO,MAAM,QAAQ,iBAAiB,IAAI,CAAC,GAAG,mBAAmB,YAAY,IAAI,CAAC,mBAAmB,YAAY;AACnH;AAIA,SAAS,qBAAqB,SAAS;AACrC,SAAO,QAAQ;AACjB;AAIA,SAAS,0BAA0B,SAAS;AAC1C,SAAO,QAAQ;AACjB;AAQA,SAAS,oBAAoB,YAAY;AACvC,MAAI,CAAC,WAAY,QAAO,CAAC;AACzB,SAAO,MAAM,QAAQ,UAAU,IAAI,aAAa,CAAC,UAAU;AAC7D;AAQA,SAAS,aAAa,YAAY,WAAW;AAC3C,SAAO,MAAM,QAAQ,UAAU,IAAI,WAAW,SAAS,SAAS,IAAI,eAAe;AACrF;AAQA,SAAS,cAAc,YAAY,mBAAmB;AACpD,QAAM,UAAU,oBAAoB,iBAAiB;AACrD,QAAM,kBAAkB,oBAAoB,UAAU;AACtD,kBAAgB,QAAQ,OAAK;AAK3B,QAAI,CAAC,aAAa,SAAS,CAAC,GAAG;AAC7B,cAAQ,KAAK,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,iBAAiB,YAAY,mBAAmB;AACvD,SAAO,oBAAoB,iBAAiB,EAAE,OAAO,OAAK,CAAC,aAAa,YAAY,CAAC,CAAC;AACxF;AAUA,IAAM,2BAAN,MAA+B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK,QAAQ,QAAQ;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK,QAAQ,QAAQ;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,KAAK,QAAQ,UAAU;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,KAAK,QAAQ,UAAU;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,KAAK,QAAQ,WAAW;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,KAAK,QAAQ,UAAU;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,SAAS;AACX,WAAO,KAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,KAAK,QAAQ,WAAW;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ;AACV,WAAO,KAAK,UAAU,KAAK,QAAQ,QAAQ;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,KAAK,QAAQ,UAAU;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,SAAS;AACX,WAAO,KAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAY;AACd,WAAO,KAAK,UAAU,KAAK,QAAQ,YAAY;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU,KAAK,QAAQ,gBAAgB;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,eAAe;AACjB,WAAO,KAAK,UAAU,KAAK,QAAQ,eAAe;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlB,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,eAAe,YAAY;AACzB,SAAK,iBAAiB,cAAc,CAAC;AACrC,SAAK,uBAAuB,kBAAkB,KAAK,cAAc;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,YAAY;AAC9B,SAAK,sBAAsB,cAAc,CAAC;AAC1C,SAAK,4BAA4B,uBAAuB,KAAK,mBAAmB;AAAA,EAClF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAY;AACd,WAAO,KAAK,wBAAwB;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,iBAAiB;AACnB,WAAO,KAAK,6BAA6B;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,mBAAmB,IAAI;AACrB,SAAK,oBAAoB,KAAK,EAAE;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,4BAA4B;AAC1B,SAAK,oBAAoB,QAAQ,QAAM,GAAG,CAAC;AAC3C,SAAK,sBAAsB,CAAC;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ,QAAW;AACvB,QAAI,KAAK,QAAS,MAAK,QAAQ,MAAM,KAAK;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,SAAS,WAAW,MAAM;AACxB,WAAO,KAAK,UAAU,KAAK,QAAQ,SAAS,WAAW,IAAI,IAAI;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BA,SAAS,WAAW,MAAM;AACxB,WAAO,KAAK,UAAU,KAAK,QAAQ,SAAS,WAAW,IAAI,IAAI;AAAA,EACjE;AACF;AASA,IAAM,mBAAN,cAA+B,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO;AAAA,EACT;AACF;AASA,IAAM,YAAN,cAAwB,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO/C,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,gBAAgB;AAClB;AAMA,IAAM,wBAAN,MAA4B;AAAA,EAC1B;AAAA,EACA,YAAY,IAAI;AACd,SAAK,MAAM;AAAA,EACb;AAAA,EACA,IAAI,YAAY;AAEd,SAAK,KAAK,SAAS,WAAW;AAC9B,WAAO,CAAC,CAAC,KAAK,KAAK,SAAS;AAAA,EAC9B;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,CAAC,CAAC,KAAK,KAAK,SAAS;AAAA,EAC9B;AAAA,EACA,IAAI,aAAa;AAEf,SAAK,KAAK,SAAS,YAAY;AAC/B,WAAO,CAAC,CAAC,KAAK,KAAK,SAAS;AAAA,EAC9B;AAAA,EACA,IAAI,UAAU;AAEZ,WAAO,CAAC,CAAC,KAAK,KAAK,SAAS;AAAA,EAC9B;AAAA,EACA,IAAI,UAAU;AAEZ,SAAK,KAAK,SAAS,UAAU;AAC7B,WAAO,CAAC,CAAC,KAAK,KAAK,SAAS;AAAA,EAC9B;AAAA,EACA,IAAI,YAAY;AAEd,WAAO,CAAC,CAAC,KAAK,KAAK,SAAS;AAAA,EAC9B;AAAA,EACA,IAAI,YAAY;AAEd,WAAO,CAAC,CAAC,KAAK,KAAK,SAAS;AAAA,EAC9B;AAAA,EACA,IAAI,cAAc;AAEhB,SAAK,KAAK,aAAa;AAGvB,WAAO,CAAC,CAAC,KAAK,KAAK;AAAA,EACrB;AACF;AACA,IAAM,sBAAsB;AAAA,EAC1B,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,sBAAsB;AACxB;AACA,IAAM,oBAAoB,iCACrB,sBADqB;AAAA,EAExB,wBAAwB;AAC1B;AAwBA,IAAM,kBAAN,MAAM,yBAAwB,sBAAsB;AAAA,EAClD,YAAY,IAAI;AACd,UAAM,EAAE;AAAA,EACV;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,kBAAkB,WAAW,CAAC,CAAC;AAAA,EACtF;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,GAAG,CAAC,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IACrF,UAAU;AAAA,IACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,IAAI,WAAW,EAAE,cAAc,IAAI,SAAS,EAAE,eAAe,IAAI,UAAU,EAAE,YAAY,IAAI,OAAO,EAAE,YAAY,IAAI,OAAO,EAAE,cAAc,IAAI,SAAS,EAAE,cAAc,IAAI,SAAS;AAAA,MACxN;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAaH,IAAM,uBAAN,MAAM,8BAA6B,sBAAsB;AAAA,EACvD,YAAY,IAAI;AACd,UAAM,EAAE;AAAA,EACV;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,kBAAkB,kBAAkB,EAAE,CAAC;AAAA,EACnG;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,aAAa,EAAE,GAAG,CAAC,QAAQ,GAAG,YAAY,EAAE,GAAG,CAAC,IAAI,UAAU,EAAE,CAAC;AAAA,IAClK,UAAU;AAAA,IACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,gBAAgB,IAAI,WAAW,EAAE,cAAc,IAAI,SAAS,EAAE,eAAe,IAAI,UAAU,EAAE,YAAY,IAAI,OAAO,EAAE,YAAY,IAAI,OAAO,EAAE,cAAc,IAAI,SAAS,EAAE,cAAc,IAAI,SAAS,EAAE,gBAAgB,IAAI,WAAW;AAAA,MACzP;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAU/B,IAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAY7B,IAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAe7B,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAM5B,IAAM,8BAA8B;AAAA;AAAA;AAAA;AAAA;AAAA;AAMpC,SAAS,uBAAuB,aAAa;AAC3C,SAAO,IAAI,aAAc,MAA8D;AAAA;AAAA;AAAA,QAGjF,oBAAoB,WAAW,CAAC;AAAA;AAAA;AAAA;AAAA,MAIlC,sBAAsB,EAAE;AAC9B;AACA,SAAS,oBAAoB,aAAa;AACxC,MAAI,eAAe,QAAQ,gBAAgB,IAAI;AAC7C,WAAO;AAAA,EACT;AACA,QAAM,YAAY,OAAO,gBAAgB,WAAW,SAAS;AAC7D,SAAO,yBAAyB,SAAS,MAAM,WAAW;AAC5D;AACA,SAAS,wBAAwB;AAC/B,SAAO,IAAI,aAAc,MAAkE;AAAA;AAAA;AAAA;AAAA;AAAA,QAKrF,oBAAoB;AAAA;AAAA;AAAA;AAAA,QAIpB,mBAAmB,EAAE;AAC7B;AACA,SAAS,uBAAuB;AAC9B,SAAO,IAAI,aAAc,MAAyD;AAAA;AAAA;AAAA;AAAA,QAI5E,sBAAsB,EAAE;AAChC;AACA,SAAS,uBAAuB;AAC9B,SAAO,IAAI,aAAc,MAA4D;AAAA;AAAA;AAAA;AAAA;AAAA,MAKjF,oBAAoB,EAAE;AAC5B;AACA,SAAS,uBAAuB;AAC9B,SAAO,IAAI,aAAc,MAA4D;AAAA;AAAA;AAAA;AAAA;AAAA,QAK/E,oBAAoB,EAAE;AAC9B;AACA,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgB5B,IAAM,wCAAwC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAe9C,SAAS,eAAe,eAAe;AACrC,SAAO;AAAA,iEACwD,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCAM7C,kBAAkB,gBAAgB,yBAAyB,iBAAiB;AAAA;AAE7G;AACA,SAAS,YAAYA,cAAa,KAAK;AACrC,SAAOA,eAAc,eAAe,GAAG,MAAM,aAAa,GAAG;AAC/D;AACA,SAAS,gBAAgBA,cAAa;AACpC,SAAO;AAAA,sDAC6CA,eAAc,UAAU,OAAO;AAAA;AAAA;AAGrF;AACA,SAAS,oBAAoBA,cAAa,KAAK;AAC7C,SAAO,4BAA4B,YAAYA,cAAa,GAAG,CAAC;AAClE;AACA,SAAS,yBAAyBA,cAAa,KAAK;AAClD,SAAO,wCAAwC,YAAYA,cAAa,GAAG,CAAC;AAC9E;AAOA,IAAM,QAAQ;AAMd,IAAM,UAAU;AAQhB,IAAM,UAAU;AAQhB,IAAM,WAAW;AAMjB,IAAM,eAAN,MAAmB;AAAC;AAMpB,IAAM,mBAAN,cAA+B,aAAa;AAAA,EAC1C;AAAA,EACA;AAAA,EACA,YAAY,OAAO,QAAQ;AACzB,UAAM;AACN,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAChB;AACF;AAKA,IAAM,sBAAN,cAAkC,aAAa;AAAA,EAC7C;AAAA,EACA;AAAA,EACA,YAAY,UAAU,QAAQ;AAC5B,UAAM;AACN,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AACF;AAMA,IAAM,qBAAN,cAAiC,aAAa;AAAA,EAC5C;AAAA,EACA;AAAA,EACA,YAAY,SAAS,QAAQ;AAC3B,UAAM;AACN,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAChB;AACF;AAMA,IAAM,oBAAN,cAAgC,aAAa;AAAA,EAC3C;AAAA,EACA;AAAA,EACA,YAAY,QAAQ,QAAQ;AAC1B,UAAM;AACN,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AACF;AAMA,IAAM,qBAAN,cAAiC,aAAa;AAAA,EAC5C;AAAA,EACA,YAAY,QAAQ;AAClB,UAAM;AACN,SAAK,SAAS;AAAA,EAChB;AACF;AAMA,IAAM,iBAAN,cAA6B,aAAa;AAAA,EACxC;AAAA,EACA,YAAY,QAAQ;AAClB,UAAM;AACN,SAAK,SAAS;AAAA,EAChB;AACF;AAIA,SAAS,eAAe,iBAAiB;AACvC,UAAQ,aAAa,eAAe,IAAI,gBAAgB,aAAa,oBAAoB;AAC3F;AAIA,SAAS,kBAAkB,WAAW;AACpC,SAAO,MAAM,QAAQ,SAAS,IAAI,kBAAkB,SAAS,IAAI,aAAa;AAChF;AAIA,SAAS,oBAAoB,gBAAgB,iBAAiB;AAC5D,MAAI,OAAO,cAAc,eAAe,WAAW;AACjD,QAAI,aAAa,eAAe,KAAK,gBAAgB;AACnD,cAAQ,KAAK,qCAAqC;AAAA,IACpD;AAAA,EACF;AACA,UAAQ,aAAa,eAAe,IAAI,gBAAgB,kBAAkB,mBAAmB;AAC/F;AAIA,SAAS,uBAAuB,gBAAgB;AAC9C,SAAO,MAAM,QAAQ,cAAc,IAAI,uBAAuB,cAAc,IAAI,kBAAkB;AACpG;AACA,SAAS,aAAa,iBAAiB;AACrC,SAAO,mBAAmB,QAAQ,CAAC,MAAM,QAAQ,eAAe,KAAK,OAAO,oBAAoB;AAClG;AACA,SAAS,qBAAqB,QAAQ,SAAS,KAAK;AAClD,QAAM,WAAW,OAAO;AACxB,QAAM,aAAa,UAAU,OAAO,KAAK,QAAQ,IAAI;AACrD,MAAI,CAAC,WAAW,QAAQ;AACtB,UAAM,IAAI,aAAc,KAAyC,OAAO,cAAc,eAAe,YAAY,gBAAgB,OAAO,IAAI,EAAE;AAAA,EAChJ;AACA,MAAI,CAAC,SAAS,GAAG,GAAG;AAClB,UAAM,IAAI,aAAc,MAA6C,OAAO,cAAc,eAAe,YAAY,oBAAoB,SAAS,GAAG,IAAI,EAAE;AAAA,EAC7J;AACF;AACA,SAAS,uBAAuB,SAAS,SAAS,OAAO;AACvD,UAAQ,cAAc,CAAC,GAAG,QAAQ;AAChC,QAAI,MAAM,GAAG,MAAM,QAAW;AAC5B,YAAM,IAAI,aAAc,MAAmD,OAAO,cAAc,eAAe,YAAY,yBAAyB,SAAS,GAAG,IAAI,EAAE;AAAA,IACxK;AAAA,EACF,CAAC;AACH;AAkBA,IAAM,kBAAN,MAAsB;AAAA;AAAA,EAEpB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhB,+BAA+B;AAAA;AAAA,EAE/B,kBAAkB;AAAA;AAAA,EAElB,sBAAsB,MAAM;AAAA,EAAC;AAAA;AAAA,EAE7B;AAAA,EACA,UAAU;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,YAAY,iBAAiB;AACvC,SAAK,kBAAkB,UAAU;AACjC,SAAK,uBAAuB,eAAe;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,aAAa;AACzB,SAAK,iBAAiB,KAAK,uBAAuB;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,iBAAiB;AACnB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,eAAe,kBAAkB;AACnC,SAAK,sBAAsB,KAAK,4BAA4B;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,SAAS;AACX,WAAO,UAAU,KAAK,cAAc;AAAA,EACtC;AAAA,EACA,IAAI,OAAO,GAAG;AACZ,cAAU,MAAM,KAAK,eAAe,IAAI,CAAC,CAAC;AAAA,EAC5C;AAAA;AAAA,EAEA,UAAU,SAAS,MAAM,KAAK,eAAe,GAAG,GAAI,YAAY,CAAC;AAAA,IAC/D,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR,iBAAiB,OAAO,QAAW,GAAI,YAAY,CAAC;AAAA,IAClD,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASR,IAAI,QAAQ;AACV,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,UAAU;AACZ,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,IAAI,WAAW;AACb,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,UAAU;AACZ,WAAO,KAAK,WAAW;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,WAAW;AACb,WAAO,UAAU,KAAK,gBAAgB;AAAA,EACxC;AAAA,EACA,IAAI,SAAS,GAAG;AACd,cAAU,MAAM,KAAK,iBAAiB,IAAI,CAAC,CAAC;AAAA,EAC9C;AAAA;AAAA,EAEA,YAAY,SAAS,MAAM,KAAK,iBAAiB,GAAG,GAAI,YAAY,CAAC;AAAA,IACnE,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR,mBAAmB,OAAO,MAAM,GAAI,YAAY,CAAC;AAAA,IAC/C,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQR,IAAI,QAAQ;AACV,WAAO,CAAC,KAAK;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,UAAU;AACZ,WAAO,UAAU,KAAK,eAAe;AAAA,EACvC;AAAA,EACA,IAAI,QAAQ,GAAG;AACb,cAAU,MAAM,KAAK,gBAAgB,IAAI,CAAC,CAAC;AAAA,EAC7C;AAAA;AAAA,EAEA,WAAW,SAAS,MAAM,KAAK,gBAAgB,GAAG,GAAI,YAAY,CAAC;AAAA,IACjE,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR,kBAAkB,OAAO,OAAO,GAAI,YAAY,CAAC;AAAA,IAC/C,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOR,IAAI,YAAY;AACd,WAAO,CAAC,KAAK;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,IAAI,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAatB,SAAS,KAAK,QAAQ,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WAAW;AACb,WAAO,KAAK,YAAY,KAAK,YAAY,KAAK,SAAS,KAAK,OAAO,WAAW;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,cAAc,YAAY;AACxB,SAAK,kBAAkB,UAAU;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,mBAAmB,YAAY;AAC7B,SAAK,uBAAuB,UAAU;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,cAAc,YAAY;AACxB,SAAK,cAAc,cAAc,YAAY,KAAK,cAAc,CAAC;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,mBAAmB,YAAY;AAC7B,SAAK,mBAAmB,cAAc,YAAY,KAAK,mBAAmB,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8BA,iBAAiB,YAAY;AAC3B,SAAK,cAAc,iBAAiB,YAAY,KAAK,cAAc,CAAC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,sBAAsB,YAAY;AAChC,SAAK,mBAAmB,iBAAiB,YAAY,KAAK,mBAAmB,CAAC;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBA,aAAa,WAAW;AACtB,WAAO,aAAa,KAAK,gBAAgB,SAAS;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,kBAAkB,WAAW;AAC3B,WAAO,aAAa,KAAK,qBAAqB,SAAS;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,kBAAkB;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,uBAAuB;AACrB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc,OAAO,CAAC,GAAG;AACvB,UAAM,UAAU,KAAK,YAAY;AACjC,SAAK,UAAU;AACf,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,QAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,WAAK,QAAQ,cAAc,iCACtB,OADsB;AAAA,QAEzB;AAAA,MACF,EAAC;AAAA,IACH;AACA,QAAI,WAAW,KAAK,cAAc,OAAO;AACvC,WAAK,QAAQ,KAAK,IAAI,mBAAmB,MAAM,aAAa,CAAC;AAAA,IAC/D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,OAAO,CAAC,GAAG;AACxB,SAAK,YAAY;AAAA,MACf,UAAU;AAAA,MACV,WAAW,KAAK;AAAA,MAChB,eAAe;AAAA,IACjB,CAAC;AACD,SAAK,cAAc,aAAW,QAAQ,eAAe,IAAI,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,iBAAiB,OAAO,CAAC,GAAG;AAC1B,SAAK,cAAc;AAAA,MACjB,UAAU;AAAA,MACV,WAAW,KAAK;AAAA,MAChB,eAAe;AAAA,IACjB,CAAC;AACD,SAAK,cAAc,aAAW,QAAQ,iBAAiB,IAAI,CAAC;AAAA,EAC9D;AAAA,EACA,gBAAgB,OAAO,CAAC,GAAG;AACzB,UAAM,UAAU,KAAK,YAAY;AACjC,SAAK,UAAU;AACf,SAAK,kBAAkB;AACvB,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,SAAK,cAAc,aAAW;AAC5B,cAAQ,gBAAgB;AAAA,QACtB,UAAU;AAAA,QACV,WAAW,KAAK;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,QAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,WAAK,QAAQ,eAAe,MAAM,aAAa;AAAA,IACjD;AACA,QAAI,WAAW,KAAK,cAAc,OAAO;AACvC,WAAK,QAAQ,KAAK,IAAI,mBAAmB,OAAO,aAAa,CAAC;AAAA,IAChE;AAAA,EACF;AAAA,EACA,YAAY,OAAO,CAAC,GAAG;AACrB,UAAM,UAAU,KAAK,aAAa;AAClC,SAAK,WAAW;AAChB,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,QAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,WAAK,QAAQ,YAAY,iCACpB,OADoB;AAAA,QAEvB;AAAA,MACF,EAAC;AAAA,IACH;AACA,QAAI,WAAW,KAAK,cAAc,OAAO;AACvC,WAAK,QAAQ,KAAK,IAAI,oBAAoB,OAAO,aAAa,CAAC;AAAA,IACjE;AAAA,EACF;AAAA,EACA,eAAe,OAAO,CAAC,GAAG;AACxB,UAAM,UAAU,KAAK,aAAa;AAClC,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,SAAK,cAAc,aAAW;AAE5B,cAAQ,eAAe;AAAA,QACrB,UAAU;AAAA,QACV,WAAW,KAAK;AAAA,MAClB,CAAC;AAAA,IACH,CAAC;AACD,QAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,WAAK,QAAQ,gBAAgB,MAAM,aAAa;AAAA,IAClD;AACA,QAAI,WAAW,KAAK,cAAc,OAAO;AACvC,WAAK,QAAQ,KAAK,IAAI,oBAAoB,MAAM,aAAa,CAAC;AAAA,IAChE;AAAA,EACF;AAAA,EACA,cAAc,OAAO,CAAC,GAAG;AACvB,SAAK,SAAS;AACd,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,QAAQ,KAAK,IAAI,kBAAkB,KAAK,QAAQ,aAAa,CAAC;AACnE,WAAK,cAAc,KAAK,KAAK,MAAM;AAAA,IACrC;AACA,QAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,WAAK,QAAQ,cAAc,iCACtB,OADsB;AAAA,QAEzB;AAAA,MACF,EAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ,OAAO,CAAC,GAAG;AAGjB,UAAM,oBAAoB,KAAK,mBAAmB,KAAK,QAAQ;AAC/D,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,cAAc,aAAW;AAE5B,cAAQ,QAAQ,iCACX,OADW;AAAA,QAEd,UAAU;AAAA,MACZ,EAAC;AAAA,IACH,CAAC;AACD,SAAK,aAAa;AAClB,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,QAAQ,KAAK,IAAI,iBAAiB,KAAK,OAAO,aAAa,CAAC;AACjE,WAAK,QAAQ,KAAK,IAAI,kBAAkB,KAAK,QAAQ,aAAa,CAAC;AACnE,WAAK,aAAa,KAAK,KAAK,KAAK;AACjC,WAAK,cAAc,KAAK,KAAK,MAAM;AAAA,IACrC;AACA,SAAK,iBAAiB,iCACjB,OADiB;AAAA,MAEpB;AAAA,IACF,IAAG,IAAI;AACP,SAAK,kBAAkB,QAAQ,cAAY,SAAS,IAAI,CAAC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,OAAO,OAAO,CAAC,GAAG;AAGhB,UAAM,oBAAoB,KAAK,mBAAmB,KAAK,QAAQ;AAC/D,SAAK,SAAS;AACd,SAAK,cAAc,aAAW;AAC5B,cAAQ,OAAO,iCACV,OADU;AAAA,QAEb,UAAU;AAAA,MACZ,EAAC;AAAA,IACH,CAAC;AACD,SAAK,uBAAuB;AAAA,MAC1B,UAAU;AAAA,MACV,WAAW,KAAK;AAAA,IAClB,CAAC;AACD,SAAK,iBAAiB,iCACjB,OADiB;AAAA,MAEpB;AAAA,IACF,IAAG,IAAI;AACP,SAAK,kBAAkB,QAAQ,cAAY,SAAS,KAAK,CAAC;AAAA,EAC5D;AAAA,EACA,iBAAiB,MAAM,eAAe;AACpC,QAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,WAAK,QAAQ,uBAAuB,IAAI;AACxC,UAAI,CAAC,KAAK,mBAAmB;AAC3B,aAAK,QAAQ,gBAAgB,CAAC,GAAG,aAAa;AAAA,MAChD;AACA,WAAK,QAAQ,eAAe,CAAC,GAAG,aAAa;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,QAAQ;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,uBAAuB,OAAO,CAAC,GAAG;AAChC,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,QAAI,KAAK,SAAS;AAChB,YAAM,oBAAoB,KAAK,4BAA4B;AAC3D,WAAK,SAAS,KAAK,cAAc;AACjC,WAAK,SAAS,KAAK,iBAAiB;AACpC,UAAI,KAAK,WAAW,SAAS,KAAK,WAAW,SAAS;AAGpD,aAAK,mBAAmB,mBAAmB,KAAK,SAAS;AAAA,MAC3D;AAAA,IACF;AACA,UAAM,gBAAgB,KAAK,iBAAiB;AAC5C,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,QAAQ,KAAK,IAAI,iBAAiB,KAAK,OAAO,aAAa,CAAC;AACjE,WAAK,QAAQ,KAAK,IAAI,kBAAkB,KAAK,QAAQ,aAAa,CAAC;AACnE,WAAK,aAAa,KAAK,KAAK,KAAK;AACjC,WAAK,cAAc,KAAK,KAAK,MAAM;AAAA,IACrC;AACA,QAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,WAAK,QAAQ,uBAAuB,iCAC/B,OAD+B;AAAA,QAElC;AAAA,MACF,EAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB,OAAO;AAAA,IACzB,WAAW;AAAA,EACb,GAAG;AACD,SAAK,cAAc,UAAQ,KAAK,oBAAoB,IAAI,CAAC;AACzD,SAAK,uBAAuB;AAAA,MAC1B,UAAU;AAAA,MACV,WAAW,KAAK;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB;AAClB,SAAK,SAAS,KAAK,qBAAqB,IAAI,WAAW;AAAA,EACzD;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK,YAAY,KAAK,UAAU,IAAI,IAAI;AAAA,EACjD;AAAA,EACA,mBAAmB,mBAAmB,WAAW;AAC/C,QAAI,KAAK,gBAAgB;AACvB,WAAK,SAAS;AACd,WAAK,+BAA+B;AAAA,QAClC,WAAW,cAAc;AAAA,QACzB,mBAAmB,sBAAsB;AAAA,MAC3C;AACA,YAAM,MAAM,aAAa,KAAK,eAAe,IAAI,CAAC;AAClD,WAAK,+BAA+B,IAAI,UAAU,YAAU;AAC1D,aAAK,+BAA+B;AAIpC,aAAK,UAAU,QAAQ;AAAA,UACrB;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,8BAA8B;AAC5B,QAAI,KAAK,8BAA8B;AACrC,WAAK,6BAA6B,YAAY;AAG9C,YAAM,qBAAqB,KAAK,8BAA8B,aAAa,KAAK,8BAA8B,sBAAsB;AACpI,WAAK,+BAA+B;AACpC,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,QAAQ,OAAO,CAAC,GAAG;AAC3B,SAAK,SAAS;AACd,SAAK,sBAAsB,KAAK,cAAc,OAAO,MAAM,KAAK,iBAAiB;AAAA,EACnF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,IAAI,MAAM;AACR,QAAI,WAAW;AACf,QAAI,YAAY,KAAM,QAAO;AAC7B,QAAI,CAAC,MAAM,QAAQ,QAAQ,EAAG,YAAW,SAAS,MAAM,GAAG;AAC3D,QAAI,SAAS,WAAW,EAAG,QAAO;AAClC,WAAO,SAAS,OAAO,CAAC,SAAS,SAAS,WAAW,QAAQ,MAAM,IAAI,GAAG,IAAI;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BA,SAAS,WAAW,MAAM;AACxB,UAAM,UAAU,OAAO,KAAK,IAAI,IAAI,IAAI;AACxC,WAAO,WAAW,QAAQ,SAAS,QAAQ,OAAO,SAAS,IAAI;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,SAAS,WAAW,MAAM;AACxB,WAAO,CAAC,CAAC,KAAK,SAAS,WAAW,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,QAAI,IAAI;AACR,WAAO,EAAE,SAAS;AAChB,UAAI,EAAE;AAAA,IACR;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,sBAAsB,WAAW,gBAAgB,mBAAmB;AAClE,SAAK,SAAS,KAAK,iBAAiB;AACpC,QAAI,WAAW;AACb,WAAK,cAAc,KAAK,KAAK,MAAM;AAAA,IACrC;AAIA,QAAI,aAAa,mBAAmB;AAClC,WAAK,QAAQ,KAAK,IAAI,kBAAkB,KAAK,QAAQ,cAAc,CAAC;AAAA,IACtE;AACA,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,sBAAsB,WAAW,gBAAgB,iBAAiB;AAAA,IACjF;AAAA,EACF;AAAA;AAAA,EAEA,mBAAmB;AAEjB,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,gBAAgB,IAAI,aAAa;AAAA,EACxC;AAAA,EACA,mBAAmB;AACjB,QAAI,KAAK,qBAAqB,EAAG,QAAO;AACxC,QAAI,KAAK,OAAQ,QAAO;AACxB,QAAI,KAAK,gCAAgC,KAAK,uBAAuB,OAAO,EAAG,QAAO;AACtF,QAAI,KAAK,uBAAuB,OAAO,EAAG,QAAO;AACjD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,uBAAuB,QAAQ;AAC7B,WAAO,KAAK,aAAa,aAAW,QAAQ,WAAW,MAAM;AAAA,EAC/D;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,aAAa,aAAW,QAAQ,KAAK;AAAA,EACnD;AAAA;AAAA,EAEA,sBAAsB;AACpB,WAAO,KAAK,aAAa,aAAW,QAAQ,OAAO;AAAA,EACrD;AAAA;AAAA,EAEA,gBAAgB,MAAM,gBAAgB;AACpC,UAAM,cAAc,CAAC,KAAK,kBAAkB;AAC5C,UAAM,UAAU,KAAK,aAAa;AAClC,SAAK,WAAW;AAChB,QAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,WAAK,QAAQ,gBAAgB,MAAM,cAAc;AAAA,IACnD;AACA,QAAI,SAAS;AACX,WAAK,QAAQ,KAAK,IAAI,oBAAoB,KAAK,UAAU,cAAc,CAAC;AAAA,IAC1E;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,OAAO,CAAC,GAAG,gBAAgB;AACxC,SAAK,UAAU,KAAK,oBAAoB;AACxC,SAAK,QAAQ,KAAK,IAAI,mBAAmB,KAAK,SAAS,cAAc,CAAC;AACtE,QAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAClC,WAAK,QAAQ,eAAe,MAAM,cAAc;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB,CAAC;AAAA;AAAA,EAErB,4BAA4B,IAAI;AAC9B,SAAK,sBAAsB;AAAA,EAC7B;AAAA;AAAA,EAEA,mBAAmB,MAAM;AACvB,QAAI,aAAa,IAAI,KAAK,KAAK,YAAY,MAAM;AAC/C,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,UAAU;AAC3B,UAAM,cAAc,KAAK,WAAW,KAAK,QAAQ;AACjD,WAAO,CAAC,YAAY,CAAC,CAAC,eAAe,CAAC,KAAK,QAAQ,kBAAkB;AAAA,EACvE;AAAA;AAAA,EAEA,MAAM,MAAM;AACV,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,YAAY;AAC5B,SAAK,iBAAiB,MAAM,QAAQ,UAAU,IAAI,WAAW,MAAM,IAAI;AACvE,SAAK,uBAAuB,kBAAkB,KAAK,cAAc;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,YAAY;AACjC,SAAK,sBAAsB,MAAM,QAAQ,UAAU,IAAI,WAAW,MAAM,IAAI;AAC5E,SAAK,4BAA4B,uBAAuB,KAAK,mBAAmB;AAAA,EAClF;AACF;AAmHA,IAAM,YAAN,cAAwB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EActC,YAAY,UAAU,iBAAiB,gBAAgB;AACrD,UAAM,eAAe,eAAe,GAAG,oBAAoB,gBAAgB,eAAe,CAAC;AAC3F,KAAC,OAAO,cAAc,eAAe,cAAc,0BAA0B,QAAQ;AACrF,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB,eAAe;AACvC,SAAK,eAAe;AACpB,SAAK,uBAAuB;AAAA,MAC1B,UAAU;AAAA;AAAA;AAAA;AAAA,MAIV,WAAW,CAAC,CAAC,KAAK;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA;AAAA,EACA,gBAAgB,MAAM,SAAS;AAC7B,QAAI,KAAK,SAAS,IAAI,EAAG,QAAO,KAAK,SAAS,IAAI;AAClD,SAAK,SAAS,IAAI,IAAI;AACtB,YAAQ,UAAU,IAAI;AACtB,YAAQ,4BAA4B,KAAK,mBAAmB;AAC5D,WAAO;AAAA,EACT;AAAA,EACA,WAAW,MAAM,SAAS,UAAU,CAAC,GAAG;AACtC,SAAK,gBAAgB,MAAM,OAAO;AAClC,SAAK,uBAAuB;AAAA,MAC1B,WAAW,QAAQ;AAAA,IACrB,CAAC;AACD,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,cAAc,MAAM,UAAU,CAAC,GAAG;AAChC,QAAI,KAAK,SAAS,IAAI,EAAG,MAAK,SAAS,IAAI,EAAE,4BAA4B,MAAM;AAAA,IAAC,CAAC;AACjF,WAAO,KAAK,SAAS,IAAI;AACzB,SAAK,uBAAuB;AAAA,MAC1B,WAAW,QAAQ;AAAA,IACrB,CAAC;AACD,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,WAAW,MAAM,SAAS,UAAU,CAAC,GAAG;AACtC,QAAI,KAAK,SAAS,IAAI,EAAG,MAAK,SAAS,IAAI,EAAE,4BAA4B,MAAM;AAAA,IAAC,CAAC;AACjF,WAAO,KAAK,SAAS,IAAI;AACzB,QAAI,QAAS,MAAK,gBAAgB,MAAM,OAAO;AAC/C,SAAK,uBAAuB;AAAA,MAC1B,WAAW,QAAQ;AAAA,IACrB,CAAC;AACD,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,SAAS,aAAa;AACpB,WAAO,KAAK,SAAS,eAAe,WAAW,KAAK,KAAK,SAAS,WAAW,EAAE;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoCA,SAAS,OAAO,UAAU,CAAC,GAAG;AAC5B,2BAAuB,MAAM,MAAM,KAAK;AACxC,WAAO,KAAK,KAAK,EAAE,QAAQ,UAAQ;AACjC,2BAAqB,MAAM,MAAM,IAAI;AACrC,WAAK,SAAS,IAAI,EAAE,SAAS,MAAM,IAAI,GAAG;AAAA,QACxC,UAAU;AAAA,QACV,WAAW,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AACD,SAAK,uBAAuB,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgCA,WAAW,OAAO,UAAU,CAAC,GAAG;AAK9B,QAAI,SAAS,KAAwC;AACrD,WAAO,KAAK,KAAK,EAAE,QAAQ,UAAQ;AAGjC,YAAM,UAAU,KAAK,SAAS,IAAI;AAClC,UAAI,SAAS;AACX,gBAAQ;AAAA;AAAA,UAAoE,MAAM,IAAI;AAAA,UAAG;AAAA,YACvF,UAAU;AAAA,YACV,WAAW,QAAQ;AAAA,UACrB;AAAA,QAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,SAAK,uBAAuB,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0DA,MAAM,QAAQ,CAAC,GAAG,UAAU,CAAC,GAAG;AAC9B,SAAK,cAAc,CAAC,SAAS,SAAS;AACpC,cAAQ,MAAM,QAAQ,MAAM,IAAI,IAAI,MAAM;AAAA,QACxC,UAAU;AAAA,QACV,WAAW,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AACD,SAAK,gBAAgB,SAAS,IAAI;AAClC,SAAK,eAAe,SAAS,IAAI;AACjC,SAAK,uBAAuB,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,WAAO,KAAK,gBAAgB,CAAC,GAAG,CAAC,KAAK,SAAS,SAAS;AACtD,UAAI,IAAI,IAAI,QAAQ,YAAY;AAChC,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,uBAAuB;AACrB,QAAI,iBAAiB,KAAK,gBAAgB,OAAO,CAAC,SAAS,UAAU;AACnE,aAAO,MAAM,qBAAqB,IAAI,OAAO;AAAA,IAC/C,CAAC;AACD,QAAI,eAAgB,MAAK,uBAAuB;AAAA,MAC9C,UAAU;AAAA,IACZ,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,IAAI;AAChB,WAAO,KAAK,KAAK,QAAQ,EAAE,QAAQ,SAAO;AAIxC,YAAM,UAAU,KAAK,SAAS,GAAG;AACjC,iBAAW,GAAG,SAAS,GAAG;AAAA,IAC5B,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,cAAc,aAAW;AAC5B,cAAQ,UAAU,IAAI;AACtB,cAAQ,4BAA4B,KAAK,mBAAmB;AAAA,IAC9D,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,eAAe;AACb,SAAK,QAAQ,KAAK,aAAa;AAAA,EACjC;AAAA;AAAA,EAEA,aAAa,WAAW;AACtB,eAAW,CAAC,aAAa,OAAO,KAAK,OAAO,QAAQ,KAAK,QAAQ,GAAG;AAClE,UAAI,KAAK,SAAS,WAAW,KAAK,UAAU,OAAO,GAAG;AACpD,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,eAAe;AACb,QAAI,MAAM,CAAC;AACX,WAAO,KAAK,gBAAgB,KAAK,CAACC,MAAK,SAAS,SAAS;AACvD,UAAI,QAAQ,WAAW,KAAK,UAAU;AACpC,QAAAA,KAAI,IAAI,IAAI,QAAQ;AAAA,MACtB;AACA,aAAOA;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,gBAAgB,WAAW,IAAI;AAC7B,QAAI,MAAM;AACV,SAAK,cAAc,CAAC,SAAS,SAAS;AACpC,YAAM,GAAG,KAAK,SAAS,IAAI;AAAA,IAC7B,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,uBAAuB;AACrB,eAAW,eAAe,OAAO,KAAK,KAAK,QAAQ,GAAG;AACpD,UAAI,KAAK,SAAS,WAAW,EAAE,SAAS;AACtC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,OAAO,KAAK,KAAK,QAAQ,EAAE,SAAS,KAAK,KAAK;AAAA,EACvD;AAAA;AAAA,EAEA,MAAM,MAAM;AACV,WAAO,KAAK,SAAS,eAAe,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI;AAAA,EACpE;AACF;AAKA,SAAS,0BAA0B,UAAU;AAC3C,QAAM,cAAc,OAAO,KAAK,QAAQ,EAAE,OAAO,SAAO,IAAI,SAAS,GAAG,CAAC;AACzE,MAAI,YAAY,SAAS,GAAG;AAE1B,YAAQ,KAAK,qEAAqE,YAAY,KAAK,GAAG,CAAC,GAAG;AAAA,EAC5G;AACF;AACA,IAAM,mBAAmB;AAOzB,IAAM,cAAc,aAAW,mBAAmB;AAoBlD,IAAM,aAAN,cAAyB,UAAU;AAAC;AAOpC,IAAM,eAAe,aAAW,mBAAmB;AAQnD,IAAM,0BAA0B,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,yBAAyB,IAAI;AAAA,EAC9H,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AAID,IAAM,0BAA0B;AAChC,SAAS,YAAY,MAAM,QAAQ;AACjC,SAAO,CAAC,GAAG,OAAO,MAAM,IAAI;AAC9B;AAQA,SAAS,aAAa,SAAS,KAAK,uBAAuB,yBAAyB;AAClF,MAAI,OAAO,cAAc,eAAe,WAAW;AACjD,QAAI,CAAC,QAAS,aAAY,KAAK,0BAA0B;AACzD,QAAI,CAAC,IAAI,cAAe,iCAAgC,GAAG;AAAA,EAC7D;AACA,kBAAgB,SAAS,GAAG;AAC5B,MAAI,cAAc,WAAW,QAAQ,KAAK;AAI1C,MAAI,QAAQ,YAAY,yBAAyB,UAAU;AACzD,QAAI,cAAc,mBAAmB,QAAQ,QAAQ;AAAA,EACvD;AACA,0BAAwB,SAAS,GAAG;AACpC,2BAAyB,SAAS,GAAG;AACrC,oBAAkB,SAAS,GAAG;AAC9B,6BAA2B,SAAS,GAAG;AACzC;AAYA,SAAS,eAAe,SAAS,KAAK,kCAAkC,MAAM;AAC5E,QAAM,OAAO,MAAM;AACjB,QAAI,oCAAoC,OAAO,cAAc,eAAe,YAAY;AACtF,sBAAgB,GAAG;AAAA,IACrB;AAAA,EACF;AAMA,MAAI,IAAI,eAAe;AACrB,QAAI,cAAc,iBAAiB,IAAI;AACvC,QAAI,cAAc,kBAAkB,IAAI;AAAA,EAC1C;AACA,oBAAkB,SAAS,GAAG;AAC9B,MAAI,SAAS;AACX,QAAI,0BAA0B;AAC9B,YAAQ,4BAA4B,MAAM;AAAA,IAAC,CAAC;AAAA,EAC9C;AACF;AACA,SAAS,0BAA0B,YAAY,UAAU;AACvD,aAAW,QAAQ,eAAa;AAC9B,QAAI,UAAU,0BAA2B,WAAU,0BAA0B,QAAQ;AAAA,EACvF,CAAC;AACH;AAQA,SAAS,2BAA2B,SAAS,KAAK;AAChD,MAAI,IAAI,cAAc,kBAAkB;AACtC,UAAM,mBAAmB,gBAAc;AACrC,UAAI,cAAc,iBAAiB,UAAU;AAAA,IAC/C;AACA,YAAQ,yBAAyB,gBAAgB;AAGjD,QAAI,mBAAmB,MAAM;AAC3B,cAAQ,4BAA4B,gBAAgB;AAAA,IACtD,CAAC;AAAA,EACH;AACF;AAQA,SAAS,gBAAgB,SAAS,KAAK;AACrC,QAAM,aAAa,qBAAqB,OAAO;AAC/C,MAAI,IAAI,cAAc,MAAM;AAC1B,YAAQ,cAAc,gBAAgB,YAAY,IAAI,SAAS,CAAC;AAAA,EAClE,WAAW,OAAO,eAAe,YAAY;AAQ3C,YAAQ,cAAc,CAAC,UAAU,CAAC;AAAA,EACpC;AACA,QAAM,kBAAkB,0BAA0B,OAAO;AACzD,MAAI,IAAI,mBAAmB,MAAM;AAC/B,YAAQ,mBAAmB,gBAAgB,iBAAiB,IAAI,cAAc,CAAC;AAAA,EACjF,WAAW,OAAO,oBAAoB,YAAY;AAChD,YAAQ,mBAAmB,CAAC,eAAe,CAAC;AAAA,EAC9C;AAEA,QAAM,oBAAoB,MAAM,QAAQ,uBAAuB;AAC/D,4BAA0B,IAAI,gBAAgB,iBAAiB;AAC/D,4BAA0B,IAAI,qBAAqB,iBAAiB;AACtE;AAUA,SAAS,kBAAkB,SAAS,KAAK;AACvC,MAAI,mBAAmB;AACvB,MAAI,YAAY,MAAM;AACpB,QAAI,IAAI,cAAc,MAAM;AAC1B,YAAM,aAAa,qBAAqB,OAAO;AAC/C,UAAI,MAAM,QAAQ,UAAU,KAAK,WAAW,SAAS,GAAG;AAEtD,cAAM,oBAAoB,WAAW,OAAO,eAAa,cAAc,IAAI,SAAS;AACpF,YAAI,kBAAkB,WAAW,WAAW,QAAQ;AAClD,6BAAmB;AACnB,kBAAQ,cAAc,iBAAiB;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AACA,QAAI,IAAI,mBAAmB,MAAM;AAC/B,YAAM,kBAAkB,0BAA0B,OAAO;AACzD,UAAI,MAAM,QAAQ,eAAe,KAAK,gBAAgB,SAAS,GAAG;AAEhE,cAAM,yBAAyB,gBAAgB,OAAO,oBAAkB,mBAAmB,IAAI,cAAc;AAC7G,YAAI,uBAAuB,WAAW,gBAAgB,QAAQ;AAC5D,6BAAmB;AACnB,kBAAQ,mBAAmB,sBAAsB;AAAA,QACnD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,OAAO,MAAM;AAAA,EAAC;AACpB,4BAA0B,IAAI,gBAAgB,IAAI;AAClD,4BAA0B,IAAI,qBAAqB,IAAI;AACvD,SAAO;AACT;AACA,SAAS,wBAAwB,SAAS,KAAK;AAC7C,MAAI,cAAc,iBAAiB,cAAY;AAC7C,YAAQ,gBAAgB;AACxB,YAAQ,iBAAiB;AACzB,YAAQ,gBAAgB;AACxB,QAAI,QAAQ,aAAa,SAAU,eAAc,SAAS,GAAG;AAAA,EAC/D,CAAC;AACH;AACA,SAAS,kBAAkB,SAAS,KAAK;AACvC,MAAI,cAAc,kBAAkB,MAAM;AACxC,YAAQ,kBAAkB;AAC1B,QAAI,QAAQ,aAAa,UAAU,QAAQ,eAAgB,eAAc,SAAS,GAAG;AACrF,QAAI,QAAQ,aAAa,SAAU,SAAQ,cAAc;AAAA,EAC3D,CAAC;AACH;AACA,SAAS,cAAc,SAAS,KAAK;AACnC,MAAI,QAAQ,cAAe,SAAQ,YAAY;AAC/C,UAAQ,SAAS,QAAQ,eAAe;AAAA,IACtC,uBAAuB;AAAA,EACzB,CAAC;AACD,MAAI,kBAAkB,QAAQ,aAAa;AAC3C,UAAQ,iBAAiB;AAC3B;AACA,SAAS,yBAAyB,SAAS,KAAK;AAC9C,QAAM,WAAW,CAAC,UAAU,mBAAmB;AAE7C,QAAI,cAAc,WAAW,QAAQ;AAErC,QAAI,eAAgB,KAAI,kBAAkB,QAAQ;AAAA,EACpD;AACA,UAAQ,iBAAiB,QAAQ;AAGjC,MAAI,mBAAmB,MAAM;AAC3B,YAAQ,oBAAoB,QAAQ;AAAA,EACtC,CAAC;AACH;AAQA,SAAS,mBAAmB,SAAS,KAAK;AACxC,MAAI,WAAW,SAAS,OAAO,cAAc,eAAe,WAAY,aAAY,KAAK,0BAA0B;AACnH,kBAAgB,SAAS,GAAG;AAC9B;AAQA,SAAS,qBAAqB,SAAS,KAAK;AAC1C,SAAO,kBAAkB,SAAS,GAAG;AACvC;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,YAAY,KAAK,wEAAwE;AAClG;AACA,SAAS,YAAY,KAAK,SAAS;AACjC,QAAM,aAAa,yBAAyB,GAAG;AAC/C,QAAM,IAAI,MAAM,GAAG,OAAO,IAAI,UAAU,EAAE;AAC5C;AACA,SAAS,yBAAyB,KAAK;AACrC,QAAM,OAAO,IAAI;AACjB,MAAI,QAAQ,KAAK,SAAS,EAAG,QAAO,UAAU,KAAK,KAAK,MAAM,CAAC;AAC/D,MAAI,OAAO,CAAC,EAAG,QAAO,UAAU,IAAI;AACpC,SAAO;AACT;AACA,SAAS,gCAAgC,KAAK;AAC5C,QAAM,MAAM,yBAAyB,GAAG;AACxC,QAAM,IAAI,aAAc,OAAwD,sCAAsC,GAAG,GAAG;AAC9H;AACA,SAAS,gCAAgC,KAAK;AAC5C,QAAM,MAAM,yBAAyB,GAAG;AACxC,QAAM,IAAI,aAAc,MAA4D,qEAAqE,GAAG,2FAAgG;AAC9P;AACA,SAAS,kBAAkB,SAAS,WAAW;AAC7C,MAAI,CAAC,QAAQ,eAAe,OAAO,EAAG,QAAO;AAC7C,QAAM,SAAS,QAAQ,OAAO;AAC9B,MAAI,OAAO,cAAc,EAAG,QAAO;AACnC,SAAO,CAAC,OAAO,GAAG,WAAW,OAAO,YAAY;AAClD;AACA,SAAS,kBAAkB,eAAe;AAGxC,SAAO,OAAO,eAAe,cAAc,WAAW,MAAM;AAC9D;AACA,SAAS,oBAAoB,MAAM,YAAY;AAC7C,OAAK,qBAAqB;AAC1B,aAAW,QAAQ,SAAO;AACxB,UAAM,UAAU,IAAI;AACpB,QAAI,QAAQ,aAAa,YAAY,QAAQ,gBAAgB;AAC3D,UAAI,kBAAkB,QAAQ,aAAa;AAC3C,cAAQ,iBAAiB;AAAA,IAC3B;AAAA,EACF,CAAC;AACH;AAEA,SAAS,oBAAoB,KAAK,gBAAgB;AAChD,MAAI,CAAC,eAAgB,QAAO;AAC5B,MAAI,CAAC,MAAM,QAAQ,cAAc,MAAM,OAAO,cAAc,eAAe,WAAY,iCAAgC,GAAG;AAC1H,MAAI,kBAAkB;AACtB,MAAI,kBAAkB;AACtB,MAAI,iBAAiB;AACrB,iBAAe,QAAQ,OAAK;AAC1B,QAAI,EAAE,gBAAgB,sBAAsB;AAC1C,wBAAkB;AAAA,IACpB,WAAW,kBAAkB,CAAC,GAAG;AAC/B,UAAI,oBAAoB,OAAO,cAAc,eAAe,WAAY,aAAY,KAAK,iEAAiE;AAC1J,wBAAkB;AAAA,IACpB,OAAO;AACL,UAAI,mBAAmB,OAAO,cAAc,eAAe,WAAY,aAAY,KAAK,+DAA+D;AACvJ,uBAAiB;AAAA,IACnB;AAAA,EACF,CAAC;AACD,MAAI,eAAgB,QAAO;AAC3B,MAAI,gBAAiB,QAAO;AAC5B,MAAI,gBAAiB,QAAO;AAC5B,MAAI,OAAO,cAAc,eAAe,WAAW;AACjD,gBAAY,KAAK,+CAA+C;AAAA,EAClE;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,MAAM,IAAI;AAClC,QAAM,QAAQ,KAAK,QAAQ,EAAE;AAC7B,MAAI,QAAQ,GAAI,MAAK,OAAO,OAAO,CAAC;AACtC;AAEA,SAAS,gBAAgB,MAAM,MAAM,UAAU,eAAe;AAC5D,MAAI,kBAAkB,QAAS;AAC/B,OAAK,kBAAkB,QAAQ,kBAAkB,WAAW,CAAC,KAAK,2BAA2B,kBAAkB,YAAY,CAAC,SAAS,qBAAqB;AACxJ,YAAQ,KAAK,eAAe,IAAI,CAAC;AACjC,SAAK,0BAA0B;AAC/B,aAAS,sBAAsB;AAAA,EACjC;AACF;AACA,IAAM,0BAA0B;AAAA,EAC9B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,MAAM;AACtC;AACA,IAAM,qBAAqB,MAAM,QAAQ,QAAQ,GAAG;AAiEpD,IAAM,SAAN,MAAM,gBAAe,iBAAiB;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,WAAO,UAAU,KAAK,iBAAiB;AAAA,EACzC;AAAA;AAAA,EAEA,aAAa,SAAS,MAAM,KAAK,kBAAkB,GAAG,GAAI,YAAY,CAAC;AAAA,IACrE,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR,oBAAoB,OAAO,OAAO,GAAI,YAAY,CAAC;AAAA,IACjD,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR,cAAc,oBAAI,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU5B;AAAA,EACA,YAAY,YAAY,iBAAiB,sBAAsB;AAC7D,UAAM;AACN,SAAK,uBAAuB;AAC5B,SAAK,OAAO,IAAI,UAAU,CAAC,GAAG,kBAAkB,UAAU,GAAG,uBAAuB,eAAe,CAAC;AAAA,EACtG;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,KAAK;AACd,sBAAkB,KAAK,MAAM;AAC3B,YAAM,YAAY,KAAK,eAAe,IAAI,IAAI;AAC9C,UAAI,UAAU,UAAU,gBAAgB,IAAI,MAAM,IAAI,OAAO;AAC7D,mBAAa,IAAI,SAAS,KAAK,KAAK,oBAAoB;AACxD,UAAI,QAAQ,uBAAuB;AAAA,QACjC,WAAW;AAAA,MACb,CAAC;AACD,WAAK,YAAY,IAAI,GAAG;AAAA,IAC1B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,KAAK;AACd,WAAO,KAAK,KAAK,IAAI,IAAI,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,KAAK;AACjB,sBAAkB,KAAK,MAAM;AAC3B,YAAM,YAAY,KAAK,eAAe,IAAI,IAAI;AAC9C,UAAI,WAAW;AACb,kBAAU,cAAc,IAAI,IAAI;AAAA,MAClC;AACA,WAAK,YAAY,OAAO,GAAG;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,KAAK;AAChB,sBAAkB,KAAK,MAAM;AAC3B,YAAM,YAAY,KAAK,eAAe,IAAI,IAAI;AAC9C,YAAM,QAAQ,IAAI,UAAU,CAAC,CAAC;AAC9B,yBAAmB,OAAO,GAAG;AAC7B,gBAAU,gBAAgB,IAAI,MAAM,KAAK;AACzC,YAAM,uBAAuB;AAAA,QAC3B,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,KAAK;AACnB,sBAAkB,KAAK,MAAM;AAC3B,YAAM,YAAY,KAAK,eAAe,IAAI,IAAI;AAC9C,UAAI,WAAW;AACb,kBAAU,cAAc,IAAI,IAAI;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,KAAK;AAChB,WAAO,KAAK,KAAK,IAAI,IAAI,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,KAAK,OAAO;AACtB,sBAAkB,KAAK,MAAM;AAC3B,YAAM,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI;AACnC,WAAK,SAAS,KAAK;AAAA,IACrB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,OAAO;AACd,SAAK,QAAQ,SAAS,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,QAAQ;AACf,SAAK,kBAAkB,IAAI,IAAI;AAC/B,wBAAoB,KAAK,MAAM,KAAK,WAAW;AAC/C,SAAK,SAAS,KAAK,MAAM;AACzB,SAAK,KAAK,QAAQ,KAAK,IAAI,mBAAmB,KAAK,OAAO,CAAC;AAG3D,WAAO,QAAQ,QAAQ,WAAW;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ,QAAW;AAC3B,SAAK,KAAK,MAAM,KAAK;AACrB,SAAK,kBAAkB,IAAI,KAAK;AAChC,SAAK,KAAK,QAAQ,KAAK,IAAI,eAAe,KAAK,IAAI,CAAC;AAAA,EACtD;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,WAAW,KAAK,QAAQ,YAAY,MAAM;AACjD,WAAK,KAAK,YAAY,KAAK,QAAQ;AAAA,IACrC;AAAA,EACF;AAAA,EACA,eAAe,MAAM;AACnB,SAAK,IAAI;AACT,WAAO,KAAK,SAAS,KAAK,KAAK,IAAI,IAAI,IAAI,KAAK;AAAA,EAClD;AAAA,EACA,OAAO,OAAO,SAAS,eAAe,mBAAmB;AACvD,WAAO,KAAK,qBAAqB,SAAW,kBAAkB,eAAe,EAAE,GAAM,kBAAkB,qBAAqB,EAAE,GAAM,kBAAkB,yBAAyB,CAAC,CAAC;AAAA,EACnL;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,QAAQ,GAAG,YAAY,IAAI,GAAG,aAAa,EAAE,GAAG,CAAC,SAAS,GAAG,CAAC,IAAI,UAAU,EAAE,CAAC;AAAA,IAC5F,cAAc,SAAS,oBAAoB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,UAAU,SAAS,iCAAiC,QAAQ;AACxE,iBAAO,IAAI,SAAS,MAAM;AAAA,QAC5B,CAAC,EAAE,SAAS,SAAS,kCAAkC;AACrD,iBAAO,IAAI,QAAQ;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,iBAAiB,SAAS;AAAA,IACzC;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,IACA,UAAU,CAAC,QAAQ;AAAA,IACnB,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,uBAAuB,CAAC,GAAM,0BAA0B;AAAA,EAC5F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,QAAQ,CAAC;AAAA,IAC/E,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,uBAAuB;AAAA,MACnC,MAAM;AAAA,QACJ,YAAY;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,MACA,SAAS,CAAC,UAAU;AAAA,MACpB,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,eAAe,MAAM,IAAI;AAChC,QAAM,QAAQ,KAAK,QAAQ,EAAE;AAC7B,MAAI,QAAQ,GAAI,MAAK,OAAO,OAAO,CAAC;AACtC;AACA,SAAS,mBAAmB,WAAW;AACrC,SAAO,OAAO,cAAc,YAAY,cAAc,QAAQ,OAAO,KAAK,SAAS,EAAE,WAAW,KAAK,WAAW,aAAa,cAAc;AAC7I;AACA,IAAM,cAAc,MAAMC,qBAAoB,gBAAgB;AAAA;AAAA,EAE5D,eAAe;AAAA;AAAA,EAEf,YAAY,CAAC;AAAA;AAAA,EAEb;AAAA;AAAA,EAEA,iBAAiB;AAAA,EACjB,YAEA,YAAY,MAAM,iBAAiB,gBAAgB;AACjD,UAAM,eAAe,eAAe,GAAG,oBAAoB,gBAAgB,eAAe,CAAC;AAC3F,SAAK,gBAAgB,SAAS;AAC9B,SAAK,mBAAmB,eAAe;AACvC,SAAK,iBAAiB;AACtB,SAAK,uBAAuB;AAAA,MAC1B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,WAAW,CAAC,CAAC,KAAK;AAAA,IACpB,CAAC;AACD,QAAI,aAAa,eAAe,MAAM,gBAAgB,eAAe,gBAAgB,wBAAwB;AAC3G,UAAI,mBAAmB,SAAS,GAAG;AACjC,aAAK,eAAe,UAAU;AAAA,MAChC,OAAO;AACL,aAAK,eAAe;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,OAAO,UAAU,CAAC,GAAG;AAC5B,SAAK,QAAQ,KAAK,gBAAgB;AAClC,QAAI,KAAK,UAAU,UAAU,QAAQ,0BAA0B,OAAO;AACpE,WAAK,UAAU,QAAQ,cAAY,SAAS,KAAK,OAAO,QAAQ,0BAA0B,KAAK,CAAC;AAAA,IAClG;AACA,SAAK,uBAAuB,OAAO;AAAA,EACrC;AAAA,EACA,WAAW,OAAO,UAAU,CAAC,GAAG;AAC9B,SAAK,SAAS,OAAO,OAAO;AAAA,EAC9B;AAAA,EACA,MAAM,YAAY,KAAK,cAAc,UAAU,CAAC,GAAG;AACjD,SAAK,gBAAgB,SAAS;AAC9B,SAAK,eAAe,OAAO;AAC3B,SAAK,gBAAgB,OAAO;AAC5B,SAAK,SAAS,KAAK,OAAO,OAAO;AACjC,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA,EAEA,eAAe;AAAA,EAAC;AAAA;AAAA,EAEhB,aAAa,WAAW;AACtB,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,uBAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,UAAU,KAAK,EAAE;AAAA,EACxB;AAAA;AAAA,EAEA,oBAAoB,IAAI;AACtB,mBAAe,KAAK,WAAW,EAAE;AAAA,EACnC;AAAA,EACA,yBAAyB,IAAI;AAC3B,SAAK,kBAAkB,KAAK,EAAE;AAAA,EAChC;AAAA;AAAA,EAEA,4BAA4B,IAAI;AAC9B,mBAAe,KAAK,mBAAmB,EAAE;AAAA,EAC3C;AAAA;AAAA,EAEA,cAAc,IAAI;AAAA,EAAC;AAAA;AAAA,EAEnB,uBAAuB;AACrB,QAAI,KAAK,aAAa,UAAU;AAC9B,UAAI,KAAK,cAAe,MAAK,YAAY;AACzC,UAAI,KAAK,gBAAiB,MAAK,cAAc;AAC7C,UAAI,KAAK,gBAAgB;AACvB,aAAK,SAAS,KAAK,eAAe;AAAA,UAChC,UAAU;AAAA,UACV,uBAAuB;AAAA,QACzB,CAAC;AACD,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,WAAW;AACzB,QAAI,mBAAmB,SAAS,GAAG;AACjC,WAAK,QAAQ,KAAK,gBAAgB,UAAU;AAC5C,gBAAU,WAAW,KAAK,QAAQ;AAAA,QAChC,UAAU;AAAA,QACV,WAAW;AAAA,MACb,CAAC,IAAI,KAAK,OAAO;AAAA,QACf,UAAU;AAAA,QACV,WAAW;AAAA,MACb,CAAC;AAAA,IACH,OAAO;AACL,WAAK,QAAQ,KAAK,gBAAgB;AAAA,IACpC;AAAA,EACF;AACF;AACA,IAAM,qBAAqB;AAO3B,IAAM,gBAAgB,aAAW,mBAAmB;AAQpD,IAAM,6BAAN,MAAM,oCAAmC,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxD;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,iBAAiB;AAEtB,SAAK,cAAc,aAAa,IAAI;AAAA,EACtC;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,eAAe;AAEtB,WAAK,cAAc,gBAAgB,IAAI;AAAA,IACzC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK,cAAc,aAAa,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACT,WAAO,YAAY,KAAK,QAAQ,OAAO,KAAK,OAAO,KAAK,KAAK,SAAS,GAAG,KAAK,OAAO;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU,KAAK,QAAQ,gBAAgB;AAAA,EACrD;AAAA;AAAA,EAEA,mBAAmB;AAAA,EAAC;AAAA,EACpB,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mCAAmC,mBAAmB;AACpE,cAAQ,4CAA4C,0CAA6C,sBAAsB,2BAA0B,IAAI,qBAAqB,2BAA0B;AAAA,IACtM;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,UAAU,CAAI,0BAA0B;AAAA,EAC1C,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,SAAS,uBAAuB;AAC9B,SAAO,IAAI,aAAc,MAAmD;AAAA;AAAA;AAAA;AAAA,MAIxE,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAMtB,2BAA2B,EAAE;AACnC;AACA,SAAS,yBAAyB;AAChC,SAAO,IAAI,aAAc,MAAwD;AAAA;AAAA;AAAA;AAAA;AAAA,MAK7E,oBAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,mBAAmB,EAAE;AAC3B;AACA,SAAS,uBAAuB;AAC9B,SAAO,IAAI,aAAc,MAAkD;AAAA;AAAA;AAAA;AAAA,4FAIe;AAC5F;AACA,SAAS,4BAA4B;AACnC,SAAO,IAAI,aAAc,MAAwD;AAAA;AAAA;AAAA;AAAA;AAAA,MAK7E,oBAAoB;AAAA;AAAA;AAAA;AAAA,MAIpB,mBAAmB,EAAE;AAC3B;AACA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,YAAY;AAC5C;AA2BA,IAAM,eAAN,MAAM,sBAAqB,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpD,OAAO;AAAA,EACP,YAAY,QAAQ,YAAY,iBAAiB;AAC/C,UAAM;AACN,SAAK,UAAU;AACf,SAAK,eAAe,UAAU;AAC9B,SAAK,oBAAoB,eAAe;AAAA,EAC1C;AAAA;AAAA,EAEA,mBAAmB;AACjB,QAAI,EAAE,KAAK,mBAAmB,kBAAiB,EAAE,KAAK,mBAAmB,YAAY,OAAO,cAAc,eAAe,YAAY;AACnI,YAAM,0BAA0B;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAiB,kBAAkB,kBAAkB,CAAC,GAAM,kBAAkB,eAAe,EAAE,GAAM,kBAAkB,qBAAqB,EAAE,CAAC;AAAA,EAClL;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,IACpC,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,gBAAgB,MAAM;AAAA,IAClC;AAAA,IACA,UAAU,CAAC,cAAc;AAAA,IACzB,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,kBAAkB,CAAC,GAAM,0BAA0B;AAAA,EACvF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,kBAAkB;AAAA,MAC9B,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,OAAO;AACvC;AAkBA,IAAM,mBAAmB,MAAM,QAAQ,QAAQ,GAAG;AAsFlD,IAAM,UAAN,MAAM,iBAAgB,UAAU;AAAA,EAC9B;AAAA,EACA;AAAA,EACA,UAAU,IAAI,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1B,OAAO;AAAA;AAAA,EAEP,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,IAAI,aAAa;AAAA,EAC1B,YAAY,QAAQ,YAAY,iBAAiB,gBAAgB,oBAAoB,sBAAsB;AACzG,UAAM;AACN,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB;AAC5B,SAAK,UAAU;AACf,SAAK,eAAe,UAAU;AAC9B,SAAK,oBAAoB,eAAe;AACxC,SAAK,gBAAgB,oBAAoB,MAAM,cAAc;AAAA,EAC/D;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,SAAK,gBAAgB;AACrB,QAAI,CAAC,KAAK,eAAe,UAAU,SAAS;AAC1C,UAAI,KAAK,aAAa;AACpB,aAAK,WAAW;AAChB,YAAI,KAAK,eAAe;AAKtB,gBAAM,UAAU,QAAQ,MAAM,EAAE;AAChC,eAAK,cAAc,cAAc;AAAA,YAC/B,MAAM;AAAA,YACN,MAAM,KAAK,SAAS,OAAO;AAAA,UAC7B,CAAC;AAAA,QACH;AAAA,MACF;AACA,WAAK,cAAc;AAAA,IACrB;AACA,QAAI,gBAAgB,SAAS;AAC3B,WAAK,gBAAgB,OAAO;AAAA,IAC9B;AACA,QAAI,kBAAkB,SAAS,KAAK,SAAS,GAAG;AAC9C,WAAK,aAAa,KAAK,KAAK;AAC5B,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,iBAAiB,KAAK,cAAc,cAAc,IAAI;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WAAO,KAAK,SAAS,KAAK,IAAI;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU,KAAK,QAAQ,gBAAgB;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,UAAU;AAC1B,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,QAAQ;AAAA,EAC3B;AAAA,EACA,gBAAgB;AACd,SAAK,mBAAmB;AACxB,SAAK,cAAc,IAAI,KAAK,iBAAiB,IAAI,KAAK,cAAc,WAAW,IAAI;AACnF,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,WAAW,KAAK,QAAQ,YAAY,MAAM;AACjD,WAAK,QAAQ,YAAY,KAAK,QAAQ;AAAA,IACxC;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,WAAO,CAAC,KAAK,WAAW,CAAC,EAAE,KAAK,WAAW,KAAK,QAAQ;AAAA,EAC1D;AAAA,EACA,mBAAmB;AACjB,iBAAa,KAAK,SAAS,MAAM,KAAK,oBAAoB;AAC1D,SAAK,QAAQ,uBAAuB;AAAA,MAClC,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB;AAChB,SAAK,OAAO,cAAc,eAAe,cAAc,CAAC,KAAK,cAAc,GAAG;AAC5E,wBAAkB,KAAK,OAAO;AAAA,IAChC;AACA,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,aAAa;AACX,QAAI,KAAK,WAAW,KAAK,QAAQ,KAAM,MAAK,OAAO,KAAK,QAAQ;AAChE,QAAI,CAAC,KAAK,cAAc,KAAK,CAAC,KAAK,SAAS,OAAO,cAAc,eAAe,YAAY;AAC1F,YAAM,qBAAqB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,aAAa,OAAO;AAClB,oBAAgB,KAAK,MAAM;AACzB,WAAK,QAAQ,SAAS,OAAO;AAAA,QAC3B,uBAAuB;AAAA,MACzB,CAAC;AACD,WAAK,oBAAoB,aAAa;AAAA,IACxC,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,SAAS;AACvB,UAAM,gBAAgB,QAAQ,YAAY,EAAE;AAE5C,UAAM,aAAa,kBAAkB,KAAK,iBAAiB,aAAa;AACxE,oBAAgB,KAAK,MAAM;AACzB,UAAI,cAAc,CAAC,KAAK,QAAQ,UAAU;AACxC,aAAK,QAAQ,QAAQ;AAAA,MACvB,WAAW,CAAC,cAAc,KAAK,QAAQ,UAAU;AAC/C,aAAK,QAAQ,OAAO;AAAA,MACtB;AACA,WAAK,oBAAoB,aAAa;AAAA,IACxC,CAAC;AAAA,EACH;AAAA,EACA,SAAS,aAAa;AACpB,WAAO,KAAK,UAAU,YAAY,aAAa,KAAK,OAAO,IAAI,CAAC,WAAW;AAAA,EAC7E;AAAA,EACA,OAAO,OAAO,SAAS,gBAAgB,mBAAmB;AACxD,WAAO,KAAK,qBAAqB,UAAY,kBAAkB,kBAAkB,CAAC,GAAM,kBAAkB,eAAe,EAAE,GAAM,kBAAkB,qBAAqB,EAAE,GAAM,kBAAkB,mBAAmB,EAAE,GAAM,kBAAkB,mBAAmB,CAAC,GAAM,kBAAkB,yBAAyB,CAAC,CAAC;AAAA,EACxT;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,WAAW,IAAI,GAAG,mBAAmB,IAAI,GAAG,eAAe,EAAE,CAAC;AAAA,IAC/E,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,YAAY,CAAC,GAAG,YAAY,YAAY;AAAA,MACxC,OAAO,CAAC,GAAG,WAAW,OAAO;AAAA,MAC7B,SAAS,CAAC,GAAG,kBAAkB,SAAS;AAAA,IAC1C;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,SAAS;AAAA,IACpB,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,oBAAoB,CAAC,GAAM,4BAA+B,oBAAoB;AAAA,EAClH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,oBAAoB;AAAA,MAChC,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,kBAAkB,QAAQ;AACjC,MAAI,EAAE,kBAAkB,iBAAiB,kBAAkB,4BAA4B;AACrF,UAAM,uBAAuB;AAAA,EAC/B,WAAW,EAAE,kBAAkB,iBAAiB,EAAE,kBAAkB,SAAS;AAC3E,UAAM,qBAAqB;AAAA,EAC7B;AACF;AAmBA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAe;AAAA,EAClD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,QAAQ,GAAG,YAAY,IAAI,GAAG,oBAAoB,EAAE,CAAC;AAAA,IAClE,WAAW,CAAC,cAAc,EAAE;AAAA,IAC5B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,cAAc;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,mBAAmB;AAAA,EACjD,OAAO;AACT;AAyBA,IAAM,sBAAN,MAAM,6BAA4B,4BAA4B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5D,WAAW,OAAO;AAEhB,UAAM,kBAAkB,SAAS,OAAO,KAAK;AAC7C,SAAK,YAAY,SAAS,eAAe;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,IAAI;AACnB,SAAK,WAAW,WAAS;AACvB,SAAG,SAAS,KAAK,OAAO,WAAW,KAAK,CAAC;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,4BAA4B,mBAAmB;AAC7D,cAAQ,qCAAqC,mCAAsC,sBAAsB,oBAAmB,IAAI,qBAAqB,oBAAmB;AAAA,IAC1K;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,QAAQ,UAAU,mBAAmB,EAAE,GAAG,CAAC,SAAS,QAAQ,UAAU,eAAe,EAAE,GAAG,CAAC,SAAS,QAAQ,UAAU,WAAW,EAAE,CAAC;AAAA,IAC1J,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,6CAA6C,QAAQ;AACnF,iBAAO,IAAI,SAAS,OAAO,OAAO,KAAK;AAAA,QACzC,CAAC,EAAE,QAAQ,SAAS,8CAA8C;AAChE,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,qBAAqB,CAAC,GAAM,0BAA0B;AAAA,EAC1F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,WAAW,CAAC,qBAAqB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,yBAAyB;AAAA,EACvD,OAAO;AACT;AACA,SAAS,iBAAiB;AACxB,QAAM,IAAI,aAAc,MAAmE;AAAA;AAAA;AAAA,KAGxF;AACL;AAKA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,IAAI,SAAS,UAAU;AACrB,SAAK,WAAW,KAAK,CAAC,SAAS,QAAQ,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAU;AACf,aAAS,IAAI,KAAK,WAAW,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AACpD,UAAI,KAAK,WAAW,CAAC,EAAE,CAAC,MAAM,UAAU;AACtC,aAAK,WAAW,OAAO,GAAG,CAAC;AAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAU;AACf,SAAK,WAAW,QAAQ,OAAK;AAC3B,UAAI,KAAK,aAAa,GAAG,QAAQ,KAAK,EAAE,CAAC,MAAM,UAAU;AACvD,UAAE,CAAC,EAAE,YAAY,SAAS,KAAK;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,aAAa,aAAa,UAAU;AAClC,QAAI,CAAC,YAAY,CAAC,EAAE,QAAS,QAAO;AACpC,WAAO,YAAY,CAAC,EAAE,YAAY,SAAS,SAAS,WAAW,YAAY,CAAC,EAAE,SAAS,SAAS;AAAA,EAClG;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,IAC9B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAqBH,IAAM,4BAAN,MAAM,mCAAkC,4BAA4B;AAAA,EAClE;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxB,WAAW,MAAM;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,uBAAuB,OAAO,yBAAyB;AAAA,IACrD,UAAU;AAAA,EACZ,CAAC,KAAK;AAAA,EACN,YAAY,UAAU,YAAY,WAAW,WAAW;AACtD,UAAM,UAAU,UAAU;AAC1B,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,WAAW,KAAK,UAAU,IAAI,SAAS;AAC5C,SAAK,WAAW;AAChB,SAAK,UAAU,IAAI,KAAK,UAAU,IAAI;AAAA,EACxC;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,UAAU,OAAO,IAAI;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAChB,SAAK,SAAS,UAAU,KAAK;AAC7B,SAAK,YAAY,WAAW,KAAK,MAAM;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,IAAI;AACnB,SAAK,MAAM;AACX,SAAK,WAAW,MAAM;AACpB,SAAG,KAAK,KAAK;AACb,WAAK,UAAU,OAAO,IAAI;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,YAAY;AAmB3B,QAAI,KAAK,yBAAyB,cAAc,KAAK,yBAAyB,6BAA6B;AACzG,WAAK,YAAY,YAAY,UAAU;AAAA,IACzC;AACA,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,aAAa;AACX,QAAI,KAAK,QAAQ,KAAK,mBAAmB,KAAK,SAAS,KAAK,oBAAoB,OAAO,cAAc,eAAe,YAAY;AAC9H,qBAAe;AAAA,IACjB;AACA,QAAI,CAAC,KAAK,QAAQ,KAAK,gBAAiB,MAAK,OAAO,KAAK;AAAA,EAC3D;AAAA,EACA,OAAO,OAAO,SAAS,kCAAkC,mBAAmB;AAC1E,WAAO,KAAK,qBAAqB,4BAA8B,kBAAqB,SAAS,GAAM,kBAAqB,UAAU,GAAM,kBAAkB,oBAAoB,GAAM,kBAAqB,QAAQ,CAAC;AAAA,EACpN;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,QAAQ,SAAS,mBAAmB,EAAE,GAAG,CAAC,SAAS,QAAQ,SAAS,eAAe,EAAE,GAAG,CAAC,SAAS,QAAQ,SAAS,WAAW,EAAE,CAAC;AAAA,IACvJ,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,UAAU,SAAS,sDAAsD;AACrF,iBAAO,IAAI,SAAS;AAAA,QACtB,CAAC,EAAE,QAAQ,SAAS,oDAAoD;AACtE,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,iBAAiB;AAAA,MACjB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,oBAAoB,CAAC,GAAM,0BAA0B;AAAA,EACzF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ;AAAA,MACA,WAAW,CAAC,oBAAoB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,kBAAkB;AAAA,EAChD,OAAO;AACT;AAyBA,IAAM,qBAAN,MAAM,4BAA2B,4BAA4B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3D,WAAW,OAAO;AAChB,SAAK,YAAY,SAAS,WAAW,KAAK,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,IAAI;AACnB,SAAK,WAAW,WAAS;AACvB,SAAG,SAAS,KAAK,OAAO,WAAW,KAAK,CAAC;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,QAAQ,SAAS,mBAAmB,EAAE,GAAG,CAAC,SAAS,QAAQ,SAAS,eAAe,EAAE,GAAG,CAAC,SAAS,QAAQ,SAAS,WAAW,EAAE,CAAC;AAAA,IACvJ,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,UAAU,SAAS,6CAA6C,QAAQ;AACpF,iBAAO,IAAI,SAAS,OAAO,OAAO,KAAK;AAAA,QACzC,CAAC,EAAE,SAAS,SAAS,4CAA4C,QAAQ;AACvE,iBAAO,IAAI,SAAS,OAAO,OAAO,KAAK;AAAA,QACzC,CAAC,EAAE,QAAQ,SAAS,6CAA6C;AAC/D,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,oBAAoB,CAAC,GAAM,0BAA0B;AAAA,EACzF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,WAAW,CAAC,oBAAoB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,qCAAqC,IAAI,eAAe,YAAY,kCAAkC,EAAE;AAC9G,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,oBAAoB;AACpD;AAsBA,IAAM,uBAAN,MAAM,8BAA6B,UAAU;AAAA,EAC3C;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW,YAAY;AACzB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,cAAQ,KAAK,mBAAmB;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA,EAGA;AAAA;AAAA,EAEA,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1B,OAAO,0BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQjC,sBAAsB;AAAA,EACtB,YAAY,YAAY,iBAAiB,gBAAgB,uBAAuB,sBAAsB;AACpG,UAAM;AACN,SAAK,wBAAwB;AAC7B,SAAK,uBAAuB;AAC5B,SAAK,eAAe,UAAU;AAC9B,SAAK,oBAAoB,eAAe;AACxC,SAAK,gBAAgB,oBAAoB,MAAM,cAAc;AAAA,EAC/D;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,QAAI,KAAK,kBAAkB,OAAO,GAAG;AACnC,YAAM,eAAe,QAAQ,MAAM,EAAE;AACrC,UAAI,cAAc;AAChB;AAAA,UAAe;AAAA,UAAc;AAAA;AAAA,UAA2C;AAAA,QAAK;AAAA,MAC/E;AACA,mBAAa,KAAK,MAAM,MAAM,KAAK,oBAAoB;AACvD,WAAK,KAAK,uBAAuB;AAAA,QAC/B,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AACA,QAAI,kBAAkB,SAAS,KAAK,SAAS,GAAG;AAC9C,UAAI,OAAO,cAAc,eAAe,WAAW;AACjD,wBAAgB,eAAe,uBAAsB,MAAM,KAAK,qBAAqB;AAAA,MACvF;AACA,WAAK,KAAK,SAAS,KAAK,KAAK;AAC7B,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,MAAM;AACb;AAAA,QAAe,KAAK;AAAA,QAAM;AAAA;AAAA,QAA2C;AAAA,MAAK;AAAA,IAC5E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,UAAU;AAC1B,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,QAAQ;AAAA,EAC3B;AAAA,EACA,kBAAkB,SAAS;AACzB,WAAO,QAAQ,eAAe,MAAM;AAAA,EACtC;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAyB,kBAAkB,eAAe,EAAE,GAAM,kBAAkB,qBAAqB,EAAE,GAAM,kBAAkB,mBAAmB,EAAE,GAAM,kBAAkB,oCAAoC,CAAC,GAAM,kBAAkB,yBAAyB,CAAC,CAAC;AAAA,EAC3S;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IACnC,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,eAAe,MAAM;AAAA,MAC/B,YAAY,CAAC,GAAG,YAAY,YAAY;AAAA,MACxC,OAAO,CAAC,GAAG,WAAW,OAAO;AAAA,IAC/B;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,UAAU,CAAC,QAAQ;AAAA,IACnB,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,kBAAkB,CAAC,GAAM,4BAA+B,oBAAoB;AAAA,EAChH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,kBAAkB;AAAA,MAC9B,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,kBAAkB;AAClD;AAyBA,IAAM,qBAAN,MAAM,4BAA2B,iBAAiB;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACd,WAAO,UAAU,KAAK,kBAAkB;AAAA,EAC1C;AAAA;AAAA,EAEA,IAAI,UAAU,OAAO;AACnB,SAAK,mBAAmB,IAAI,KAAK;AAAA,EACnC;AAAA;AAAA,EAEA,aAAa,SAAS,MAAM,KAAK,mBAAmB,GAAG,GAAI,YAAY,CAAC;AAAA,IACtE,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA,EACR,qBAAqB,OAAO,OAAO,GAAI,YAAY,CAAC;AAAA,IAClD,WAAW;AAAA,EACb,CAAC,IAAI,CAAC,CAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,MAAM,KAAK,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjD,aAAa,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,WAAW,IAAI,aAAa;AAAA,EAC5B,YAAY,YAAY,iBAAiB,sBAAsB;AAC7D,UAAM;AACN,SAAK,uBAAuB;AAC5B,SAAK,eAAe,UAAU;AAC9B,SAAK,oBAAoB,eAAe;AAAA,EAC1C;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,SAAK,OAAO,cAAc,eAAe,cAAc,CAAC,KAAK,MAAM;AACjE,YAAM,qBAAqB;AAAA,IAC7B;AACA,QAAI,QAAQ,eAAe,MAAM,GAAG;AAClC,WAAK,kBAAkB;AACvB,WAAK,gBAAgB;AACrB,WAAK,qBAAqB;AAC1B,WAAK,WAAW,KAAK;AAAA,IACvB;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,MAAM;AACb,wBAAkB,KAAK,MAAM,IAAI;AAOjC,UAAI,KAAK,KAAK,wBAAwB,KAAK,qBAAqB;AAC9D,aAAK,KAAK,4BAA4B,MAAM;AAAA,QAAC,CAAC;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAClB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WAAO,CAAC;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,KAAK;AACd,UAAM,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI;AACnC,iBAAa,MAAM,KAAK,KAAK,oBAAoB;AACjD,SAAK,uBAAuB;AAAA,MAC1B,WAAW;AAAA,IACb,CAAC;AACD,SAAK,WAAW,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,KAAK;AACd,WAAO,KAAK,KAAK,IAAI,IAAI,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,KAAK;AACjB;AAAA,MAAe,IAAI,WAAW;AAAA,MAAM;AAAA;AAAA,MAA0C;AAAA,IAAK;AACnF,qBAAiB,KAAK,YAAY,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,KAAK;AAChB,SAAK,oBAAoB,GAAG;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,KAAK;AACnB,SAAK,sBAAsB,GAAG;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,KAAK;AAChB,WAAO,KAAK,KAAK,IAAI,IAAI,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,KAAK;AAChB,SAAK,oBAAoB,GAAG;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,KAAK;AACnB,SAAK,sBAAsB,GAAG;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,KAAK;AAChB,WAAO,KAAK,KAAK,IAAI,IAAI,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,KAAK,OAAO;AACtB,UAAM,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI;AACnC,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,QAAQ;AACf,SAAK,mBAAmB,IAAI,IAAI;AAChC,wBAAoB,KAAK,MAAM,KAAK,UAAU;AAC9C,SAAK,SAAS,KAAK,MAAM;AACzB,SAAK,KAAK,QAAQ,KAAK,IAAI,mBAAmB,KAAK,OAAO,CAAC;AAI3D,WAAO,QAAQ,QAAQ,WAAW;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,QAAQ,QAAW,UAAU,CAAC,GAAG;AACzC,SAAK,KAAK,MAAM,OAAO,OAAO;AAC9B,SAAK,mBAAmB,IAAI,KAAK;AACjC,QAAI,SAAS,cAAc,OAAO;AAChC,WAAK,KAAK,QAAQ,KAAK,IAAI,eAAe,KAAK,IAAI,CAAC;AAAA,IACtD;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,SAAK,WAAW,QAAQ,SAAO;AAC7B,YAAM,UAAU,IAAI;AACpB,YAAM,UAAU,KAAK,KAAK,IAAI,IAAI,IAAI;AACtC,UAAI,YAAY,SAAS;AAGvB,uBAAe,WAAW,MAAM,GAAG;AAMnC,YAAI,cAAc,OAAO,GAAG;AAC1B,uBAAa,SAAS,KAAK,KAAK,oBAAoB;AACpD,cAAI,UAAU;AAAA,QAChB;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,KAAK,oBAAoB;AAAA,MAC5B,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB,KAAK;AACvB,UAAM,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI;AACnC,uBAAmB,MAAM,GAAG;AAI5B,SAAK,uBAAuB;AAAA,MAC1B,WAAW;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,sBAAsB,KAAK;AACzB,QAAI,KAAK,MAAM;AACb,YAAM,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI;AACnC,UAAI,MAAM;AACR,cAAM,mBAAmB,qBAAqB,MAAM,GAAG;AACvD,YAAI,kBAAkB;AAGpB,eAAK,uBAAuB;AAAA,YAC1B,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EACA,uBAAuB;AACrB,SAAK,KAAK,4BAA4B,KAAK,mBAAmB;AAC9D,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,4BAA4B,MAAM;AAAA,MAAC,CAAC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,oBAAgB,KAAK,MAAM,IAAI;AAC/B,QAAI,KAAK,UAAU;AACjB,wBAAkB,KAAK,UAAU,IAAI;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAuB,kBAAkB,eAAe,EAAE,GAAM,kBAAkB,qBAAqB,EAAE,GAAM,kBAAkB,yBAAyB,CAAC,CAAC;AAAA,EAC/L;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,IACjC,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,UAAU,SAAS,6CAA6C,QAAQ;AACpF,iBAAO,IAAI,SAAS,MAAM;AAAA,QAC5B,CAAC,EAAE,SAAS,SAAS,8CAA8C;AACjE,iBAAO,IAAI,QAAQ;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,aAAa,MAAM;AAAA,IAC/B;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,IACZ;AAAA,IACA,UAAU,CAAC,QAAQ;AAAA,IACnB,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,qBAAqB,CAAC,GAAM,4BAA+B,oBAAoB;AAAA,EACnH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,qBAAqB;AAAA,MACjC,MAAM;AAAA,QACJ,YAAY;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,MACA,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,aAAa;AAC7C;AAgDA,IAAM,gBAAN,MAAM,uBAAsB,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUrD,OAAO;AAAA,EACP,YAAY,QAAQ,YAAY,iBAAiB;AAC/C,UAAM;AACN,SAAK,UAAU;AACf,SAAK,eAAe,UAAU;AAC9B,SAAK,oBAAoB,eAAe;AAAA,EAC1C;AAAA;AAAA,EAEA,mBAAmB;AACjB,QAAI,iBAAiB,KAAK,OAAO,MAAM,OAAO,cAAc,eAAe,YAAY;AACrF,YAAM,qBAAqB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAkB,kBAAkB,kBAAkB,EAAE,GAAM,kBAAkB,eAAe,EAAE,GAAM,kBAAkB,qBAAqB,EAAE,CAAC;AAAA,EACpL;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACrC,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,iBAAiB,MAAM;AAAA,IACnC;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,qBAAqB,CAAC,GAAM,0BAA0B;AAAA,EAC1F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,qBAAqB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,aAAa;AAC7C;AAyBA,IAAM,gBAAN,MAAM,uBAAsB,iBAAiB;AAAA;AAAA,EAE3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO;AAAA,EACP,YAAY,QAAQ,YAAY,iBAAiB;AAC/C,UAAM;AACN,SAAK,UAAU;AACf,SAAK,eAAe,UAAU;AAC9B,SAAK,oBAAoB,eAAe;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,QAAI,iBAAiB,KAAK,OAAO,MAAM,OAAO,cAAc,eAAe,YAAY;AACrF,YAAM,qBAAqB;AAAA,IAC7B;AACA,SAAK,cAAc,aAAa,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,SAAK,eAAe,gBAAgB,IAAI;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACZ,WAAO,KAAK,cAAc,aAAa,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU,KAAK,QAAQ,gBAAgB;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WAAO,YAAY,KAAK,QAAQ,OAAO,KAAK,OAAO,KAAK,KAAK,SAAS,GAAG,KAAK,OAAO;AAAA,EACvF;AAAA,EACA,OAAO,OAAO,SAAS,sBAAsB,mBAAmB;AAC9D,WAAO,KAAK,qBAAqB,gBAAkB,kBAAkB,kBAAkB,EAAE,GAAM,kBAAkB,eAAe,EAAE,GAAM,kBAAkB,qBAAqB,EAAE,CAAC;AAAA,EACpL;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,IACrC,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,iBAAiB,MAAM;AAAA,IACnC;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,qBAAqB,CAAC,GAAM,0BAA0B;AAAA,EAC1F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,qBAAqB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,iBAAiB,QAAQ;AAChC,SAAO,EAAE,kBAAkB,kBAAkB,EAAE,kBAAkB,uBAAuB,EAAE,kBAAkB;AAC9G;AACA,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,eAAe;AAC/C;AAiCA,IAAM,kBAAN,MAAM,yBAAwB,UAAU;AAAA,EACtC;AAAA,EACA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,IAAI,WAAW,YAAY;AACzB,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,cAAQ,KAAK,mBAAmB;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA,EAGA;AAAA;AAAA,EAEA,SAAS,IAAI,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1B,OAAO,0BAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQjC,sBAAsB;AAAA,EACtB,YAAY,QAAQ,YAAY,iBAAiB,gBAAgB,uBAAuB;AACtF,UAAM;AACN,SAAK,wBAAwB;AAC7B,SAAK,UAAU;AACf,SAAK,eAAe,UAAU;AAC9B,SAAK,oBAAoB,eAAe;AACxC,SAAK,gBAAgB,oBAAoB,MAAM,cAAc;AAAA,EAC/D;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,QAAI,CAAC,KAAK,OAAQ,MAAK,cAAc;AACrC,QAAI,kBAAkB,SAAS,KAAK,SAAS,GAAG;AAC9C,UAAI,OAAO,cAAc,eAAe,WAAW;AACjD,wBAAgB,mBAAmB,kBAAiB,MAAM,KAAK,qBAAqB;AAAA,MACtF;AACA,WAAK,YAAY,KAAK;AACtB,WAAK,cAAc,YAAY,MAAM,KAAK,KAAK;AAAA,IACjD;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,cAAc,IAAI;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,UAAU;AAC1B,SAAK,YAAY;AACjB,SAAK,OAAO,KAAK,QAAQ;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACT,WAAO,YAAY,KAAK,QAAQ,OAAO,KAAK,OAAO,KAAK,KAAK,SAAS,GAAG,KAAK,OAAO;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAClB,WAAO,KAAK,UAAU,KAAK,QAAQ,gBAAgB;AAAA,EACrD;AAAA,EACA,gBAAgB;AACd,QAAI,OAAO,cAAc,eAAe,WAAW;AACjD,sBAAgB,KAAK,SAAS,KAAK,IAAI;AAAA,IACzC;AACA,SAAK,UAAU,KAAK,cAAc,WAAW,IAAI;AACjD,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAoB,kBAAkB,kBAAkB,EAAE,GAAM,kBAAkB,eAAe,EAAE,GAAM,kBAAkB,qBAAqB,EAAE,GAAM,kBAAkB,mBAAmB,EAAE,GAAM,kBAAkB,oCAAoC,CAAC,CAAC;AAAA,EAChS;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,IACvC,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,mBAAmB,MAAM;AAAA,MACnC,YAAY,CAAC,GAAG,YAAY,YAAY;AAAA,MACxC,OAAO,CAAC,GAAG,WAAW,OAAO;AAAA,IAC/B;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,kBAAkB,CAAC,GAAM,4BAA+B,oBAAoB;AAAA,EAChH,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,kBAAkB;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,kCAAkC;AAAA,IAC3C,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,eAAe;AAAA,IACxB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,gBAAgB,QAAQ,MAAM;AACrC,MAAI,EAAE,kBAAkB,kBAAkB,kBAAkB,4BAA4B;AACtF,UAAM,sBAAsB;AAAA,EAC9B,WAAW,EAAE,kBAAkB,kBAAkB,EAAE,kBAAkB,uBAAuB,EAAE,kBAAkB,gBAAgB;AAC9H,UAAM,uBAAuB,IAAI;AAAA,EACnC;AACF;AACA,IAAM,wBAAwB;AAAA,EAC5B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,0BAA0B;AAAA,EACxD,OAAO;AACT;AACA,SAAS,oBAAoB,IAAI,OAAO;AACtC,MAAI,MAAM,KAAM,QAAO,GAAG,KAAK;AAC/B,MAAI,SAAS,OAAO,UAAU,SAAU,SAAQ;AAChD,SAAO,GAAG,EAAE,KAAK,KAAK,GAAG,MAAM,GAAG,EAAE;AACtC;AACA,SAAS,aAAa,aAAa;AACjC,SAAO,YAAY,MAAM,GAAG,EAAE,CAAC;AACjC;AAyDA,IAAM,6BAAN,MAAM,oCAAmC,4BAA4B;AAAA;AAAA,EAEnE;AAAA;AAAA,EAEA,aAAa,oBAAI,IAAI;AAAA;AAAA,EAErB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,IAAI,YAAY,IAAI;AAClB,QAAI,OAAO,OAAO,eAAe,OAAO,cAAc,eAAe,YAAY;AAC/E,YAAM,IAAI,aAAc,MAAkD,gDAAgD,KAAK,UAAU,EAAE,CAAC,EAAE;AAAA,IAChJ;AACA,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,iBAAiB,OAAO,cAAc,EAAE;AAAA,EACxC,aAAa,OAAO,UAAU;AAAA,EAC9B,MAAM,OAAO,iBAAiB;AAAA,EAC9B,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqBf,yBAAyB;AACvB,QAAI,KAAK,gBAAgB,KAAK,eAAe,WAAW;AACtD;AAAA,IACF;AACA,SAAK,eAAe;AACpB,oBAAgB;AAAA,MACd,OAAO,MAAM;AACX,YAAI,KAAK,WAAW,WAAW;AAC7B;AAAA,QACF;AACA,aAAK,eAAe;AACpB,aAAK,WAAW,KAAK,KAAK;AAAA,MAC5B;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AAGhB,SAAK,IAAI,aAAa;AACtB,SAAK,QAAQ;AACb,UAAM,KAAK,KAAK,aAAa,KAAK;AAClC,UAAM,cAAc,oBAAoB,IAAI,KAAK;AACjD,SAAK,YAAY,SAAS,WAAW;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,IAAI;AACnB,SAAK,WAAW,iBAAe;AAC7B,WAAK,QAAQ,KAAK,gBAAgB,WAAW;AAC7C,SAAG,KAAK,KAAK;AAAA,IACf;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,YAAQ,KAAK,cAAc,SAAS;AAAA,EACtC;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,eAAW,MAAM,KAAK,WAAW,KAAK,GAAG;AACvC,UAAI,KAAK,aAAa,KAAK,WAAW,IAAI,EAAE,GAAG,KAAK,EAAG,QAAO;AAAA,IAChE;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gBAAgB,aAAa;AAC3B,UAAM,KAAK,aAAa,WAAW;AACnC,WAAO,KAAK,WAAW,IAAI,EAAE,IAAI,KAAK,WAAW,IAAI,EAAE,IAAI;AAAA,EAC7D;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,mCAAmC,mBAAmB;AACpE,cAAQ,4CAA4C,0CAA6C,sBAAsB,2BAA0B,IAAI,qBAAqB,2BAA0B;AAAA,IACtM;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,mBAAmB,IAAI,GAAG,YAAY,EAAE,GAAG,CAAC,UAAU,eAAe,IAAI,GAAG,YAAY,EAAE,GAAG,CAAC,UAAU,WAAW,IAAI,GAAG,YAAY,EAAE,CAAC;AAAA,IAChK,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,UAAU,SAAS,qDAAqD,QAAQ;AAC5F,iBAAO,IAAI,SAAS,OAAO,OAAO,KAAK;AAAA,QACzC,CAAC,EAAE,QAAQ,SAAS,qDAAqD;AACvE,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa;AAAA,IACf;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,qBAAqB,CAAC,GAAM,0BAA0B;AAAA,EAC1F,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ;AAAA,MACA,WAAW,CAAC,qBAAqB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAWH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA,EACA,YAAY,UAAU,WAAW,SAAS;AACxC,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,QAAI,KAAK,QAAS,MAAK,KAAK,KAAK,QAAQ,gBAAgB;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ,OAAO;AACjB,QAAI,KAAK,WAAW,KAAM;AAC1B,SAAK,QAAQ,WAAW,IAAI,KAAK,IAAI,KAAK;AAC1C,SAAK,iBAAiB,oBAAoB,KAAK,IAAI,KAAK,CAAC;AACzD,SAAK,QAAQ,uBAAuB;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,MAAM,OAAO;AACf,SAAK,iBAAiB,KAAK;AAC3B,QAAI,KAAK,QAAS,MAAK,QAAQ,uBAAuB;AAAA,EACxD;AAAA;AAAA,EAEA,iBAAiB,OAAO;AACtB,SAAK,UAAU,YAAY,KAAK,SAAS,eAAe,SAAS,KAAK;AAAA,EACxE;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,WAAW,OAAO,KAAK,EAAE;AACtC,WAAK,QAAQ,uBAAuB;AAAA,IACtC;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAmB,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,4BAA4B,CAAC,CAAC;AAAA,EAC/K;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,QAAQ,CAAC;AAAA,IACtB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iCAAiC;AAAA,EACrC,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,kCAAkC;AAAA,EAChE,OAAO;AACT;AACA,SAAS,kBAAkB,IAAI,OAAO;AACpC,MAAI,MAAM,KAAM,QAAO,GAAG,KAAK;AAC/B,MAAI,OAAO,UAAU,SAAU,SAAQ,IAAI,KAAK;AAChD,MAAI,SAAS,OAAO,UAAU,SAAU,SAAQ;AAChD,SAAO,GAAG,EAAE,KAAK,KAAK,GAAG,MAAM,GAAG,EAAE;AACtC;AACA,SAAS,WAAW,aAAa;AAC/B,SAAO,YAAY,MAAM,GAAG,EAAE,CAAC;AACjC;AAoCA,IAAM,qCAAN,MAAM,4CAA2C,4BAA4B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3E;AAAA;AAAA,EAEA,aAAa,oBAAI,IAAI;AAAA;AAAA,EAErB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMb,IAAI,YAAY,IAAI;AAClB,QAAI,OAAO,OAAO,eAAe,OAAO,cAAc,eAAe,YAAY;AAC/E,YAAM,IAAI,aAAc,MAAkD,gDAAgD,KAAK,UAAU,EAAE,CAAC,EAAE;AAAA,IAChJ;AACA,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,WAAW,OAAO;AAChB,SAAK,QAAQ;AACb,QAAI;AACJ,QAAI,MAAM,QAAQ,KAAK,GAAG;AAExB,YAAM,MAAM,MAAM,IAAI,OAAK,KAAK,aAAa,CAAC,CAAC;AAC/C,kCAA4B,CAAC,KAAK,MAAM;AACtC,YAAI,aAAa,IAAI,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE;AAAA,MACjD;AAAA,IACF,OAAO;AACL,kCAA4B,CAAC,KAAK,MAAM;AACtC,YAAI,aAAa,KAAK;AAAA,MACxB;AAAA,IACF;AACA,SAAK,WAAW,QAAQ,yBAAyB;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,IAAI;AACnB,SAAK,WAAW,aAAW;AACzB,YAAM,WAAW,CAAC;AAClB,YAAM,kBAAkB,QAAQ;AAChC,UAAI,oBAAoB,QAAW;AACjC,cAAM,UAAU;AAChB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,MAAM,QAAQ,CAAC;AACrB,gBAAM,MAAM,KAAK,gBAAgB,IAAI,KAAK;AAC1C,mBAAS,KAAK,GAAG;AAAA,QACnB;AAAA,MACF,OAIK;AACH,cAAM,UAAU,QAAQ;AACxB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAM,MAAM,QAAQ,CAAC;AACrB,cAAI,IAAI,UAAU;AAChB,kBAAM,MAAM,KAAK,gBAAgB,IAAI,KAAK;AAC1C,qBAAS,KAAK,GAAG;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AACA,WAAK,QAAQ;AACb,SAAG,QAAQ;AAAA,IACb;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,OAAO;AACrB,UAAM,MAAM,KAAK,cAAc,SAAS;AACxC,SAAK,WAAW,IAAI,IAAI,KAAK;AAC7B,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,eAAW,MAAM,KAAK,WAAW,KAAK,GAAG;AACvC,UAAI,KAAK,aAAa,KAAK,WAAW,IAAI,EAAE,EAAE,QAAQ,KAAK,EAAG,QAAO;AAAA,IACvE;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,gBAAgB,aAAa;AAC3B,UAAM,KAAK,WAAW,WAAW;AACjC,WAAO,KAAK,WAAW,IAAI,EAAE,IAAI,KAAK,WAAW,IAAI,EAAE,EAAE,SAAS;AAAA,EACpE;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2CAA2C,mBAAmB;AAC5E,cAAQ,oDAAoD,kDAAqD,sBAAsB,mCAAkC,IAAI,qBAAqB,mCAAkC;AAAA,IACtO;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,UAAU,YAAY,IAAI,mBAAmB,EAAE,GAAG,CAAC,UAAU,YAAY,IAAI,eAAe,EAAE,GAAG,CAAC,UAAU,YAAY,IAAI,WAAW,EAAE,CAAC;AAAA,IACvJ,cAAc,SAAS,gDAAgD,IAAI,KAAK;AAC9E,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,UAAU,SAAS,6DAA6D,QAAQ;AACpG,iBAAO,IAAI,SAAS,OAAO,MAAM;AAAA,QACnC,CAAC,EAAE,QAAQ,SAAS,6DAA6D;AAC/E,iBAAO,IAAI,UAAU;AAAA,QACvB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,aAAa;AAAA,IACf;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,8BAA8B,CAAC,GAAM,0BAA0B;AAAA,EACnG,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oCAAoC,CAAC;AAAA,IAC3G,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,YAAY;AAAA,QACZ,UAAU;AAAA,MACZ;AAAA,MACA,WAAW,CAAC,8BAA8B;AAAA,MAC1C,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAWH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA,YAAY,UAAU,WAAW,SAAS;AACxC,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,QAAI,KAAK,SAAS;AAChB,WAAK,KAAK,KAAK,QAAQ,gBAAgB,IAAI;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,QAAQ,OAAO;AACjB,QAAI,KAAK,WAAW,KAAM;AAC1B,SAAK,SAAS;AACd,SAAK,iBAAiB,kBAAkB,KAAK,IAAI,KAAK,CAAC;AACvD,SAAK,QAAQ,WAAW,KAAK,QAAQ,KAAK;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,MAAM,OAAO;AACf,QAAI,KAAK,SAAS;AAChB,WAAK,SAAS;AACd,WAAK,iBAAiB,kBAAkB,KAAK,IAAI,KAAK,CAAC;AACvD,WAAK,QAAQ,WAAW,KAAK,QAAQ,KAAK;AAAA,IAC5C,OAAO;AACL,WAAK,iBAAiB,KAAK;AAAA,IAC7B;AAAA,EACF;AAAA;AAAA,EAEA,iBAAiB,OAAO;AACtB,SAAK,UAAU,YAAY,KAAK,SAAS,eAAe,SAAS,KAAK;AAAA,EACxE;AAAA;AAAA,EAEA,aAAa,UAAU;AACrB,SAAK,UAAU,YAAY,KAAK,SAAS,eAAe,YAAY,QAAQ;AAAA,EAC9E;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,WAAW,OAAO,KAAK,EAAE;AACtC,WAAK,QAAQ,WAAW,KAAK,QAAQ,KAAK;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAA4B,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAkB,oCAAoC,CAAC,CAAC;AAAA,EAChM;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,QAAQ,CAAC;AAAA,IACtB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAQH,SAAS,UAAU,OAAO;AACxB,SAAO,OAAO,UAAU,WAAW,QAAQ,SAAS,OAAO,EAAE;AAC/D;AAOA,SAAS,QAAQ,OAAO;AACtB,SAAO,OAAO,UAAU,WAAW,QAAQ,WAAW,KAAK;AAC7D;AAOA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,aAAa;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA;AAAA;AAAA,EAEA,YAAY,SAAS;AACnB,QAAI,KAAK,aAAa,SAAS;AAC7B,YAAM,QAAQ,KAAK,eAAe,QAAQ,KAAK,SAAS,EAAE,YAAY;AACtE,WAAK,WAAW,KAAK,QAAQ,KAAK;AAClC,WAAK,aAAa,KAAK,WAAW,KAAK,gBAAgB,KAAK,IAAI;AAChE,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,SAAS,SAAS;AAChB,WAAO,KAAK,WAAW,OAAO;AAAA,EAChC;AAAA;AAAA,EAEA,0BAA0B,IAAI;AAC5B,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,OAAO;AACb,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,UAAU,CAAI,oBAAoB;AAAA,EACpC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,gBAAgB;AAAA,EACpB,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,YAAY;AAAA,EAC1C,OAAO;AACT;AAsBA,IAAM,eAAN,MAAM,sBAAqB,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpD;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,iBAAiB,WAAS,QAAQ,KAAK;AAAA;AAAA,EAEvC,kBAAkB,SAAO,aAAa,GAAG;AAAA,EACzC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,QAAQ,UAAU,OAAO,IAAI,mBAAmB,EAAE,GAAG,CAAC,SAAS,QAAQ,UAAU,OAAO,IAAI,eAAe,EAAE,GAAG,CAAC,SAAS,QAAQ,UAAU,OAAO,IAAI,WAAW,EAAE,CAAC;AAAA,IAC3L,UAAU;AAAA,IACV,cAAc,SAAS,0BAA0B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,OAAO,IAAI,WAAW,IAAI,MAAM,IAAI;AAAA,MACrD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,IACP;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,aAAa,CAAC,GAAM,0BAA0B;AAAA,EAClF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,aAAa;AAAA,MACzB,MAAM;AAAA,QACJ,cAAc;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,gBAAgB;AAAA,EACpB,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,YAAY;AAAA,EAC1C,OAAO;AACT;AAsBA,IAAM,eAAN,MAAM,sBAAqB,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpD;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,iBAAiB,WAAS,QAAQ,KAAK;AAAA;AAAA,EAEvC,kBAAkB,SAAO,aAAa,GAAG;AAAA,EACzC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,qBAAqB,mBAAmB;AACtD,cAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,IAC9I;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,QAAQ,UAAU,OAAO,IAAI,mBAAmB,EAAE,GAAG,CAAC,SAAS,QAAQ,UAAU,OAAO,IAAI,eAAe,EAAE,GAAG,CAAC,SAAS,QAAQ,UAAU,OAAO,IAAI,WAAW,EAAE,CAAC;AAAA,IAC3L,UAAU;AAAA,IACV,cAAc,SAAS,0BAA0B,IAAI,KAAK;AACxD,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,OAAO,IAAI,WAAW,IAAI,MAAM,IAAI;AAAA,MACrD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,KAAK;AAAA,IACP;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,aAAa,CAAC,GAAM,0BAA0B;AAAA,EAClF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,aAAa;AAAA,MACzB,MAAM;AAAA,QACJ,cAAc;AAAA,MAChB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,KAAK,CAAC;AAAA,MACJ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,qBAAqB;AAAA,EACzB,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,iBAAiB;AAAA,EAC/C,OAAO;AACT;AAKA,IAAM,8BAA8B;AAAA,EAClC,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,yBAAyB;AAAA,EACvD,OAAO;AACT;AAoBA,IAAM,oBAAN,MAAM,2BAA0B,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzD;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,iBAAiB;AAAA;AAAA,EAEjB,kBAAkB,WAAS;AAAA;AAAA,EAE3B,QAAQ,OAAO;AACb,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,0BAA0B,mBAAmB;AAC3D,cAAQ,mCAAmC,iCAAoC,sBAAsB,kBAAiB,IAAI,qBAAqB,kBAAiB;AAAA,IAClK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,YAAY,IAAI,mBAAmB,IAAI,GAAG,QAAQ,UAAU,GAAG,CAAC,IAAI,YAAY,IAAI,eAAe,IAAI,GAAG,QAAQ,UAAU,GAAG,CAAC,IAAI,YAAY,IAAI,WAAW,IAAI,GAAG,QAAQ,UAAU,CAAC;AAAA,IAC1M,UAAU;AAAA,IACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,YAAY,IAAI,WAAW,KAAK,IAAI;AAAA,MACrD;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,kBAAkB,CAAC,GAAM,0BAA0B;AAAA,EACvF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,kBAAkB;AAAA,MAC9B,MAAM;AAAA,QACJ,mBAAmB;AAAA,MACrB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAsBH,IAAM,4BAAN,MAAM,mCAAkC,kBAAkB;AAAA;AAAA,EAExD,kBAAkB,WAAS;AAAA,EAC3B,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,kCAAkC,mBAAmB;AACnE,cAAQ,2CAA2C,yCAA4C,sBAAsB,0BAAyB,IAAI,qBAAqB,0BAAyB;AAAA,IAClM;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,QAAQ,YAAY,YAAY,IAAI,mBAAmB,EAAE,GAAG,CAAC,SAAS,QAAQ,YAAY,YAAY,IAAI,eAAe,EAAE,GAAG,CAAC,SAAS,QAAQ,YAAY,YAAY,IAAI,WAAW,EAAE,CAAC;AAAA,IAChN,UAAU;AAAA,IACV,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,YAAY,IAAI,WAAW,KAAK,IAAI;AAAA,MACrD;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,2BAA2B,CAAC,GAAM,0BAA0B;AAAA,EAChG,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,2BAA2B;AAAA,MACvC,MAAM;AAAA,QACJ,mBAAmB;AAAA,MACrB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,kBAAkB;AAAA,EACtB,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,cAAc;AAAA,EAC5C,OAAO;AACT;AA4BA,IAAM,iBAAN,MAAM,wBAAuB,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtD;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,iBAAiB;AAAA;AAAA,EAEjB,kBAAkB,WAAS;AAAA;AAAA,EAE3B,QAAQ,OAAO;AACb,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,uBAAuB,mBAAmB;AACxD,cAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,qBAAqB,eAAc;AAAA,IACtJ;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,SAAS,IAAI,mBAAmB,EAAE,GAAG,CAAC,IAAI,SAAS,IAAI,eAAe,EAAE,GAAG,CAAC,IAAI,SAAS,IAAI,WAAW,EAAE,CAAC;AAAA,IAC5H,QAAQ;AAAA,MACN,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,eAAe,CAAC,GAAM,0BAA0B;AAAA,EACpF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,eAAe;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,kBAAkB;AAAA,EAChD,OAAO;AACT;AAsBA,IAAM,qBAAN,MAAM,4BAA2B,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1D;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,iBAAiB,WAAS,UAAU,KAAK;AAAA;AAAA,EAEzC,kBAAkB,eAAa,mBAAmB,SAAS;AAAA,EAC3D,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,IAAI,mBAAmB,EAAE,GAAG,CAAC,IAAI,aAAa,IAAI,eAAe,EAAE,GAAG,CAAC,IAAI,aAAa,IAAI,WAAW,EAAE,CAAC;AAAA,IACxI,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,aAAa,IAAI,WAAW,IAAI,YAAY,IAAI;AAAA,MACjE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW;AAAA,IACb;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,oBAAoB,CAAC,GAAM,0BAA0B;AAAA,EACzF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,oBAAoB;AAAA,MAChC,MAAM;AAAA,QACJ,oBAAoB;AAAA,MACtB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,kBAAkB;AAAA,EAChD,OAAO;AACT;AAsBA,IAAM,qBAAN,MAAM,4BAA2B,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1D;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,iBAAiB,WAAS,UAAU,KAAK;AAAA;AAAA,EAEzC,kBAAkB,eAAa,mBAAmB,SAAS;AAAA,EAC3D,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,aAAa,IAAI,mBAAmB,EAAE,GAAG,CAAC,IAAI,aAAa,IAAI,eAAe,EAAE,GAAG,CAAC,IAAI,aAAa,IAAI,WAAW,EAAE,CAAC;AAAA,IACxI,UAAU;AAAA,IACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,aAAa,IAAI,WAAW,IAAI,YAAY,IAAI;AAAA,MACjE;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,WAAW;AAAA,IACb;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,oBAAoB,CAAC,GAAM,0BAA0B;AAAA,EACzF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,oBAAoB;AAAA,MAChC,MAAM;AAAA,QACJ,oBAAoB;AAAA,MACtB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,oBAAoB;AAAA,EACxB,SAAS;AAAA,EACT,aAAa,WAAW,MAAM,gBAAgB;AAAA,EAC9C,OAAO;AACT;AAwBA,IAAM,mBAAN,MAAM,0BAAyB,2BAA2B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxD;AAAA;AAAA;AAAA,EAEA,YAAY;AAAA;AAAA,EAEZ,iBAAiB,WAAS;AAAA;AAAA,EAE1B,kBAAkB,WAAS,iBAAiB,KAAK;AAAA,EACjD,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,yBAAyB,mBAAmB;AAC1D,cAAQ,kCAAkC,gCAAmC,sBAAsB,iBAAgB,IAAI,qBAAqB,iBAAgB;AAAA,IAC9J;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,WAAW,IAAI,mBAAmB,EAAE,GAAG,CAAC,IAAI,WAAW,IAAI,eAAe,EAAE,GAAG,CAAC,IAAI,WAAW,IAAI,WAAW,EAAE,CAAC;AAAA,IAClI,UAAU;AAAA,IACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,UAAI,KAAK,GAAG;AACV,QAAG,YAAY,WAAW,IAAI,WAAW,IAAI,UAAU,IAAI;AAAA,MAC7D;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,YAAY;AAAA,IACZ,UAAU,CAAI,mBAAmB,CAAC,iBAAiB,CAAC,GAAM,0BAA0B;AAAA,EACtF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC,iBAAiB;AAAA,MAC7B,MAAM;AAAA,QACJ,kBAAkB;AAAA,MACpB;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,yBAAyB,CAAC,eAAe,gBAAgB,yBAAyB,sBAAsB,qBAAqB,oBAAoB,8BAA8B,4BAA4B,oCAAoC,2BAA2B,iBAAiB,sBAAsB,mBAAmB,oBAAoB,oBAAoB,kBAAkB,2BAA2B,gBAAgB,cAAc,YAAY;AACzc,IAAM,6BAA6B,CAAC,SAAS,cAAc,MAAM;AACjE,IAAM,6BAA6B,CAAC,sBAAsB,oBAAoB,iBAAiB,eAAe,aAAa;AAI3H,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,OAAO,OAAO,SAAS,mCAAmC,mBAAmB;AAC3E,WAAO,KAAK,qBAAqB,6BAA4B;AAAA,EAC/D;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,eAAe,gBAAgB,yBAAyB,sBAAsB,qBAAqB,oBAAoB,8BAA8B,4BAA4B,oCAAoC,2BAA2B,iBAAiB,sBAAsB,mBAAmB,oBAAoB,oBAAoB,kBAAkB,2BAA2B,gBAAgB,cAAc,YAAY;AAAA,IACxb,SAAS,CAAC,eAAe,gBAAgB,yBAAyB,sBAAsB,qBAAqB,oBAAoB,8BAA8B,4BAA4B,oCAAoC,2BAA2B,iBAAiB,sBAAsB,mBAAmB,oBAAoB,oBAAoB,kBAAkB,2BAA2B,gBAAgB,cAAc,YAAY;AAAA,EACrb,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc;AAAA,MACd,SAAS;AAAA,IACX,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAqEH,IAAM,YAAN,cAAwB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EActC,YAAY,UAAU,iBAAiB,gBAAgB;AACrD,UAAM,eAAe,eAAe,GAAG,oBAAoB,gBAAgB,eAAe,CAAC;AAC3F,SAAK,WAAW;AAChB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB,eAAe;AACvC,SAAK,eAAe;AACpB,SAAK,uBAAuB;AAAA,MAC1B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKV,WAAW,CAAC,CAAC,KAAK;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,GAAG,OAAO;AACR,WAAO,KAAK,SAAS,KAAK,aAAa,KAAK,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,KAAK,SAAS,UAAU,CAAC,GAAG;AAC1B,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,cAAQ,QAAQ,UAAQ;AACtB,aAAK,SAAS,KAAK,IAAI;AACvB,aAAK,iBAAiB,IAAI;AAAA,MAC5B,CAAC;AAAA,IACH,OAAO;AACL,WAAK,SAAS,KAAK,OAAO;AAC1B,WAAK,iBAAiB,OAAO;AAAA,IAC/B;AACA,SAAK,uBAAuB;AAAA,MAC1B,WAAW,QAAQ;AAAA,IACrB,CAAC;AACD,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,OAAO,OAAO,SAAS,UAAU,CAAC,GAAG;AACnC,SAAK,SAAS,OAAO,OAAO,GAAG,OAAO;AACtC,SAAK,iBAAiB,OAAO;AAC7B,SAAK,uBAAuB;AAAA,MAC1B,WAAW,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,SAAS,OAAO,UAAU,CAAC,GAAG;AAE5B,QAAI,gBAAgB,KAAK,aAAa,KAAK;AAC3C,QAAI,gBAAgB,EAAG,iBAAgB;AACvC,QAAI,KAAK,SAAS,aAAa,EAAG,MAAK,SAAS,aAAa,EAAE,4BAA4B,MAAM;AAAA,IAAC,CAAC;AACnG,SAAK,SAAS,OAAO,eAAe,CAAC;AACrC,SAAK,uBAAuB;AAAA,MAC1B,WAAW,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,WAAW,OAAO,SAAS,UAAU,CAAC,GAAG;AAEvC,QAAI,gBAAgB,KAAK,aAAa,KAAK;AAC3C,QAAI,gBAAgB,EAAG,iBAAgB;AACvC,QAAI,KAAK,SAAS,aAAa,EAAG,MAAK,SAAS,aAAa,EAAE,4BAA4B,MAAM;AAAA,IAAC,CAAC;AACnG,SAAK,SAAS,OAAO,eAAe,CAAC;AACrC,QAAI,SAAS;AACX,WAAK,SAAS,OAAO,eAAe,GAAG,OAAO;AAC9C,WAAK,iBAAiB,OAAO;AAAA,IAC/B;AACA,SAAK,uBAAuB;AAAA,MAC1B,WAAW,QAAQ;AAAA,IACrB,CAAC;AACD,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACX,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoCA,SAAS,OAAO,UAAU,CAAC,GAAG;AAC5B,2BAAuB,MAAM,OAAO,KAAK;AACzC,UAAM,QAAQ,CAAC,UAAU,UAAU;AACjC,2BAAqB,MAAM,OAAO,KAAK;AACvC,WAAK,GAAG,KAAK,EAAE,SAAS,UAAU;AAAA,QAChC,UAAU;AAAA,QACV,WAAW,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AACD,SAAK,uBAAuB,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiCA,WAAW,OAAO,UAAU,CAAC,GAAG;AAK9B,QAAI,SAAS,KAAwC;AACrD,UAAM,QAAQ,CAAC,UAAU,UAAU;AACjC,UAAI,KAAK,GAAG,KAAK,GAAG;AAClB,aAAK,GAAG,KAAK,EAAE,WAAW,UAAU;AAAA,UAClC,UAAU;AAAA,UACV,WAAW,QAAQ;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,SAAK,uBAAuB,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+CA,MAAM,QAAQ,CAAC,GAAG,UAAU,CAAC,GAAG;AAC9B,SAAK,cAAc,CAAC,SAAS,UAAU;AACrC,cAAQ,MAAM,MAAM,KAAK,GAAG;AAAA,QAC1B,UAAU;AAAA,QACV,WAAW,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH,CAAC;AACD,SAAK,gBAAgB,SAAS,IAAI;AAClC,SAAK,eAAe,SAAS,IAAI;AACjC,SAAK,uBAAuB,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,WAAO,KAAK,SAAS,IAAI,aAAW,QAAQ,YAAY,CAAC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqCA,MAAM,UAAU,CAAC,GAAG;AAClB,QAAI,KAAK,SAAS,SAAS,EAAG;AAC9B,SAAK,cAAc,aAAW,QAAQ,4BAA4B,MAAM;AAAA,IAAC,CAAC,CAAC;AAC3E,SAAK,SAAS,OAAO,CAAC;AACtB,SAAK,uBAAuB;AAAA,MAC1B,WAAW,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,OAAO;AAClB,WAAO,QAAQ,IAAI,QAAQ,KAAK,SAAS;AAAA,EAC3C;AAAA;AAAA,EAEA,uBAAuB;AACrB,QAAI,iBAAiB,KAAK,SAAS,OAAO,CAAC,SAAS,UAAU;AAC5D,aAAO,MAAM,qBAAqB,IAAI,OAAO;AAAA,IAC/C,GAAG,KAAK;AACR,QAAI,eAAgB,MAAK,uBAAuB;AAAA,MAC9C,UAAU;AAAA,IACZ,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,cAAc,IAAI;AAChB,SAAK,SAAS,QAAQ,CAAC,SAAS,UAAU;AACxC,SAAG,SAAS,KAAK;AAAA,IACnB,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,eAAe;AACb,SAAK,QAAQ,KAAK,SAAS,OAAO,aAAW,QAAQ,WAAW,KAAK,QAAQ,EAAE,IAAI,aAAW,QAAQ,KAAK;AAAA,EAC7G;AAAA;AAAA,EAEA,aAAa,WAAW;AACtB,WAAO,KAAK,SAAS,KAAK,aAAW,QAAQ,WAAW,UAAU,OAAO,CAAC;AAAA,EAC5E;AAAA;AAAA,EAEA,iBAAiB;AACf,SAAK,cAAc,aAAW,KAAK,iBAAiB,OAAO,CAAC;AAAA,EAC9D;AAAA;AAAA,EAEA,uBAAuB;AACrB,eAAW,WAAW,KAAK,UAAU;AACnC,UAAI,QAAQ,QAAS,QAAO;AAAA,IAC9B;AACA,WAAO,KAAK,SAAS,SAAS,KAAK,KAAK;AAAA,EAC1C;AAAA,EACA,iBAAiB,SAAS;AACxB,YAAQ,UAAU,IAAI;AACtB,YAAQ,4BAA4B,KAAK,mBAAmB;AAAA,EAC9D;AAAA;AAAA,EAEA,MAAM,MAAM;AACV,WAAO,KAAK,GAAG,IAAI,KAAK;AAAA,EAC1B;AACF;AACA,IAAM,mBAAmB;AAOzB,IAAM,cAAc,aAAW,mBAAmB;AAClD,SAAS,yBAAyB,SAAS;AACzC,SAAO,CAAC,CAAC,YAAY,QAAQ,oBAAoB,UAAa,QAAQ,eAAe,UAAa,QAAQ,aAAa;AACzH;AAaA,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2CjB,IAAI,cAAc;AAChB,UAAM,OAAO,IAAI,aAAY;AAC7B,SAAK,iBAAiB;AACtB,WAAO;AAAA,EACT;AAAA,EACA,MAAM,UAAU,UAAU,MAAM;AAC9B,UAAM,kBAAkB,KAAK,gBAAgB,QAAQ;AACrD,QAAI,aAAa,CAAC;AAClB,QAAI,yBAAyB,OAAO,GAAG;AAErC,mBAAa;AAAA,IACf,WAAW,YAAY,MAAM;AAE3B,iBAAW,aAAa,QAAQ;AAChC,iBAAW,kBAAkB,QAAQ;AAAA,IACvC;AACA,WAAO,IAAI,UAAU,iBAAiB,UAAU;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,OAAO,UAAU,UAAU,MAAM;AAC/B,UAAM,kBAAkB,KAAK,gBAAgB,QAAQ;AAErD,WAAO,IAAI,WAAW,iBAAiB,OAAO;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BA,QAAQ,WAAW,iBAAiB,gBAAgB;AAClD,QAAI,aAAa,CAAC;AAClB,QAAI,CAAC,KAAK,gBAAgB;AACxB,aAAO,IAAI,YAAY,WAAW,iBAAiB,cAAc;AAAA,IACnE;AACA,QAAI,yBAAyB,eAAe,GAAG;AAE7C,mBAAa;AAAA,IACf,OAAO;AAEL,iBAAW,aAAa;AACxB,iBAAW,kBAAkB;AAAA,IAC/B;AACA,WAAO,IAAI,YAAY,WAAW,iCAC7B,aAD6B;AAAA,MAEhC,aAAa;AAAA,IACf,EAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,MAAM,UAAU,iBAAiB,gBAAgB;AAC/C,UAAM,kBAAkB,SAAS,IAAI,OAAK,KAAK,eAAe,CAAC,CAAC;AAEhE,WAAO,IAAI,UAAU,iBAAiB,iBAAiB,cAAc;AAAA,EACvE;AAAA;AAAA,EAEA,gBAAgB,UAAU;AACxB,UAAM,kBAAkB,CAAC;AACzB,WAAO,KAAK,QAAQ,EAAE,QAAQ,iBAAe;AAC3C,sBAAgB,WAAW,IAAI,KAAK,eAAe,SAAS,WAAW,CAAC;AAAA,IAC1E,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,eAAe,UAAU;AACvB,QAAI,oBAAoB,aAAa;AACnC,aAAO;AAAA,IACT,WAAW,oBAAoB,iBAAiB;AAE9C,aAAO;AAAA,IACT,WAAW,MAAM,QAAQ,QAAQ,GAAG;AAElC,YAAM,QAAQ,SAAS,CAAC;AACxB,YAAM,YAAY,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI;AACtD,YAAM,iBAAiB,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI;AAC3D,aAAO,KAAK,QAAQ,OAAO,WAAW,cAAc;AAAA,IACtD,OAAO;AAEL,aAAO,KAAK,QAAQ,QAAQ;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,aAAY;AAAA,IACrB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAQH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO,OAAO,SAAS,+BAA+B,mBAAmB;AACvE,WAAO,KAAK,qBAAqB,yBAAwB;AAAA,EAC3D;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,OAAO,MAAM,OAAO,WAAW,EAAE,aAAa;AAAA,IACvD,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,MACZ,YAAY,MAAM,OAAO,WAAW,EAAE;AAAA,IACxC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,IAAM,qBAAN,MAAM,4BAA2B,YAAY;AAAA,EAC3C,MAAM,gBAAgB,UAAU,MAAM;AACpC,WAAO,MAAM,MAAM,gBAAgB,OAAO;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,WAAW,iBAAiB,gBAAgB;AAClD,WAAO,MAAM,QAAQ,WAAW,iBAAiB,cAAc;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,gBAAgB,iBAAiB,gBAAgB;AACrD,WAAO,MAAM,MAAM,gBAAgB,iBAAiB,cAAc;AAAA,EACpE;AAAA,EACA,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,2BAA2B,mBAAmB;AAC5D,cAAQ,oCAAoC,kCAAqC,sBAAsB,mBAAkB,IAAI,qBAAqB,mBAAkB;AAAA,IACtK;AAAA,EACF,GAAG;AAAA,EACH,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,oBAAmB;AAAA,IAC5B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAM,UAAU,IAAI,QAAQ,QAAQ;AAWpC,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAShB,OAAO,WAAW,MAAM;AACtB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU,KAAK,wBAAwB;AAAA,MACzC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,SAAS,cAAc,MAAM;AAAA,IAC5C,SAAS,CAAC,4BAA4B,SAAS,cAAc,MAAM;AAAA,EACrE,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,0BAA0B;AAAA,EACtC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc;AAAA,MACd,SAAS,CAAC,4BAA4B,0BAA0B;AAAA,IAClE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAUH,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWxB,OAAO,WAAW,MAAM;AACtB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU,KAAK,gCAAgC;AAAA,MACjD,GAAG;AAAA,QACD,SAAS;AAAA,QACT,UAAU,KAAK,wBAAwB;AAAA,MACzC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,cAAc,CAAC,sBAAsB,oBAAoB,iBAAiB,eAAe,aAAa;AAAA,IACtG,SAAS,CAAC,4BAA4B,sBAAsB,oBAAoB,iBAAiB,eAAe,aAAa;AAAA,EAC/H,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,0BAA0B;AAAA,EACtC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC,0BAA0B;AAAA,MACzC,SAAS,CAAC,4BAA4B,0BAA0B;AAAA,IAClE,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["isFormGroup", "acc", "FormControl"]}